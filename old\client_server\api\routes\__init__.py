from typing import Literal
from fastapi import FastAPI, APIRouter

# -----------------------------------------------------------------------------

# Store routes in a list instead of using function attributes
ROUTES = []


def route(method: Literal["GET", "POST", "PUT", "DELETE"], path: str):
    def decorator(handler):
        ROUTES.append((method, path, handler))
        return handler

    return decorator


# -----------------------------------------------------------------------------

from .basic import *
from .chat import *
from .codelens import *
from .dependencies import *
from .delete_kb import *
from .file_actions import *
from .hardware import *
from .search import *
from .swagger import *
from .list_swagger_endpoints import *
from .list_kbs import *
from .register_meta import *
from .report_to_cloud import *
from .watchdog_file_update import *


def setup_routes(app: FastAPI):
    # Create a router for our routes
    router = APIRouter()

    # Register routes with the FastAPI router
    for method, path, handler in ROUTES:
        if method == "GET":
            router.get(path)(handler)
        elif method == "POST":
            router.post(path)(handler)
        elif method == "PUT":
            router.put(path)(handler)
        elif method == "DELETE":
            router.delete(path)(handler)

    # Include the router in the app
    app.include_router(router)


# -----------------------------------------------------------------------------
