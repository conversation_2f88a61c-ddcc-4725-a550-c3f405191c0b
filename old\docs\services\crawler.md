# Crawler Service

The Crawler service is responsible for scraping web content. It's designed to be able to handle JavaScript-heavy sites by using a headless browser.

## Design

The service follows the interface-implementation pattern.

- **Interface (`ICrawlerBackend`)**: Defines the contract for any crawler implementation. It's located in `client_server/services/crawler/__init__.py`.
- **Implementation (`PlaywrightCrawlerBackend`)**: The concrete implementation of the crawler, which uses `playwright` to render and scrape web pages.

### Interface: `ICrawlerBackend`

The `ICrawlerBackend` interface has one abstract method:

- `scrape(url: str) -> Coroutine[dict[str, str], None, None]`: This method takes a starting URL and asynchronously returns a dictionary where keys are the scraped URLs and values are the content of those pages in Markdown format.

### Implementation: `PlaywrightCrawlerBackend`

This is the primary implementation of the `ICrawlerBackend`. It uses `playwright` with a Chromium browser to perform the crawling.

**Key Features:**

- **JavaScript Rendering**: Can handle modern, dynamic websites.
- **Parallel Crawling**: Uses `asyncio` and a `ThreadPoolExecutor` to scrape multiple pages concurrently, improving speed.
- **URL Collection Phase**: It first collects all unique URLs within the same domain up to a specified limit (`max_pages`) before starting to scrape content. This is done to avoid getting stuck in crawling loops and to manage the scope of the crawl.
- **Content Scraping Phase**: After collecting URLs, it scrapes the content of each page.
- **Content Cleaning**: It converts the HTML content to Markdown and removes unnecessary elements like `<footer>`, `<canvas>`, `<img>`, etc., to extract clean textual data.
- **Resilience**: It includes retry logic for scraping individual URLs.
- **Configurable**: You can configure `max_pages` and `max_depth` for a crawl.

**How it works:**

1.  **Initialization**: `PlaywrightCrawlerBackend(max_pages, max_depth)`
2.  **Scraping**: The `scrape` method is called with a URL.
3.  **Dependency Installation**: It ensures Playwright's browser dependencies are installed.
4.  **URL Collection (`_collect_urls`)**:
    - It starts from the given URL and explores links.
    - It uses a queue for URLs to visit and a set to track visited URLs.
    - It only follows links that are on the same domain or a subdomain of the starting URL.
    - It uses an `asyncio.Semaphore` to limit the number of concurrent pages being processed.
5.  **Content Scraping (`_scrape_urls`)**:
    - It iterates through the collected URLs.
    - For each URL, it launches a `playwright` page, waits for the network to be idle, and gets the content.
    - It uses `BeautifulSoup` to parse the HTML and `markdownify` to convert it to Markdown.
    - It returns a dictionary of `url: content`.

There is no `utils.py` for a builder pattern in this service. It must be instantiated directly.
