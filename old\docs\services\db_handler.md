# Database Handler (db_handler.py)

The `db_handler.py` service provides a lightweight, MongoDB-like interface for a local database, but it's built entirely on top of Python's built-in `sqlite3` module. It is designed to offer the convenience of a document-oriented database (like MongoDB) without requiring a separate database server.

As per the project's design guidelines, this service is a single file because it provides one specific, concrete implementation and does not require the abstraction of an interface.

## Design

The handler uses SQLite as its backend and leverages SQLite's `json1` extension to store and query JSON documents directly in a relational database table. This approach simulates a NoSQL document store.

The service consists of two main classes:

- **`Database`**: This class represents the connection to the SQLite database file (`records.db`). It acts as a factory for `Collection` objects.
- **`Collection`**: This class represents a single table within the SQLite database, but it exposes methods that mimic the syntax of a MongoDB collection.

## Core Components

### `Database` Class

- **Initialization**: When a `Database` object is created, it connects to a `records.db` file located in the path provided by `PathSelector.get_qdrant_db_path()`.
- **Collection Access**: It uses the `__getitem__` magic method to provide dictionary-style access to collections. For example, `db['users']` will create and return a `Collection` object for the "users" table.
- **JSON1 Extension**: It attempts to enable the `json1` extension, which is essential for its operation.

### `Collection` Class

This is where the core logic resides. An instance of this class maps to a single database table.

- **CRUD Operations**: It implements a rich set of methods that are familiar to anyone who has used MongoDB or similar document databases:
  - `insert_one(doc)`
  - `insert_many(docs)`
  - `find(query, projection)`
  - `find_one(query, projection)`
  - `update_one(query, update)`
  - `update_many(query, update)`
  - `delete_one(query)`
  - `delete_many(query)`
  - `count_documents(query)`
- **Query Translation**: It can parse MongoDB-style query dictionaries and translate them into SQL `WHERE` clauses.
  - It handles basic equality checks (`{'name': 'John'}`).
  - It supports operators like `$gt`, `$lt`, `$in`, `$ne`, `$exists` by converting them into their SQL equivalents (`>`, `<`, `IN`, `!=`, `IS NOT NULL`).
- **Update Operators**: It also implements update operations like `$set`, `$inc`, and `$unset`, applying the changes to the JSON document before writing it back to the database.
- **Dot Notation**: It supports dot notation for querying and updating nested fields within a JSON document (e.g., `'user.address.city'`).

## Workflow

1.  **Get a Database instance**: `db = Database()`
2.  **Get a Collection**: `my_collection = db['my_data']`
3.  **Insert Data**: `my_collection.insert_one({'user': 'test', 'score': 100})`
4.  **Query Data**: `results = my_collection.find({'score': {'$gt': 50}})`
5.  **Iterate Results**:
    ```python
    for doc in results:
        print(doc)
    ```

This service is used by other parts of the application (like the `DependencyRegistry`) to persist state and records locally without introducing a heavy external dependency like a full database server.
