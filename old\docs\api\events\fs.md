# File System Events (`fs.py`)

The `fs.py` module provides handlers for interacting with the local file system. These events are used by the client to get information about the structure of the user's workspace.

## Overview

These handlers allow the client to request lists of files and folders from a given path. They are designed to be efficient by skipping common unnecessary directories and using heuristics to identify different file types.

## Event Handlers

- `handle_get_folder_paths_recursive_event(sio, sid, data)`:

  - **Payload**: `{ "path": "path/to/directory" }`
  - **Response**: A list of objects, where each object represents a subdirectory and has `path` and `name` properties.
  - This event recursively scans a directory and returns a flat list of all its subdirectories.
  - It intelligently ignores common folders like `node_modules`, `.git`, `__pycache__`, etc., based on the `DEFAULT_EXCLUDED_DIRS` set.

### Event Flow

1.  The client sends a `get_folder_paths_recursive` event with a `path`.
2.  The handler recursively walks the directory structure using `os.walk`.
3.  In each iteration, it filters the list of subdirectories to remove any that are in the `DEFAULT_EXCLUDED_DIRS` set, preventing `os.walk` from traversing into them.
4.  It compiles a flat list of all non-excluded subdirectories.
5.  Finally, it sends this list back to the client in a `get_folder_paths_recursive:response` event.

- `handle_get_file_paths_recursive_event(sio, sid, data)`:
  - **Payload**: `{ "path": "path/to/directory" }`
  - **Response**: A complex object containing a tree structure of files and folders, a flat list of all file paths, a list of ignored (non-text) files, and the workspace path.
  - This event recursively scans a directory and builds a hierarchical tree structure representing the files and folders.
  - It uses the `is_text_file` helper to differentiate between text and binary files, excluding the latter from some processing.
  - The response is comprehensive, providing both the tree (`value`) for UI rendering and flat lists (`allFiles`, `ignoredFiles`) for other uses.

### Event Flow

1.  The client sends a `get_file_paths_recursive` event with a `path`.
2.  **First Pass**: The handler performs an initial `os.walk` to efficiently collect all file paths. It skips directories in `DEFAULT_EXCLUDED_DIRS` and uses `is_text_file` to build a list of non-text files to be ignored.
3.  **Second Pass**: It performs another `os.walk`, again skipping excluded directories. This time, it builds a hierarchical tree structure of all files and folders.
4.  **Sorting**: The nodes in the tree are sorted alphabetically, with folders appearing before files.
5.  **Response**: The handler sends a `get_file_paths_recursive:response` event containing the sorted tree, the list of all files, the list of ignored files, and the original workspace path.

## Helper Functions

- `is_text_file(filepath)`:

  - A utility function that determines if a file is likely a text file.
  - It checks the file extension against a list of known text extensions (`DEFAULT_TEXT_EXTENSIONS`).
  - It also checks the file's MIME type.
  - As a fallback, it reads the first few bytes of the file to check for null bytes, which are a common indicator of a binary file.

- `should_skip_dir(dirpath)`:
  - A utility that uses various heuristics to decide if a directory should be skipped during a scan. This goes beyond the simple name check in `DEFAULT_EXCLUDED_DIRS`.
  - For example, it can identify Python virtual environments, conda environments, and other project-specific folders that are not useful to scan.
