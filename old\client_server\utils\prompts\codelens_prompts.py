"""
CodeLens Prompts for AI-powered code operations

This module contains message templates for different CodeLens operations
following the established pattern of system and user prompts.
"""

from typing import Dict, List, Any
from .prompt_loader import load_prompt


class CodeLensPrompts:
    """Message templates for CodeLens operations"""
    
    @staticmethod
    def get_inline_edit_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for inline code editing

        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to be edited
            instructions: User instructions for the edit
        """
        system_prompt = load_prompt("codelens", "inline_edit")

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Target Code to Edit:
```{language}
{target_code}
```

Instructions: {instructions}

Please provide the edited code that follows the instructions while maintaining compatibility with the context."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_debug_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for debugging assistance

        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code that needs debugging
            instructions: Specific debugging request or error description
        """
        system_prompt = load_prompt("codelens", "debug")

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Debug:
```{language}
{target_code}
```

Debug Request: {instructions}

Please analyze the code and provide debugging insights, explanations, and fixes."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_test_generation_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for test generation

        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to generate tests for
            instructions: Specific testing requirements or framework preferences
        """
        system_prompt = load_prompt("codelens", "test_generation")

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Test:
```{language}
{target_code}
```

Testing Requirements: {instructions}

Please generate comprehensive tests for the given code, including setup, test cases, and assertions."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_optimization_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for code optimization

        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to optimize
            instructions: Specific optimization goals or constraints
        """
        system_prompt = load_prompt("codelens", "optimization")

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Optimize:
```{language}
{target_code}
```

Optimization Goals: {instructions}

Please analyze and optimize the code for better performance, explaining the improvements made."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_explanation_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for code explanation

        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to explain
            instructions: Specific aspects to focus on or level of detail required
        """
        system_prompt = load_prompt("codelens", "explanation")

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Explain:
```{language}
{target_code}
```

Explanation Focus: {instructions}

Please provide a detailed explanation of how this code works, its purpose, and key implementation details."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]


# Convenience function to get messages by operation type
def get_codelens_messages(operation: str, language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
    """
    Get CodeLens messages for a specific operation type
    
    Args:
        operation: Type of operation ('edit', 'debug', 'test', 'optimize', 'explain')
        language: Programming language
        context_code: Surrounding code context
        target_code: Target code for the operation
        instructions: User instructions or requirements
    
    Returns:
        List of message dictionaries for the LLM
    
    Raises:
        ValueError: If operation type is not supported
    """
    operation_map = {
        'edit': CodeLensPrompts.get_inline_edit_messages,
        'debug': CodeLensPrompts.get_debug_messages,
        'test': CodeLensPrompts.get_test_generation_messages,
        'optimize': CodeLensPrompts.get_optimization_messages,
        'explain': CodeLensPrompts.get_explanation_messages
    }
    
    if operation not in operation_map:
        raise ValueError(f"Unsupported operation: {operation}. Supported operations: {list(operation_map.keys())}")
    
    return operation_map[operation](language, context_code, target_code, instructions)
