# Services

This directory contains documentation for the backend services.

## Overview

The services in this application are located in the `client_server/services` directory. They follow a few key design patterns as outlined in the main project `overview.md`:

- **Interface-Based Design**: Most services with the potential for multiple strategies (e.g., local vs. cloud, different libraries) are defined by an abstract interface (e.g., `IEmbeddingBackend`). This allows different implementations to be used interchangeably.
- **Builder/Factory Pattern**: For services with multiple backends, a `Builder` class (e.g., `InferenceBuilder`) is used to intelligently select and instantiate the correct implementation based on the environment, such as platform architecture or internet connectivity.
- **Standalone Utilities**: Services that provide a single, concrete functionality (e.g., `db_handler`) are implemented as a single file without an interface.

## Documented Services

Here is a list of the documented services. Please refer to the individual markdown files for detailed information on each service's design, components, and workflow.

- [**Crawler Service (`crawler.md`)**](./crawler.md): Responsible for scraping web content from URLs, with capabilities to handle JavaScript-heavy sites using Playwright.
- [**Dependencies Service (`dependencies.md`)**](./dependencies.md): Manages the installation, status, and lifecycle of external dependencies like Ollama and InferX.
- [**Embeddings Service (`embeddings.md`)**](./embeddings.md): Provides a unified interface for generating text embeddings using different backends (Cloud, Ollama, InferX).
- [**Inference Service (`inference.md`)**](./inference.md): A standardized interface for running text generation with LLMs, supporting different backends for local inference.
- [**Swagger Service (`swagger.md`)**](./swagger.md): A utility to parse, resolve, and deconstruct OpenAPI specification files into individual endpoint definitions.
- [**Tokenization Service (`tokenization.md`)**](./tokenization.md): A service for tokenizing text, which is essential for working with LLMs. It supports multiple tokenization backends.
- [**Database Handler (`db_handler.md`)**](./db_handler.md): A lightweight, MongoDB-like interface built on top of SQLite for simple local data persistence.
- [**Qdrant Handler (`qdrant_handler.md`)**](./qdrant_handler.md): A centralized handler for interacting with the Qdrant vector database, used for storing and searching embeddings.
