"""
File Action Prompts for AI-powered file analysis and automated improvements

This module contains message templates for different file action operations
including code review, documentation generation, security scanning, fix suggestions,
and automated fix application following the established CodeLens pattern.
"""

from typing import Dict, List, Any, Optional
from enum import Enum
from .prompt_loader import load_prompt




class SafetyLevel(Enum):
    """Safety levels for automated fixes"""
    SAFE_ONLY = "safe_only"
    REVIEW_REQUIRED = "review_required"
    MANUAL_APPROVAL = "manual_approval"


class FileActionPrompts:
    """Message templates for file action operations"""
    
    @staticmethod
    def get_code_review_messages(
        language: str,
        file_path: str,
        file_content: str,
    ) -> List[Dict[str, str]]:
        """
        Generate messages for comprehensive code review

        Args:
            language: Programming language of the code
            file_path: Path to the file being reviewed
            file_content: Complete file content to review
        """

        system_prompt = load_prompt("file_actions", "code_review")

        user_prompt = f"""Review this {language} code:
```
{file_content}
```"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_documentation_generation_messages(
        language: str,
        file_path: str,
        file_content: str,
    ) -> List[Dict[str, str]]:
        """
        Generate messages for documentation generation/update

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content

        Returns:
            List of message dictionaries for the LLM containing system and user prompts
        """
        system_prompt = f"""
{load_prompt("file_actions", "documentation")}

Here's the code in {language} for which you need to generate documentation in markdown format:
<code>
{file_content}
</code>
   """

        user_prompt = f"""File: {file_path}

Code to Document:
```{language}
{file_content}
```

Please generate comprehensive documentation following {language} best practices and the specified format."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    @staticmethod
    def get_security_scan_messages(
        language: str,
        file_path: str,
        file_content: str,
    ) -> List[Dict[str, str]]:
        """
        Generate messages for security vulnerability detection

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content
        """

        system_prompt = f"""
{load_prompt("file_actions", "security_scan")}

Here's the {language} code to analyze:
<code>
{file_content}
</code>
   """

        user_prompt = f"""File Path: {file_path}
Language: {language}

Code to Scan for Security Vulnerabilities:
```{language}
{file_content}
```

Please perform a comprehensive security analysis following the specified format. Rate each issue's severity on a scale of 1-10."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    @staticmethod
    def get_fix_suggestions_messages(
        language: str,
        file_path: str,
        file_content: str,
        title: str,
        description: str,
        target_code_block: str
    ) -> List[Dict[str, str]]:
        """
        Generate messages for automated fix suggestions

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content

        """

        system_prompt = f"""
{load_prompt("file_actions", "fix_suggestions")}

Security Issue Details:
- Title: {title}
- Description: {description}
- Language: {language}
- file_content: {file_content}

Original Code to Fix:
<target_code>
{target_code_block}
</target_code>
   """

        user_prompt = f"""File Path: {file_path}
Language: {language}

Current Code:
```{language}
{file_content}
```
Please generate specific, actionable fixes for these issues"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    @staticmethod
    def get_auto_apply_messages(
        language: str,
        file_path: str,
        file_content: str,
        fixes: List[Dict[str, Any]],
        safety_level: SafetyLevel = SafetyLevel.SAFE_ONLY
    ) -> List[Dict[str, str]]:
        """
        Generate messages for auto-applying fixes to code

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content
            fixes: List of fixes to apply
            safety_level: Safety level for auto-application
        """
        safety_instructions = {
            SafetyLevel.SAFE_ONLY: "Only apply fixes marked as SAFE with confidence >= 0.9",
            SafetyLevel.REVIEW_REQUIRED: "Apply fixes marked as SAFE or LIKELY_SAFE with confidence >= 0.8",
            SafetyLevel.MANUAL_APPROVAL: "Apply all approved fixes regardless of safety rating"
        }

        fixes_text = "\n".join([
            f"- Fix {i+1}: {fix.get('title', 'Unknown fix')} (Confidence: {fix.get('confidence', 0)}, Safety: {fix.get('safety_rating', 'UNKNOWN')})"
            for i, fix in enumerate(fixes)
        ])

        system_prompt = f"""{load_prompt("file_actions", "auto_apply")}

Safety Level: {safety_level.value}
{safety_instructions[safety_level]}"""

        user_prompt = f"""File Path: {file_path}
Language: {language}

Original Code:
```{language}
{file_content}
```

Fixes to Apply:
{fixes_text}

Please apply the approved fixes following the safety level: {safety_level.value}"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]


# Convenience functions to get messages by operation type
def get_file_action_messages(
    operation: str,
    language: str,
    file_path: str,
    file_content: str,
    **kwargs
) -> List[Dict[str, str]]:
    """
    Get file action messages for a specific operation type

    Args:
        operation: Type of operation ('review', 'document', 'security-scan', 'fix-suggestions', 'auto-apply')
        language: Programming language
        file_path: Path to the file
        file_content: Complete file content
        **kwargs: Additional arguments specific to each operation

    Returns:
        List of message dictionaries for the LLM

    Raises:
        ValueError: If operation type is not supported
    """
    operation_map = {
        'review': FileActionPrompts.get_code_review_messages,
        'document': FileActionPrompts.get_documentation_generation_messages,
        'security-scan': FileActionPrompts.get_security_scan_messages,
        'fix-suggestions': FileActionPrompts.get_fix_suggestions_messages,
        'auto-apply': FileActionPrompts.get_auto_apply_messages
    }

    if operation not in operation_map:
        raise ValueError(f"Unsupported operation: {operation}. Supported operations: {list(operation_map.keys())}")

    return operation_map[operation](language, file_path, file_content, **kwargs)
