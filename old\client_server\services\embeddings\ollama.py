import traceback
from overrides import override
from . import IEmbeddingBackend
import ollama
import socket
import subprocess
import time
import os
from pathlib import Path
from subprocess import Pope<PERSON>
from typing import Optional, Coroutine

from client_server.core.logger import LOGGER
from client_server.utils.platform_detector import PlatformDetector
from client_server.utils.path_selector import PathSelector


class OllamaEmbeddingsBackend(IEmbeddingBackend):
    _server_process: Optional[Popen[str]] = None
    _api_base: str = "http://localhost:11434"

    def __init__(self):
        super().__init__()
        self.target_dir = PathSelector.get_cache_path() / "ollama"
        self._ollama_binary_path = self._get_binary_path(str(self.target_dir))

    def _get_binary_path(self, ollama_base_path: str) -> str:
        """Get the platform-specific path to the Ollama binary."""
        binary_paths = {
            "windows": f"{ollama_base_path}/ollama.exe",
            "linux": f"{ollama_base_path}/bin/ollama",
            "darwin": f"{ollama_base_path}/ollama",
        }

        os_name = PlatformDetector.get_os_name()
        if os_name not in binary_paths:
            raise ValueError(f"Unsupported platform: {os_name}")
        return binary_paths[os_name]

    def _is_port_available(self, port: int = 11434) -> bool:
        """Check if the specified port is available."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return True
        except socket.error:
            return False

    def _start_server(self):
        """Start the Ollama server if not already running"""
        if self._server_process is not None:
            return

        # Check if port is available
        if not self._is_port_available():
            LOGGER.debug("Ollama server is already running, using existing instance")
            return

        # Create .logs directory if it doesn't exist
        log_path = PathSelector.get_logs_path() / "ollama.log"
        LOGGER.info(f"Starting ollama server at {self._ollama_binary_path}")
        try:
            with open(log_path, "w") as log_file:
                env = os.environ.copy()
                env["OLLAMA_MODELS"] = str(self.target_dir)
                self._server_process = subprocess.Popen(
                    [self._ollama_binary_path, "serve"],
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    text=True,
                    env=env,
                    shell=False,
                )
                time.sleep(1)
                retry_count = 10
                while retry_count > 0:
                    try:
                        ollama.ps()
                        break
                    except Exception as e:
                        LOGGER.warning(
                            f"Failed to start ollama server, retrying... ({retry_count}/10): {e}"
                        )
                        retry_count -= 1
                        time.sleep(1)
                LOGGER.info("ollama server started successfully")
        except Exception as e:
            LOGGER.error(f"Failed to start ollama server: {e}")
            raise RuntimeError(f"Failed to start ollama server: {e}")

    @override
    async def generate(self, content: str) -> Coroutine[list[float], None, None]:
        # Ensure server is running
        self._start_server()

        embed = ollama.embed(model="nomic-embed-text:v1.5", input=content)
        return embed.embeddings[0]

    @override
    async def generate_batch(
        self, content: list[str]
    ) -> Coroutine[list[list[float]], None, None]:
        print("from ollama")
        # log traceback as warn log
        traceback.print_stack()
        LOGGER.warning(traceback.format_stack())

        # Ensure server is running
        self._start_server()

        embeddings = []
        for text in content:
            embed = ollama.embed(model="nomic-embed-text:v1.5", input=text)
            embeddings.append(embed.embeddings[0])
        return embeddings
