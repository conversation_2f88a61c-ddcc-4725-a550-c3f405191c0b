# Web Search (`web_search.py`)

This module implements the `web_search` action, which allows the application to perform a web search using an external service.

## Functions

### `process_web_search(*, query: str, tool_id: str, session: str, search_references: SearchReferences)`

This is the main function for handling a web search request.

- **Parameters:**
  - `query` (str): The search query.
  - `tool_id` (str): The unique identifier for this tool call.
  - `session` (str): The user's session ID, used for authentication with the search service.
  - `search_references` (SearchReferences): An object to track the search results.
- **Returns:**
  - A tuple containing:
    - `list[dict]`: A list of formatted messages for the language model, including the search results.
    - `SearchReferences`: The updated search references object.

The function works by making a POST request to the `/web_search` API endpoint. It then processes the response, extracts the search results and their sources, adds the sources to the `search_references` object, and formats the content into the standard message structure for the language model.
