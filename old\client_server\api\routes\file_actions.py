"""
File Actions API Routes

This module provides REST API endpoints for automated file analysis and improvements
including code review, documentation generation, security scanning, fix suggestions,
and automated fix application.
"""

import time
import json
import traceback
import asyncio
from typing import Any, Dict, Optional, List
from pathlib import Path
from fastapi import HTT<PERSON>Ex<PERSON>, Request
from pydantic import ValidationError

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_operation_stats
from client_server.api.events.connection import update_session_activity
from client_server.utils.router.litellm_router import get_litellm_session_router
from client_server.utils.router.openai_router import get_openai_session_router
from client_server.utils.prompts.file_action_prompts import get_file_action_messages

from client_server.utils.path_selector import PathSelector
from client_server.services.db_handler import Database
from . import route
from client_server.core.state import G_SESSION_ID


# Initialize database for storing analysis results
db = Database()
file_actions_collection = db["file_actions"]
analysis_history_collection = db["analysis_history"]


async def _process_file_action_request(
    operation: str,
    request_data: Dict[str, Any],  # Changed from FileActionRequest to Dict
    session_id: Optional[str] = None,
    **kwargs
) -> Any:
    """
    Process a file action request using LiteLLM
    
    Args:
        operation: Type of operation to perform
        request_data: Request data containing file information
        session_id: Optional session identifier
        **kwargs: Additional operation-specific parameters
    
    Returns:
        FileActionResponse with operation results
    """
    start_time = time.time()
    
    try:
        # Update session activity if session ID is provided
        if session_id:
            update_session_activity(session_id)
        
        LOGGER.info(f"Processing file action '{operation}' for file: {request_data['file_path']}")
        
        # Generate messages for the operation
        messages = get_file_action_messages(
            operation=operation,
            language=request_data['language'],
            file_path=request_data['file_path'],
            file_content=request_data['file_content'],
            **kwargs
        )

        # LOGGER.info(json.dumps(messages, indent=2))
        
        # Get LiteLLM router and make completion request
        router = get_litellm_session_router(session_id)
        # router = get_openai_session_router(session_id)

        LOGGER.info(f"Using session ID: {session_id}")
        LOGGER.info(f"Client type: {type(router).__name__}")

        # Check if client was created successfully
        if router is None:
            raise ValueError("Failed to create OpenAI client. Check your API configuration and session.")

        # Using OpenAI's chat completion API
        completion_start = time.time()
        # response = await client.chat.completions.create(
        #     messages=messages,
        #     model="bodh-x1",
        #     temperature=0.1,  # Low temperature for more consistent analysis
        #     max_tokens=8000,  # Higher token limit for detailed analysis
        #     # stream=True
        # )

        response = await router.acompletion(
            messages=messages,
            model=request_data['model'],
            temperature=0.1,  # Low temperature for more consistent analysis
            max_tokens=8000,  # Higher token limit for detailed analysis
            # stream=True
        )
        completion_time = time.time() - completion_start
        
        # Extract content from response
        content = response.choices[0].message.content
        
        # Process response based on operation type
        result = {}
        
        if operation == "document":
            try:
                parsed_content = content.split("<docs>")[-1].split("</docs>")[0]
                result = {'docs': parsed_content}
            except Exception as e:
                LOGGER.warning(f"Failed to parse documentation content: {e}")
                result = {"raw_content": content}
                
        elif operation == "security-scan":
            try:
                # Extract overall evaluation
                overall_eval = content.split("<overall_eval>")[-1].split("</overall_eval>")[0].strip()
                
                # Parse security problems
                problems = []
                problems_section = content.split("<problems>")[-1].split("</problems>")[0].strip()
                
                if problems_section:
                    # Process each problem block
                    for block in problems_section.split("<problem>")[1:]:  # Skip empty first element
                        if "</problem>" not in block:
                            continue
                            
                        problem_content = block.split("</problem>")[0]
                        problem_data = {}
                        
                        # Extract problem components using helper function
                        for field in ["title", "severity", "description", "target_code_block"]:
                            tag = f"<{field}>"
                            end_tag = f"</{field}>"
                            if tag in problem_content and end_tag in problem_content:
                                problem_data[field] = problem_content.split(tag)[-1].split(end_tag)[0].strip()
                        
                        if problem_data.get("title"):  # Only add if title exists
                            problems.append(problem_data)
                
                result = {
                    "overall_eval": overall_eval,
                    "problems_identified": problems
                }
            except Exception as e:
                LOGGER.warning(f"Failed to parse security scan content: {e}")
                result = {"raw_content": content}
                
        elif operation == "fix-suggestions":
            try:
                fixes = []
                fixes_section = content.split("<fixes>")[-1].split("</fixes>")[0].strip()
                
                if fixes_section:
                    # Split by <fix> tags and process each one
                    fix_blocks = fixes_section.split("<fix>")[1:]  # Skip the first empty element
                    
                    for block in fix_blocks:
                        if "</fix>" in block:
                            fix_content = block.split("</fix>")[0]
                            
                            # Extract old_code and new_code
                            old_code = ""
                            new_code = ""
                            
                            if "<old_code>" in fix_content and "</old_code>" in fix_content:
                                old_code = fix_content.split("<old_code>")[-1].split("</old_code>")[0].strip()
                            
                            if "<new_code>" in fix_content and "</new_code>" in fix_content:
                                new_code = fix_content.split("<new_code>")[-1].split("</new_code>")[0].strip()
                            
                            if old_code and new_code:  # Only add if we have both parts
                                fixes.append({
                                    "old_code": old_code,
                                    "new_code": new_code
                                })
                
                result = {"fixes": fixes }
            except json.JSONDecodeError:
                LOGGER.warning(f"Failed to parse JSON response for fix suggestions, using raw content")
                result = {"raw_content": content}            
        elif operation == "review":
            try:
                # Extract analysis components
                analysis = {}
                
                # Parse score
                if "<score>" in content and "</score>" in content:
                    analysis["score"] = int(content.split("<score>")[-1].split("</score>")[0].strip())
                
                # Parse summary
                if "<summary>" in content and "</summary>" in content:
                    analysis["summary"] = content.split("<summary>")[-1].split("</summary>")[0].strip()
                
                # Parse issues
                issues = []
                if "<issues>" in content and "</issues>" in content:
                    issues_section = content.split("<issues>")[-1].split("</issues>")[0].strip()
                    
                    # Process each issue block
                    for block in issues_section.split("<issue>")[1:]:  # Skip empty first element
                        if "</issue>" not in block:
                            continue
                            
                        issue_content = block.split("</issue>")[0]
                        issue = {}
                        
                        # Extract issue components
                        for field in ["severity", "category", "line", "description", "fix"]:
                            tag = f"<{field}>"
                            end_tag = f"</{field}>"
                            if tag in issue_content and end_tag in issue_content:
                                issue[field] = issue_content.split(tag)[-1].split(end_tag)[0].strip()
                                if field == "line":
                                    issue[field] = int(issue[field])
                        
                        if issue:  # Only add if we extracted some data
                            issues.append(issue)
                
                analysis["issues"] = issues
                result = analysis
            except Exception as e:
                LOGGER.warning(f"Failed to parse review content: {e}")
                result = {"raw_content": content}
                
        elif operation in ['auto-apply']:
            try:
                result = json.loads(content)
            except json.JSONDecodeError:
                LOGGER.warning(f"Failed to parse JSON response for {operation}, using raw content")
                result = {"raw_content": content}
        else:
            result = {"content": content}
        
        processing_time = time.time() - start_time
        
        # Store analysis result in database
        analysis_record = {
            "operation": operation,
            "file_path": request_data['file_path'],
            "language": request_data['language'],
            "processing_time": processing_time,
            "completion_time": completion_time,
            "session_id": session_id,
            # "result_summary": _extract_result_summary(operation, result)
        }
        analysis_history_collection.insert_one(analysis_record)
        
        LOGGER.info(f"File action '{operation}' completed in {processing_time:.3f}s")
        
        return {
            "success": True,
            "operation": operation,
            "file_path": request_data['file_path'],
            "result": result,
            "processing_time": processing_time,
        }
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Error processing file action '{operation}': {str(e)}"
        LOGGER.error(f"{error_msg}\n{traceback.format_exc()}")
        
        response = {
            "success": False,
            "operation": operation,
            "file_path": request_data.get('file_path', ''),
            "result": {},
            "error": error_msg,
            "processing_time": processing_time,
        }
        return response

def _detect_language_from_path(file_path: str) -> str:
    """Detect programming language from file extension"""
    extension_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.jsx': 'javascript',
        '.tsx': 'typescript',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby',
        '.go': 'go',
        '.rs': 'rust',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.sh': 'bash',
        '.sql': 'sql',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.less': 'less',
        '.json': 'json',
        '.xml': 'xml',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.md': 'markdown',
        '.dockerfile': 'dockerfile'
    }
    
    path = Path(file_path)
    extension = path.suffix.lower()
    
    # Special cases
    if path.name.lower() == 'dockerfile':
        return 'dockerfile'
    
    return extension_map.get(extension, 'text')



@route("POST", "/auto-actions/review")
async def file_action_review(request: Request):
    """
    Analyze code quality, style, and provide detailed feedback with severity levels
    
    Performs comprehensive code review including:
    - Code correctness and potential bugs
    - Performance bottlenecks and inefficiencies
    - Security vulnerabilities and best practices
    - Code style and consistency
    - Maintainability and readability
    - Documentation quality
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()
        request_data = data
        
        # Process the request
        response = await _process_file_action_request(
            "review",
            request_data,
            session_id,
        )
        
        # Log operation stats
        log_operation_stats("file_action_review", time.time() - start_time, response["success"])
        
        return response
        
    except ValidationError as e:
        LOGGER.error(f"Validation error in file action review: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Unexpected error in file action review: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/auto-actions/doc")

async def file_action_document(request: Request):
    """
    Generate or update documentation based on code structure and functionality
    
    Supports multiple documentation types:
    - inline: Docstrings, comments, and type annotations
    - readme: README documentation for projects/modules
    - api: API documentation with endpoints and examples
    - comments: Explanatory comments for complex logic
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()
        
        request_data = data
        
        
        # Process the request
        response = await _process_file_action_request(
            "document",
            request_data,
            session_id,
        )
        
        # Log operation stats
        log_operation_stats("file_action_document", time.time() - start_time, response["success"])
        
        return response
        
    except ValidationError as e:
        LOGGER.error(f"Validation error in file action document: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Unexpected error in file action document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/auto-actions/security-scan")
async def file_action_security_scan(request: Request):
    """
    Detect security vulnerabilities, return findings with CVSS scores and remediation steps

    Performs comprehensive security analysis including:
    - OWASP Top 10 vulnerabilities
    - Input validation and sanitization issues
    - Authentication and authorization flaws
    - Injection vulnerabilities
    - Cryptographic issues
    - Sensitive data exposure
    """
    start_time = time.time()

    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()

        # Validate request data
        request_data = data

        # Auto-detect language if not provided
        # if not request_data.language or request_data.language == "auto":
        #     request_data.language = _detect_language_from_path(request_data.file_path)

        # Process the request
        response = await _process_file_action_request(
            "security-scan",
            request_data,
            session_id,
        )

        # Log operation stats
        log_operation_stats("file_action_security_scan", time.time() - start_time, response["success"])

        return response

    except ValidationError as e:
        LOGGER.error(f"Validation error in file action security scan: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Unexpected error in file action security scan: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/auto-actions/fix-suggestions")
async def file_action_fix_suggestions(request: Request):
    """
    Generate automated fix recommendations with confidence ratings and preview diffs

    Provides specific, actionable fixes for identified issues including:
    - Confidence scores and safety ratings
    - Impact assessments
    - Before/after code previews
    - Testing requirements
    - Dependencies between fixes
    """
    start_time = time.time()

    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()

        # Validate request data
        request_data = data

        # Auto-detect language if not provided
        # if not request_data.language or request_data.language == "auto":
        #     request_data.language = _detect_language_from_path(request_data.file_path)

        # Process the request
        response = await _process_file_action_request(
            "fix-suggestions",
            request_data,
            session_id,
            title=request_data["title"],
            description=request_data["description"],
            target_code_block=request_data["target_code_block"]
        )

        # Log operation stats
        log_operation_stats("file_action_fix_suggestions", time.time() - start_time, response["success"])

        return response

    except ValidationError as e:
        LOGGER.error(f"Validation error in file action fix suggestions: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Unexpected error in file action fix suggestions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/file-actions/apply-fixes")
async def file_action_apply_fixes(request: Request):
    """
    Apply selected fixes to files with rollback capability and change tracking

    Safely applies automated fixes with:
    - Safety level validation
    - Backup creation
    - Rollback capability
    - Change tracking and audit logs
    - Dry run support
    """
    start_time = time.time()

    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()

        # Validate request data
        # request_data = AutoApplyRequest.model_validate(data)
        request_data = data

        # Auto-detect language if not provided
        if not request_data.language or request_data.language == "auto":
            request_data.language = _detect_language_from_path(request_data.file_path)

        # Create backup if requested and not a dry run
        backup_path = None
        if request_data.create_backup and not request_data.dry_run:
            backup_path = await _create_file_backup(request_data.file_path)

        # Process the request
        response = await _process_file_action_request(
            "auto-apply",
            request_data,
            session_id,
            fixes=request_data.fixes,
            safety_level=request_data.safety_level
        )

        # Add backup path to response if created
        if backup_path and response["success"]:
            response["result"]["backup_path"] = backup_path

        # Log operation stats
        log_operation_stats("file_action_apply_fixes", time.time() - start_time, response["success"])

        return response

    except ValidationError as e:
        LOGGER.error(f"Validation error in file action apply fixes: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Unexpected error in file action apply fixes: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _create_file_backup(file_path: str) -> str:
    """Create a backup of the file before applying fixes"""
    try:
        import shutil
        from datetime import datetime

        path = Path(file_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = path.parent / f"{path.stem}_backup_{timestamp}{path.suffix}"

        shutil.copy2(file_path, backup_path)
        LOGGER.info(f"Created backup: {backup_path}")

        return str(backup_path)
    except Exception as e:
        LOGGER.error(f"Failed to create backup for {file_path}: {e}")
        raise


@route("POST", "/auto-actions/batch")
async def file_action_batch(request: Request):
    """
    Process multiple files in a single batch request

    Supports batch processing for any file action operation with:
    - Parallel processing support
    - Configurable concurrency limits
    - Individual file result tracking
    - Error isolation (one file failure doesn't stop others)
    """
    start_time = time.time()

    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()

        # Validate request data
        # request_data = BatchFileActionRequest.model_validate(data)
        request_data = data

        results = []
        successful_files = 0
        failed_files = 0
        errors = []

        if request_data["parallel_processing"]:
            # Process files in parallel with concurrency limit
            semaphore = asyncio.Semaphore(request_data["max_concurrent"])

            async def process_single_file(file_request):
                async with semaphore:
                    return await _process_file_action_request(
                        file_request["operation"],  # Use batch operation
                        file_request,
                        session_id
                    )

            # Create tasks for all files
            tasks = [process_single_file(file_req) for file_req in request_data["files"]]

            # Execute all tasks
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results and handle exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    error_msg = f"Error processing file {request_data['files'][i]['file_path']}: {str(result)}"
                    errors.append(error_msg)
                    failed_files += 1

                    # Create error response
                    error_response = {
                        "success": False,
                        "operation": request_data["operation"],
                        "file_path": request_data["files"][i]["file_path"],
                        "result": {},
                        "error": error_msg,
                        "processing_time": 0.0,
                        "model_used": request_data["files"][i].get("model", "")
                    }
                    processed_results.append(error_response)
                else:
                    processed_results.append(result)
                    if result["success"]:
                        successful_files += 1
                    else:
                        failed_files += 1
                        if result.get("error"):
                            errors.append(result["error"])

            results = processed_results
        else:
            # Process files sequentially
            for file_request in request_data["files"]:
                try:
                    result = await _process_file_action_request(
                        request_data["operation"],  # Use batch operation
                        file_request,
                        session_id
                    )
                    results.append(result)

                    if result["success"]:
                        successful_files += 1
                    else:
                        failed_files += 1
                        if result.get("error"):
                            errors.append(result["error"])

                except Exception as e:
                    error_msg = f"Error processing file {file_request['file_path']}: {str(e)}"
                    errors.append(error_msg)
                    failed_files += 1

                    # Create error response
                    error_response = {
                        "success": False,
                        "operation": request_data["operation"],
                        "file_path": file_request["file_path"],
                        "result": {},
                        "error": error_msg,
                        "processing_time": 0.0,
                        "model_used": file_request.get("model", "")
                    }
                    results.append(error_response)

        processing_time = time.time() - start_time

        # Create batch response
        batch_response = {
            "success": failed_files == 0,
            "total_files": len(request_data["files"]),
            "successful_files": successful_files,
            "failed_files": failed_files,
            "results": results,
            "processing_time": processing_time,
            "errors": errors
        }

        # Log operation stats
        log_operation_stats("file_action_batch", processing_time, batch_response["success"])

        LOGGER.info(f"Batch file action completed: {successful_files}/{len(request_data['files'])} files successful")

        return batch_response

    except ValidationError as e:
        LOGGER.error(f"Validation error in batch file action: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Unexpected error in batch file action: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/history")
async def file_action_history(request: Request):
    """
    Get analysis history for files

    Query parameters:
    - file_path: Filter by specific file path
    - operation: Filter by operation type
    - session_id: Filter by session
    - limit: Maximum number of results (default: 50)
    - offset: Offset for pagination (default: 0)
    """
    try:
        # Get query parameters
        file_path = request.query_params.get("file_path")
        operation = request.query_params.get("operation")
        session_id = request.query_params.get("session_id")
        limit = int(request.query_params.get("limit", 50))
        offset = int(request.query_params.get("offset", 0))

        # Build query filter
        query_filter = {}
        if file_path:
            query_filter["file_path"] = file_path
        if operation:
            query_filter["operation"] = operation
        if session_id:
            query_filter["session_id"] = session_id

        # Query database
        results = list(analysis_history_collection.find(
            query_filter,
            sort=[("timestamp", -1)],  # Most recent first
            limit=limit,
            skip=offset
        ))

        return {
            "success": True,
            "results": results,
            "count": len(results),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        LOGGER.error(f"Error retrieving file action history: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/stats")
async def file_action_stats(request: Request = None):
    """
    Get statistics about file actions

    Returns aggregated statistics including:
    - Total operations by type
    - Average processing times
    - Success rates
    - Most analyzed files
    """
    try:
        # Get all analysis records
        all_records = list(analysis_history_collection.find())

        if not all_records:
            return {
                "success": True,
                "stats": {
                    "total_operations": 0,
                    "operations_by_type": {},
                    "average_processing_time": 0,
                    "success_rate": 0,
                    "most_analyzed_files": []
                }
            }

        # Calculate statistics
        total_operations = len(all_records)
        operations_by_type = {}
        total_processing_time = 0
        successful_operations = 0
        file_analysis_count = {}

        for record in all_records:
            # Count operations by type
            operation = record.get("operation", "unknown")
            operations_by_type[operation] = operations_by_type.get(operation, 0) + 1

            # Sum processing times
            processing_time = record.get("processing_time", 0)
            total_processing_time += processing_time

            # Count successful operations (assuming success if no error in result_summary)
            result_summary = record.get("result_summary", {})
            if not result_summary.get("error"):
                successful_operations += 1

            # Count file analysis frequency
            file_path = record.get("file_path", "unknown")
            file_analysis_count[file_path] = file_analysis_count.get(file_path, 0) + 1

        # Calculate averages and rates
        average_processing_time = total_processing_time / total_operations if total_operations > 0 else 0
        success_rate = successful_operations / total_operations if total_operations > 0 else 0

        # Get most analyzed files (top 10)
        most_analyzed_files = sorted(
            file_analysis_count.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]

        stats = {
            "total_operations": total_operations,
            "operations_by_type": operations_by_type,
            "average_processing_time": round(average_processing_time, 3),
            "success_rate": round(success_rate, 3),
            "most_analyzed_files": [
                {"file_path": path, "analysis_count": count}
                for path, count in most_analyzed_files
            ]
        }

        return {
            "success": True,
            "stats": stats
        }

    except Exception as e:
        LOGGER.error(f"Error retrieving file action stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Queue Management Endpoints
@route("POST", "/file-actions/queue")
async def file_action_enqueue(request: Request):
    """
    Add a file action to the background processing queue

    Supports all file action operations with configurable priority and retry settings.
    Returns a queue item ID for tracking progress.
    """
    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()

        # Extract queue-specific parameters
        operation = data.pop("operation")
        priority_str = data.pop("priority", "NORMAL")
        max_retries = data.pop("max_retries", 3)

        # Validate operation
        valid_operations = ["review", "document", "security-scan", "fix-suggestions", "auto-apply"]
        if operation not in valid_operations:
            raise HTTPException(status_code=400, detail=f"Invalid operation: {operation}")

        # Parse priority
        from client_server.utils.file_action.file_action_queue import QueuePriority, get_file_action_queue
        try:
            priority = QueuePriority[priority_str.upper()]
        except KeyError:
            raise HTTPException(status_code=400, detail=f"Invalid priority: {priority_str}")

        # Validate request data based on operation
        if operation == "review":
            request_data = CodeReviewRequest.model_validate(data)
        elif operation == "document":
            request_data = DocumentationRequest.model_validate(data)
        elif operation == "security-scan":
            request_data = SecurityScanRequest.model_validate(data)
        elif operation == "fix-suggestions":
            request_data = FixSuggestionsRequest.model_validate(data)
        elif operation == "auto-apply":
            request_data = AutoApplyRequest.model_validate(data)
        else:
            request_data = FileActionRequest.model_validate(data)

        # Auto-detect language if not provided
        if not request_data.language or request_data.language == "auto":
            request_data.language = _detect_language_from_path(request_data.file_path)

        # Get queue and enqueue item
        queue = get_file_action_queue()
        item_id = queue.enqueue(
            operation=operation,
            request_data=request_data,
            priority=priority,
            session_id=session_id,
            max_retries=max_retries
        )

        return {
            "success": True,
            "item_id": item_id,
            "operation": operation,
            "priority": priority.name,
            "queue_position": queue.get_queue_stats()["pending_items"]
        }

    except ValidationError as e:
        LOGGER.error(f"Validation error in file action enqueue: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request data: {e}")
    except Exception as e:
        LOGGER.error(f"Error enqueuing file action: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/queue/{item_id}")
async def file_action_queue_status(request: Request):
    """
    Get the status of a queued file action item

    Returns current status, progress information, and results if completed.
    """
    try:
        # Extract item_id from path
        path_parts = request.url.path.split('/')
        item_id = path_parts[-1]

        from client_server.utils.file_action.file_action_queue import get_file_action_queue
        queue = get_file_action_queue()

        item = queue.get_item_status(item_id)
        if not item:
            raise HTTPException(status_code=404, detail="Queue item not found")

        response = {
            "success": True,
            "item_id": item.id,
            "operation": item.operation,
            "status": item.status.value,
            "priority": item.priority.name,
            "created_at": item.created_at,
            "retry_count": item.retry_count,
            "max_retries": item.max_retries
        }

        if item.started_at:
            response["started_at"] = item.started_at

        if item.completed_at:
            response["completed_at"] = item.completed_at
            response["processing_time"] = item.completed_at - item.started_at

        if item.result:
            response["result"] = item.result.model_dump()

        if item.error:
            response["error"] = item.error

        return response

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error getting queue item status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("DELETE", "/file-actions/queue/{item_id}")
async def file_action_queue_cancel(request: Request):
    """
    Cancel a queued file action item

    Can only cancel items that haven't started processing yet.
    """
    try:
        # Extract item_id from path
        path_parts = request.url.path.split('/')
        item_id = path_parts[-1]

        from client_server.utils.file_action.file_action_queue import get_file_action_queue
        queue = get_file_action_queue()

        cancelled = queue.cancel_item(item_id)

        if not cancelled:
            raise HTTPException(
                status_code=400,
                detail="Item cannot be cancelled (not found, already processing, or completed)"
            )

        return {
            "success": True,
            "item_id": item_id,
            "message": "Item cancelled successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error cancelling queue item: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/queue/stats")
async def file_action_queue_stats(request: Request):
    """
    Get current queue statistics and performance metrics

    Returns information about queue size, processing times, success rates, etc.
    """
    try:
        from client_server.utils.file_action.file_action_queue import get_file_action_queue
        queue = get_file_action_queue()

        stats = queue.get_queue_stats()

        return {
            "success": True,
            "queue_stats": stats,
            "pending_items": queue.get_pending_items(),
            "processing_items": queue.get_processing_items()
        }

    except Exception as e:
        LOGGER.error(f"Error getting queue stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/file-actions/queue/clear")
async def file_action_queue_clear(request: Request):
    """
    Clear completed and failed items from the queue

    Accepts optional parameter 'older_than_hours' to only clear items older than specified time.
    """
    try:
        # Parse request body for parameters
        body = await request.body()
        if body:
            data = json.loads(body.decode('utf-8'))
            older_than_hours = data.get("older_than_hours", 24)
        else:
            older_than_hours = 24

        from client_server.utils.file_action.file_action_queue import get_file_action_queue
        queue = get_file_action_queue()

        queue.clear_completed_items(older_than_hours)

        return {
            "success": True,
            "message": f"Cleared completed items older than {older_than_hours} hours"
        }

    except Exception as e:
        LOGGER.error(f"Error clearing queue: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Safety and Rollback Endpoints
@route("POST", "/file-actions/rollback/change/{change_id}")
async def file_action_rollback_change(request: Request):
    """
    Rollback a specific file change using its backup

    Restores the file to its state before the change was applied.
    Creates a backup of the current state before rollback.
    """
    try:
        # Extract change_id from path
        path_parts = request.url.path.split('/')
        change_id = path_parts[-1]

        from client_server.utils.file_action.file_action_safety import get_safety_manager
        safety_manager = get_safety_manager()

        success, message = safety_manager.rollback_change(change_id)

        if not success:
            raise HTTPException(status_code=400, detail=message)

        return {
            "success": True,
            "change_id": change_id,
            "message": message
        }

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error rolling back change: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/file-actions/rollback/operation/{operation_id}")
async def file_action_rollback_operation(request: Request):
    """
    Rollback all changes from a specific operation

    Rolls back all file changes made during a single operation in reverse chronological order.
    """
    try:
        # Extract operation_id from path
        path_parts = request.url.path.split('/')
        operation_id = path_parts[-1]

        from client_server.utils.file_action.file_action_safety import get_safety_manager
        safety_manager = get_safety_manager()

        success, message, rolled_back_changes = safety_manager.rollback_operation(operation_id)

        return {
            "success": success,
            "operation_id": operation_id,
            "message": message,
            "rolled_back_changes": rolled_back_changes,
            "changes_count": len(rolled_back_changes)
        }

    except Exception as e:
        LOGGER.error(f"Error rolling back operation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/changes")
async def file_action_change_history(request: Request):
    """
    Get file change history with optional filtering

    Query parameters:
    - file_path: Filter by specific file path
    - operation_id: Filter by operation ID
    - session_id: Filter by session ID
    - limit: Maximum number of results (default: 50)
    """
    try:
        # Get query parameters
        file_path = request.query_params.get("file_path")
        operation_id = request.query_params.get("operation_id")
        session_id = request.query_params.get("session_id")
        limit = int(request.query_params.get("limit", 50))

        from client_server.utils.file_action.file_action_safety import get_safety_manager
        safety_manager = get_safety_manager()

        changes = safety_manager.get_change_history(
            file_path=file_path,
            operation_id=operation_id,
            session_id=session_id,
            limit=limit
        )

        return {
            "success": True,
            "changes": changes,
            "count": len(changes),
            "limit": limit
        }

    except Exception as e:
        LOGGER.error(f"Error retrieving change history: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/audit")
async def file_action_audit_log(request: Request):
    """
    Get audit log entries with optional filtering

    Query parameters:
    - action: Filter by action type
    - file_path: Filter by specific file path
    - operation_id: Filter by operation ID
    - session_id: Filter by session ID
    - limit: Maximum number of results (default: 100)
    """
    try:
        # Get query parameters
        action = request.query_params.get("action")
        file_path = request.query_params.get("file_path")
        operation_id = request.query_params.get("operation_id")
        session_id = request.query_params.get("session_id")
        limit = int(request.query_params.get("limit", 100))

        from client_server.utils.file_action.file_action_safety import get_safety_manager
        safety_manager = get_safety_manager()

        entries = safety_manager.get_audit_log(
            action=action,
            file_path=file_path,
            operation_id=operation_id,
            session_id=session_id,
            limit=limit
        )

        return {
            "success": True,
            "audit_entries": entries,
            "count": len(entries),
            "limit": limit
        }

    except Exception as e:
        LOGGER.error(f"Error retrieving audit log: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/file-actions/cleanup")
async def file_action_cleanup(request: Request):
    """
    Clean up old backup files and change records

    Accepts optional parameter 'days_old' to specify age threshold (default: 30 days).
    """
    try:
        # Parse request body for parameters
        body = await request.body()
        if body:
            data = json.loads(body.decode('utf-8'))
            days_old = data.get("days_old", 30)
        else:
            days_old = 30

        from client_server.utils.file_action.file_action_safety import get_safety_manager
        safety_manager = get_safety_manager()

        files_removed, records_cleaned = safety_manager.cleanup_old_backups(days_old)

        return {
            "success": True,
            "files_removed": files_removed,
            "records_cleaned": records_cleaned,
            "days_old": days_old,
            "message": f"Cleaned up {files_removed} backup files and {records_cleaned} change records older than {days_old} days"
        }

    except Exception as e:
        LOGGER.error(f"Error during cleanup: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Configuration Management Endpoints
@route("GET", "/file-actions/config")
async def file_action_get_config(request: Request):
    """
    Get file action configuration

    Query parameters:
    - file_path: Get effective config for specific file (includes project detection)
    - project_path: Get config for specific project
    - global: Get global configuration only (default if no other params)
    """
    try:
        # Get query parameters
        file_path = request.query_params.get("file_path")
        project_path = request.query_params.get("project_path")

        from client_server.utils.file_action.file_action_config import get_config_manager
        config_manager = get_config_manager()

        if file_path:
            # Get effective config for file
            config = config_manager.get_effective_config(file_path)
            detected_project = config_manager.detect_project_path(file_path)

            return {
                "success": True,
                "config_type": "file_effective",
                "file_path": file_path,
                "detected_project": detected_project,
                "config": config_manager._config_to_dict(config)
            }
        elif project_path:
            # Get project-specific config
            config = config_manager.get_project_config(project_path)

            return {
                "success": True,
                "config_type": "project",
                "project_path": project_path,
                "config": config_manager._config_to_dict(config)
            }
        else:
            # Get global config
            config = config_manager.get_global_config()

            return {
                "success": True,
                "config_type": "global",
                "config": config_manager._config_to_dict(config)
            }

    except Exception as e:
        LOGGER.error(f"Error retrieving file action config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/file-actions/config")
async def file_action_save_config(request: Request):
    """
    Save file action configuration

    Request body should contain:
    - config_type: "global" or "project"
    - project_path: Required if config_type is "project"
    - config: Configuration object or overrides
    """
    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        config_type = data.get("config_type", "global")
        config_data = data.get("config", {})

        from client_server.utils.file_action.file_action_config import get_config_manager
        config_manager = get_config_manager()

        if config_type == "global":
            # Save global configuration
            config = config_manager._dict_to_config(config_data)
            success = config_manager.save_global_config(config)

            if not success:
                raise HTTPException(status_code=500, detail="Failed to save global configuration")

            return {
                "success": True,
                "config_type": "global",
                "message": "Global configuration saved successfully"
            }

        elif config_type == "project":
            # Save project configuration
            project_path = data.get("project_path")
            if not project_path:
                raise HTTPException(status_code=400, detail="project_path is required for project configuration")

            success = config_manager.save_project_config(project_path, config_data)

            if not success:
                raise HTTPException(status_code=500, detail="Failed to save project configuration")

            return {
                "success": True,
                "config_type": "project",
                "project_path": project_path,
                "message": "Project configuration saved successfully"
            }
        else:
            raise HTTPException(status_code=400, detail="Invalid config_type. Must be 'global' or 'project'")

    except HTTPException:
        raise
    except ValidationError as e:
        LOGGER.error(f"Validation error in save config: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid configuration data: {e}")
    except Exception as e:
        LOGGER.error(f"Error saving file action config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("GET", "/file-actions/config/projects")
async def file_action_get_project_configs(request: Request = None):
    """
    Get all project configurations

    Returns a list of all project-specific configurations.
    """
    try:
        from client_server.utils.file_action.file_action_config import get_config_manager
        config_manager = get_config_manager()

        project_configs = config_manager.get_all_project_configs()

        return {
            "success": True,
            "project_configs": project_configs,
            "count": len(project_configs)
        }

    except Exception as e:
        LOGGER.error(f"Error retrieving project configs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("DELETE", "/file-actions/config/projects/{project_path}")
async def file_action_delete_project_config(request: Request):
    """
    Delete project-specific configuration

    Removes the project configuration and falls back to global configuration.
    """
    try:
        # Extract project_path from URL (it may contain slashes, so we need to handle it carefully)
        # For now, we'll expect it to be URL-encoded
        import urllib.parse
        path_parts = request.url.path.split('/file-actions/config/projects/')
        if len(path_parts) < 2:
            raise HTTPException(status_code=400, detail="Invalid project path")

        project_path = urllib.parse.unquote(path_parts[1])

        from client_server.utils.file_action.file_action_config import get_config_manager
        config_manager = get_config_manager()

        success = config_manager.delete_project_config(project_path)

        if not success:
            raise HTTPException(status_code=404, detail="Project configuration not found")

        return {
            "success": True,
            "project_path": project_path,
            "message": "Project configuration deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error deleting project config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@route("POST", "/file-actions/config/clear-cache")
async def file_action_clear_config_cache(request: Request = None):
    """
    Clear configuration cache

    Forces reload of all configurations from storage.
    """
    try:
        from client_server.utils.file_action.file_action_config import get_config_manager
        config_manager = get_config_manager()

        config_manager.clear_cache()

        return {
            "success": True,
            "message": "Configuration cache cleared successfully"
        }

    except Exception as e:
        LOGGER.error(f"Error clearing config cache: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


