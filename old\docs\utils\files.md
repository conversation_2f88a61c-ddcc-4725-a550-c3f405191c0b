# File Utilities (`files.py`)

The `files.py` module provides utilities for discovering and filtering files within a directory. It is particularly useful for scanning a codebase to find relevant source code files while ignoring binaries, temporary files, and other non-essential assets.

## Functions

### `is_text_file(file_path, blocksize=512)`

This function checks if a given file is a text file.

- **Parameters:**
  - `file_path` (str): The path to the file to check.
  - `blocksize` (int): The number of bytes to read from the beginning of the file to perform the check. Defaults to 512.
- **Returns:**
  - `bool`: `True` if the file is likely a text file, `False` otherwise.

It operates by reading a small block from the file and checking for characteristics of binary files, such as the presence of `NULL` bytes or a high proportion of non-text characters.

### `get_files(data=None)`

This function recursively walks through a directory and returns a list of all text-readable files. It has built-in logic to exclude common directories and file types that are not typically part of a project's source code.

- **Parameters:**
  - `data` (dict): A dictionary containing the root path to search. It should have a key `"root"` with the path string.
- **Returns:**
  - `list[dict]`: A list of dictionaries, where each dictionary represents a file and contains its `path` and `name`.

#### Exclusions

The function is configured to exclude:

- **Directories:** Common directories like `.git`, `node_modules`, `venv`, `__pycache__`, build artifacts, and IDE-specific folders.
- **Files:** Configuration files like `.gitignore`, `.env`, and hidden files like `.DS_Store`.
- **Extensions:** A wide range of binary and non-code file extensions, such as `.png`, `.jpg`, `.zip`, `.exe`, etc.
- **File Size:** Files larger than 100 MB.
- **MIME Types:** It uses a list of known text-based MIME types to quickly identify text files. For unknown types, it falls back to `is_text_file`.
