from itertools import chain
import os
import uuid
import time
import async<PERSON>
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, Future
import traceback
from typing import Any, Callable, Coroutine

from . import IChunker
from overrides import override
from client_server.utils.chunkers.utils import (
    make_qdrant_knowledgebase_chunks_from_file,
)
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_file_stats, log_memory_usage
from client_server.utils.files import get_files
from client_server.utils.models.knowledgebase import (
    QdrantGithubMetadata,
    QdrantKnowledgeBaseChunk,
)


class GithubChunker(IChunker):
    def __init__(self, metadata: QdrantGithubMetadata):
        self.metadata = metadata

    @override
    async def process(
        self,
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        """Process the GitHub repository and return list of files."""
        LOGGER.info("Processing Github type knowledge base")

        await self._clone_repository()
        files = self._discover_files()

        chunks = await self._make_chunks(files, progress_callback)
        return chunks

    async def _clone_repository(self):
        """Clone the repository."""
        target_path = self.metadata.get_repo_dir() / str(uuid.uuid4())
        LOGGER.info(f"Repository will be cloned to: {target_path}")
        try:
            clone_command = self.metadata.make_git_clone_command(target_path.as_posix())
            LOGGER.info(f"Executing git clone command: {' '.join(clone_command)}")

            clone_start = time.time()
            process = await asyncio.create_subprocess_exec(
                *clone_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await process.communicate()
            clone_time = time.time() - clone_start

            if process.returncode == 0:
                LOGGER.info(
                    f"Git repository cloned successfully in {clone_time:.2f} seconds to {target_path}"
                )
                if stdout:
                    LOGGER.debug(f"Git clone stdout: {stdout.decode()[:500]}...")
                if stderr:
                    LOGGER.debug(f"Git clone stderr: {stderr.decode()[:500]}...")
            else:
                error_output = stderr.decode() if stderr else "No error output"
                LOGGER.error(
                    f"Error cloning repository. Return code: {process.returncode}"
                )
                LOGGER.error(f"Command error: {error_output}")

                if (
                    "git" in error_output.lower()
                    and "not found" in error_output.lower()
                ):
                    raise RuntimeError("Git CLI not found. Please install Git.")
                else:
                    raise RuntimeError(f"Error cloning repository: {error_output}")

        except Exception as e:
            LOGGER.error(f"Error cloning repository: {e}")
            LOGGER.error(f"Error traceback: {traceback.format_exc()}")
            raise RuntimeError(f"Error cloning repository: {e}")

    def _discover_files(self) -> list[str]:
        """Discover files in the cloned repository."""
        file_discovery_start = time.time()
        files = list(
            map(
                lambda x: x["path"],
                get_files(data={"root": self.metadata.get_repo_dir()}),
            )
        )
        file_discovery_time = time.time() - file_discovery_start

        LOGGER.info(
            f"Found {len(files)} files in cloned repository (discovery took {file_discovery_time:.2f}s)"
        )

        # Log repository statistics
        repo_size = sum(log_file_stats(f) for f in files)
        LOGGER.info(f"Repository size: {repo_size / 1024 / 1024:.2f} MB")

        return files

    async def _make_chunks(
        self,
        files: list[str],
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        LOGGER.info("Processing Github type knowledge base")
        LOGGER.info(f"Found {len(files)} files for github processing")

        # Log file type distribution
        file_extensions = {}
        total_size = 0
        for file_path in files:
            ext = os.path.splitext(file_path)[1] or "no_extension"
            file_extensions[ext] = file_extensions.get(ext, 0) + 1
            total_size += log_file_stats(file_path)

        LOGGER.info(f"File distribution: {dict(sorted(file_extensions.items()))}")
        LOGGER.info(f"Total github size: {total_size / 1024 / 1024:.2f} MB")

        try:
            chunking_phase_start = time.time()
            LOGGER.info("Starting chunk creation phase")
            log_memory_usage("before_chunking")

            MAX_WORKERS = int(os.cpu_count() * 0.4)
            chunks_by_file: dict[str, list[QdrantKnowledgeBaseChunk]] = {}
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                chunk_futures: dict[str, Future[list[QdrantKnowledgeBaseChunk]]] = {}
                # Dispatch all the futures
                dispatch_start = time.time()
                LOGGER.debug("Dispatching chunk creation tasks to thread pool")
                for file in files:
                    chunk_futures[file] = executor.submit(
                        make_qdrant_knowledgebase_chunks_from_file, file
                    )
                dispatch_time = time.time() - dispatch_start
                LOGGER.debug(
                    f"Dispatched {len(chunk_futures)} chunk creation tasks in {dispatch_time:.2f}s"
                )

                # Collect the results
                collection_start = time.time()
                completed_files = 0
                for i, (file, chunk_future) in enumerate(chunk_futures.items()):
                    file_start = time.time()
                    # Wait for the future to complete
                    chunks_by_file[file] = chunk_future.result()
                    file_time = time.time() - file_start
                    completed_files += 1

                    file_chunk_count = len(chunks_by_file[file])
                    LOGGER.debug(
                        f"Completed chunking for file {completed_files}/{len(chunk_futures)}: {file} "
                        f"({file_chunk_count} chunks, took {file_time:.2f}s)"
                    )

                    progress = i / len(chunk_futures) * 100
                    LOGGER.info(f"Progress: {progress:.2f}%")
                    if progress_callback:
                        await progress_callback(progress)

                collection_time = time.time() - collection_start
                LOGGER.debug(
                    f"Chunk collection completed in {collection_time:.2f} seconds"
                )

            total_chunks = len(list(chain(*chunks_by_file.values())))
            chunking_phase_time = time.time() - chunking_phase_start

            LOGGER.info(
                f"Chunk creation completed. Total chunks created: {total_chunks} in {chunking_phase_time:.2f}s"
            )
            log_memory_usage("after_chunking")

            if total_chunks == 0:
                LOGGER.error("No chunks found after processing all files")
                raise RuntimeError("No chunks found")

            # Log chunking statistics
            files_with_chunks = sum(
                1 for chunks_list in chunks_by_file.values() if len(chunks_list) > 0
            )
            files_without_chunks = len(chunks_by_file) - files_with_chunks
            avg_chunks_per_file = (
                total_chunks / files_with_chunks if files_with_chunks > 0 else 0
            )

            LOGGER.info(
                f"Chunking stats - Files with chunks: {files_with_chunks}, "
                f"Files without chunks: {files_without_chunks}, "
                f"Avg chunks per file: {avg_chunks_per_file:.1f}"
            )

            return list(chain(*chunks_by_file.values()))

        except Exception as e:
            LOGGER.error(f"Error processing files: {e}")
            LOGGER.error(f"Error traceback: {traceback.format_exc()}")
            log_memory_usage("error_state")
            return []
