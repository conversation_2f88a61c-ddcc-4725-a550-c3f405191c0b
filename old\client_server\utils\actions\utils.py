import json
from typing import Any

from client_server.core.logger import LOGGER
import socketio
import copy

# -----------------------------------------------------------------------------
# Create action messages
# -----------------------------------------------------------------------------


def create_action_messages(
    tool_id: str,
    action_name: str,
    action_arguments: dict[str, str],
    search_results: list[dict[str, Any]],
):
    """Create formatted action messages"""
    actions_content = ""
    for search_result in search_results:
        actions_content += search_result["content"]["text"] + "\n"
    try:
        return [
            {
                "role": "assistant",
                "action": {
                    "id": tool_id,
                    "name": action_name,
                    "arguments": action_arguments,
                },
            },
            {
                "role": "action",
                "content": actions_content,
                "action_id": tool_id,
            },
        ]

    except Exception as e:
        LOGGER.error(f"Error in create_action_messages: {e}")
        raise


# -----------------------------------------------------------------------------


class SearchReferences:
    def __init__(self, request_id: str):
        self.search_results = {"request_id": request_id, "results": []}

    def add_search_result(self, path: str, type: str, name: str, content: str):
        self.search_results["results"].append(
            {"path": path, "type": type, "name": name, "content": content}
        )

    def get_search_result(self):
        return copy.deepcopy(self.search_results)
