from datetime import datetime, timedelta
import json
import os
import subprocess
import httpx
import platform
import psutil
from fastapi import H<PERSON><PERSON>Ex<PERSON>, Request, Response

from . import route
from client_server.utils.interet_check import check_internet_connection
from client_server.utils.path_selector import PathSelector
from client_server.core.logger import LOGGER
from client_server.core.state import (
    G_CLIENT_SERVER_VERSION,
    G_EXTENSION_VERSION,
    G_SESSION_ID,
)

REPORT_URL = "https://backend.v3.codemate.ai/report?source=client_server"


def get_system_info() -> str:
    """Gather system information and return as formatted string"""
    try:
        # CPU Info
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()

        # Memory Info
        memory = psutil.virtual_memory()

        # Network Info
        network_connected = check_internet_connection()

        # OS Info
        os_name = platform.system()
        os_mapping = {"Darwin": "Mac", "Windows": "Win", "Linux": "Linux"}
        os_display = os_mapping.get(os_name, os_name)

        # Version Info
        client_server_version = G_CLIENT_SERVER_VERSION.get() or "Unknown"
        extension_version = G_EXTENSION_VERSION.get() or "Unknown"
        vscode_version = "Unknown"
        try:
            subprocess.run(
                ["code", "--version"],
                capture_output=True,
                text=True,
                shell=False,
            )
            vscode_version = (
                subprocess.run(
                    ["code", "--version"],
                    capture_output=True,
                    text=True,
                    shell=False,
                )
                .stdout.strip()
                .split("\n")[0]
            )
        except Exception as e:
            LOGGER.error(f"Error getting VS Code version: {e}")
            vscode_version = "Unknown"

        return f"""
---
CPU INFORMATION:
ARCH: {platform.machine()}
NAME: {platform.processor()}
CURRENT USAGE: {cpu_percent}%
CORES: {cpu_count}
---
MEMORY:
TOTAL AVAILABLE: {memory.total / (1024 * 1024 * 1024):.2f} GB
CURRENT USAGE: {memory.percent}%
---
NETWORK
CONNECTED: {str(network_connected).lower()}
---
OS: {os_display}
CLIENT SERVER VERSION: {client_server_version}
EXTENSION VERSION: {extension_version}
VS CODE VERSION: {vscode_version}
---
""".strip()
    except Exception as e:
        LOGGER.error(f"Error gathering system info: {e}")
        return "Error gathering system information\n\n"


def get_vscode_state() -> str:
    """Get the state of the VS Code extension"""
    try:
        # Get the state of the VS Code extension
        with open(PathSelector.get_base_path() / "state.json", "r") as f:
            state = json.load(f)
            return f"---\nVS CODE STATE: {state}\n---"
    except Exception:
        return "---\nVS CODE STATE: Unknown\n---"


@route("GET", "/report_to_cloud")
async def report_to_cloud(request: Request = None):
    """Send error logs to cloud as JSON"""
    start_time = datetime.now()
    LOGGER.info(f"Processing log report request")

    try:
        log_file = PathSelector.get_logs_path() / "logs.log"
        if not log_file.exists():
            LOGGER.warning("No log file found")
            raise HTTPException(status_code=404, detail="No log file found")

        # Get logs from last 7 days
        n_days_ago = datetime.now() - timedelta(days=7)
        recent_logs = []

        with open(log_file, "r", encoding="utf-8") as f:
            for line in f:
                try:
                    # Assuming ISO format timestamp at start of each log line
                    log_time_str = line.split()[0]
                    log_time = datetime.fromisoformat(log_time_str)
                    if log_time >= n_days_ago:
                        recent_logs.append(line.strip())
                except (ValueError, IndexError):
                    continue

        if not recent_logs:
            LOGGER.warning("No logs found in the last 7 days")
            raise HTTPException(
                status_code=404, detail="No logs found in the last 7 days"
            )

        # Prepare JSON payload with system info and logs
        system_info = get_system_info()
        vscode_state = get_vscode_state()
        logs_content = "\n".join(recent_logs)
        combined_content = f"{system_info}\n\n\n{vscode_state}\n\n\n{logs_content}"

        payload = {"session_id": G_SESSION_ID.get(), "log": combined_content}
        # Send JSON request
        with httpx.Client(timeout=300.0) as client:
            response = client.post(REPORT_URL, json=payload)
            response.raise_for_status()

        total_time = (datetime.now() - start_time).total_seconds()
        LOGGER.info(f"Log report request completed in {total_time:.3f}s")

        return Response(content="OK", media_type="text/plain")
    except HTTPException:
        raise

    except Exception as e:
        total_time = (datetime.now() - start_time).total_seconds()
        LOGGER.error(f"Error reporting logs to cloud after {total_time:.3f}s: {e}")
        raise HTTPException(status_code=500, detail=str(e))
