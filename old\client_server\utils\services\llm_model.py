def get_llm_model_name(choice: str | None = None) -> str:
    """
    Returns appropriate LLM model name based on the thinking mode or action type.

    Args:
        choice: The type of thinking or action ('slow_thinking', 'fast_thinking',
               'action', 'security', etc.)

    Returns:
        str: The model name to use for the given choice
    """
    # Define model mappings for different choices
    model_mappings = [
        (['<slow_thinking>', '<fast_thinking>', '<calculative_thinking>', 'followup'], 'gpt-4o'),
        (['codelens_edit', 'codelens_debug', 'codelens_test', 'codelens_optimize', 'codelens_explain', "<image_chat>"], 'gpt-4.1-mini'),
        (['document', 'security-scan', 'fix-suggestions', 'review', 'auto-apply'], 'gpt-4.1-mini')
    ]

    # Find the appropriate model for the given choice
    if choice:
        for choices, model in model_mappings:
            if choice in choices:
                return model

    # Return default model if no match found
    return 'gpt-4o-mini'

