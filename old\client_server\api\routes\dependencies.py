from client_server.services.dependencies.utils import DependencyRegistry
from client_server.utils.platform_detector import PlatformDetector
from client_server.utils.resource import SystemResource

from . import route


def prepare_common_dependencies():
    return []
    deps = []
    if PlatformDetector.is_snapdragon_arm():
        pass
    else:
        deps.append(DependencyRegistry.get_dependency_by_id("embedding_model"))
        # # Chat model for eco mode:
        # # ---- RAM: 3 GB, VRAM: 3 GB
        # ram_size = SystemResource.get_ram_size_in_gb()
        # vram_size = SystemResource.get_vram_size_in_gb()
        # if ram_size >= 3 or vram_size >= 3:
        #     deps.append(DependencyRegistry.get_dependency_by_id("ollama_model_qwen_coder"))
    return deps


def prepare_eco_mode_dependencies():
    deps = []
    if PlatformDetector.is_snapdragon_arm():
        deps = [
            DependencyRegistry.get_dependency_by_id("inferx"),
            DependencyRegistry.get_dependency_by_id("inferx_all_models"),
        ]
    else:
        deps = [
            DependencyRegistry.get_dependency_by_id("ollama"),
            DependencyRegistry.get_dependency_by_id("ollama_model_embedding"),
        ]
        # Chat model for eco mode:
        # ---- RAM: 3 GB, VRAM: 3 GB
        ram_size = SystemResource.get_ram_size_in_gb()
        vram_size = SystemResource.get_vram_size_in_gb()
        if ram_size >= 3 or vram_size >= 3:
            deps.append(DependencyRegistry.get_dependency_by_id("ollama_model_qwen"))

    return deps


@route("GET", "/dependencies_status")
async def dependencies_status():
    result = [
        {
            "id": "common",
            "name": "Common dependencies",
            "dependencies": prepare_common_dependencies(),
        },
        {
            "id": "eco_mode",
            "name": "Eco mode dependencies",
            "dependencies": prepare_eco_mode_dependencies(),
        },
    ]

    for group in result:
        for i, dependency in enumerate(group["dependencies"]):
            group["dependencies"][i] = {
                "id": dependency.id,
                "name": dependency.name,
                "status": dependency.get_status().value,
                "version": dependency.version,
                "description": dependency.description,
            }

    return result
