from pathlib import Path
from typing import Generator, <PERSON><PERSON>, Any, Optional
import subprocess
import time
import os
from subprocess import Popen
import socket
from overrides import override
import ollama

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.utils.platform_detector import PlatformDetector
from . import IDependency, DependencyStatus
from .registry import DependencyRegistry


class OllamaModelDependency(IDependency):
    _server_process: Optional[Popen[str]] = None
    _api_base: str = "http://localhost:11434"

    def __init__(
        self,
        target_dir: Path,
        model_name: str,
        *,
        id: str | None = None,
        name: str | None = None,
        version: str | None = None,
        description: str | None = None,
    ):
        super().__init__()
        self.model_name = model_name
        self.target_dir = target_dir
        self._ollama_binary_path = self._get_binary_path(str(target_dir))

        # Store properties
        self._id = id or f"ollama_model_{self.model_name.replace('/', '_')}"
        self._name = name or self.model_name
        self._version = version or "latest"
        self._description = (
            description
            or f"Ollama model {model_name} for text generation and processing."
        )

    def _get_binary_path(self, ollama_base_path: str) -> str:
        """Get the platform-specific path to the Ollama binary."""
        binary_paths = {
            "windows": f"{ollama_base_path}/ollama.exe",
            "linux": f"{ollama_base_path}/bin/ollama",
            "darwin": f"{ollama_base_path}/ollama",
        }

        os_name = PlatformDetector.get_os_name()
        if os_name not in binary_paths:
            raise ValueError(f"Unsupported platform: {os_name}")
        return binary_paths[os_name]

    def _is_port_available(self, port: int = 11434) -> bool:
        """Check if the specified port is available."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return True
        except socket.error:
            return False

    def _is_running(self) -> bool:
        """Check if the Ollama server is running."""
        try:
            ollama.ps()
            return True
        except Exception:
            return False

    def _start_server(self):
        """Start the Ollama server if not already running"""
        if self._server_process is not None:
            return

        # Check if port is available
        if self._is_running():
            LOGGER.info("Ollama server is already running, using existing instance")
            return

        # Create .logs directory if it doesn't exist
        log_path = PathSelector.get_logs_path() / "ollama.log"
        LOGGER.info(f"Starting ollama server at {self._ollama_binary_path}")
        try:
            with open(log_path, "w") as log_file:
                env = os.environ.copy()
                env["OLLAMA_MODELS"] = str(self.target_dir)
                self._server_process = subprocess.Popen(
                    [self._ollama_binary_path, "serve"],
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    text=True,
                    env=env,
                    shell=False,
                )
                time.sleep(1)
                retry_count = 10
                while retry_count > 0:
                    try:
                        ollama.ps()
                        break
                    except Exception as e:
                        LOGGER.warning(
                            f"Failed to start ollama server, retrying... ({retry_count}/10): {e}"
                        )
                        retry_count -= 1
                        time.sleep(1)
                LOGGER.info("ollama server started successfully")
        except Exception as e:
            LOGGER.error(f"Failed to start ollama server: {e}")
            raise RuntimeError(f"Failed to start ollama server: {e}")

    def _check_model_exists(self) -> bool:
        """Check if the model exists in Ollama."""
        try:
            # Check if server is running by trying to connect
            try:
                ollama.ps()
            except Exception:
                LOGGER.warning("Ollama server is not running")
                return False

            # If server is running, check if model exists
            ollama.show(self.model_name)
            return True
        except Exception as e:
            LOGGER.warning(f"Model check failed: {e}")
            return False

    @override
    def get_status(self) -> DependencyStatus:
        """Get the current status of the Ollama model."""
        # Use DependencyRegistry to get the stored status
        status_str = DependencyRegistry.get_status(self._id)

        # If status is None, determine it and update
        if status_str is None:
            try:
                if not self._check_model_exists():
                    status = DependencyStatus.MISSING
                else:
                    status = DependencyStatus.READY

                # Update the status in the registry
                DependencyRegistry.update_status(self._id, status.value)
                return status
            except Exception as e:
                LOGGER.error(f"Error checking model status: {e}")
                status = DependencyStatus.ERROR
                DependencyRegistry.update_status(self._id, status.value)
                return status
        else:
            # Convert string status to enum
            return DependencyStatus(status_str)

    @override
    def install(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Install the Ollama model."""
        status = DependencyStatus.INSTALLING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, None

        try:
            # Ensure server is running
            if not self._is_running():
                LOGGER.info("Ollama server is not running, starting it")
                DependencyRegistry.update_status(
                    self._id, DependencyStatus.PREPARING.value
                )
                yield DependencyStatus.PREPARING, "Starting ollama server"
                self._start_server()
                DependencyRegistry.update_status(
                    self._id, DependencyStatus.PREPARING.value
                )
                yield DependencyStatus.PREPARING, "Ollama server started"

            # Create a generator for the pull operation
            last_progress = -1
            for progress in ollama.pull(self.model_name, stream=True):
                if "completed" in progress and "total" in progress:
                    completed = int(progress["completed"])
                    total = int(progress["total"])
                    percentage = round((completed / total) * 100)
                    if percentage != last_progress:
                        last_progress = percentage
                        yield status, percentage

            status = DependencyStatus.INSTALLED
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama model installation complete"

            status = DependencyStatus.READY
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama model is ready to use"
        except Exception as e:
            LOGGER.error(f"Failed to install Ollama model: {e}")
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to install Ollama model: {e}"

    @override
    def uninstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Uninstall the Ollama model."""
        if not self._check_model_exists():
            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama model is not installed"
            return

        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Removing Ollama model"

        try:
            # Ensure server is running
            if not self._is_running():
                LOGGER.info("Ollama server is not running, starting it")
                DependencyRegistry.update_status(
                    self._id, DependencyStatus.PREPARING.value
                )
                yield DependencyStatus.PREPARING, "Starting ollama server"
                self._start_server()
                DependencyRegistry.update_status(
                    self._id, DependencyStatus.PREPARING.value
                )
                yield DependencyStatus.PREPARING, "Ollama server started"
            ollama.delete(self.model_name)

            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama model has been uninstalled"
        except Exception as e:
            LOGGER.error(f"Failed to uninstall Ollama model: {e}")
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to uninstall Ollama model: {e}"

    def __del__(self):
        """Destructor to ensure cleanup"""
        if self._server_process is not None:
            self._server_process.terminate()
            self._server_process = None
            LOGGER.info("ollama server stopped")

    @property
    @override
    def id(self) -> str:
        return self._id

    @property
    @override
    def name(self) -> str:
        return self._name

    @property
    @override
    def version(self) -> str:
        return self._version

    @property
    @override
    def description(self) -> str:
        return self._description

    @override
    def reinstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Reinstall the Ollama model by first uninstalling and then installing."""
        yield from self.uninstall()
        yield from self.install()

    @override
    def ensure(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Ensure the Ollama model is installed and ready.
        First reports the current status, then handles installation if needed.
        Yields status updates during the process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """

        match self.get_status():
            case DependencyStatus.INSTALLED:
                yield DependencyStatus.INSTALLED, "Ollama model is already installed and ready"
                return
            case DependencyStatus.MISSING | DependencyStatus.ERROR:
                yield DependencyStatus.MISSING, "Ollama model is not installed"
                yield from self.install()
            case DependencyStatus.CORRUPTED:
                yield DependencyStatus.CORRUPTED, "Ollama model is corrupted"
                yield from self.reinstall()
