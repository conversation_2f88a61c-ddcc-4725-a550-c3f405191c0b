from overrides import override
from typing import List
import tiktoken

from client_server.core.logger import LOGGER
from . import ITokenizationBackend


class TiktokenBackend(ITokenizationBackend):
    """
    A tokenization service using OpenAI's tiktoken library.
    """

    _default_encoding: str = "cl100k_base"  # Default encoding used by ChatGPT models

    def __init__(self, default_encoding: str = None):
        self._default_encoding = default_encoding or self._default_encoding
        # Pre-load the default encoding
        try:
            tiktoken.get_encoding(self._default_encoding)
        except Exception as e:
            LOGGER.warning(
                f"Failed to initialize tiktoken with encoding {self._default_encoding}: {e}"
            )

    def _get_encoding(self, model: str = None):
        """Get the appropriate encoding based on the model name."""
        try:
            if model:
                # Try to get model-specific encoding
                return tiktoken.encoding_for_model(model)
            else:
                # Use default encoding
                return tiktoken.get_encoding(self._default_encoding)
        except Exception as e:
            LOGGER.warning(
                f"Error getting tiktoken encoding for {model or self._default_encoding}: {e}"
            )
            # Fallback to default encoding
            return tiktoken.get_encoding(self._default_encoding)

    @override
    def count_tokens(self, text: str, model: str = None) -> int:
        """Count the number of tokens in a text string using tiktoken."""
        if not text:
            return 0

        try:
            encoding = self._get_encoding(model)
            return len(encoding.encode(text))
        except Exception as e:
            LOGGER.warning(f"Error counting tokens with tiktoken: {e}")
            # Fallback to a rough character-based estimate
            return len(text) // 4  # Rough approximation

    @override
    def tokenize(self, text: str, model: str = None) -> List[int]:
        """Convert a string to tokens using tiktoken."""
        if not text:
            return []

        try:
            encoding = self._get_encoding(model)
            return encoding.encode(text)
        except Exception as e:
            LOGGER.warning(f"Error tokenizing with tiktoken: {e}")
            return []

    @override
    def detokenize(self, tokens: List[int], model: str = None) -> str:
        """Convert tokens back to a string using tiktoken."""
        if not tokens:
            return ""

        try:
            encoding = self._get_encoding(model)
            return encoding.decode(tokens)
        except Exception as e:
            LOGGER.warning(f"Error detokenizing with tiktoken: {e}")
            return ""

    @override
    def truncate_to_token_limit(
        self, text: str, max_tokens: int, model: str = None
    ) -> str:
        """Truncate text to stay within a maximum token limit using tiktoken."""
        if not text or max_tokens <= 0:
            return ""

        try:
            encoding = self._get_encoding(model)
            tokens = encoding.encode(text)

            if len(tokens) <= max_tokens:
                return text

            # Get the truncated tokens
            truncated_tokens = tokens[:max_tokens]

            # Convert tokens back to text
            return encoding.decode(truncated_tokens)
        except Exception as e:
            LOGGER.warning(f"Error truncating text with tiktoken: {e}")
            # Fallback to character-based truncation (rough estimate)
            char_limit = max_tokens * 4  # Rough approximation
            return text[:char_limit]
