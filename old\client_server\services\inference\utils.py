from typing import Optional

from client_server.utils.platform_detector import PlatformDetector

from . import IInferenceBackend


class InferenceBuilder:
    """
    A builder class that selects and instantiates the appropriate inference backend
    based on the platform architecture.
    """

    _instance: Optional[IInferenceBackend] = None

    @staticmethod
    def create() -> IInferenceBackend:
        """
        Creates and returns the appropriate inference backend based on platform architecture.
        Returns InferX for ARM architecture, Ollama for others.

        Returns:
            IInferenceBackend: The appropriate inference backend instance
        """
        if InferenceBuilder._instance is None:
            # ARM
            if PlatformDetector.is_snapdragon_arm():
                from .inferx_handler import InferXInferenceBackend

                InferenceBuilder._instance = InferXInferenceBackend()
            else:
                from .ollama_handler import OllamaInferenceBackend

                InferenceBuilder._instance = OllamaInferenceBackend()

        return InferenceBuilder._instance

    @staticmethod
    def dispose():
        """
        Disposes of the current inference backend instance if it exists.
        """
        if InferenceBuilder._instance is not None:
            InferenceBuilder._instance.dispose()
            InferenceBuilder._instance = None
