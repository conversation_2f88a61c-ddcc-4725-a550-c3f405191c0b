import asyncio
import json
import time
import traceback
import uuid
import os
from typing import Any, Async<PERSON>enerator

from fastapi import Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from client_server.api.events.chat import HTTPStreamEventEmitter
from client_server.api.events.connection import update_session_activity
from client_server.api.events.swagger import SwaggerRequestData
from client_server.core.logger.utils import log_operation_stats, log_api_call_stats
from client_server.utils.actions.swagger_search import _perform_swagger_search
from client_server.utils.actions.utils import SearchReferences
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_memory_usage
from client_server.services.swagger.generator import SwaggerSpecGenerator
from client_server.core.state import G_BASE_URL
from . import route
import requests
import re


class SwaggerStreamRequest(BaseModel):
    """Request model for HTTP streaming swagger generation"""
    swagger_file_path: str = Field(default="", description="Path to Swagger file")
    swagger_content: str = Field(default="", description="Raw Swagger content")
    swagger_url: str = Field(default="", description="URL to Swagger spec")
    swagger_knowledgebase_id: str = Field(default="", description="Knowledge base ID")
    client_side_language: str = Field(default="", description="Client language")
    custom_instructions: str = Field(default="", description="Custom instructions")
    base_url: str = Field(default="", description="Base URL for API")
    provider: str | dict[str, Any] = Field(..., description="Provider information")
    session__: str | None = None


async def mock_process_endpoint_http(
    endpoint, client_language, custom_instructions, event_emitter: HTTPStreamEventEmitter
):
    """HTTP version of mock_process_endpoint for streaming."""
    start_time = time.time()
    endpoint_path = endpoint.get("path", "unknown")
    LOGGER.info(f"Processing mock endpoint: {endpoint_path}")

    try:
        await event_emitter.emit(
            "swagger_gen:endpoint_start",
            data={"id": endpoint_path}
        )

        payload = {
            "required_imports": ["import requests"],
            "code": "print('Hello, world!')",
        }

        await event_emitter.emit(
            "swagger_gen:endpoint_success",
            data={
                "id": endpoint_path,
                "response": payload,
            }
        )

        total_time = time.time() - start_time
        LOGGER.debug(f"Mock endpoint processed in {total_time:.3f}s")
        return payload

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error processing mock endpoint {endpoint_path} after {total_time:.3f}s: {e}")
        await event_emitter.emit(
            "swagger_gen:endpoint_error",
            data={"id": endpoint_path, "error": str(e)}
        )
        raise


async def combine_imports(results):
    """Combine import statements from multiple results."""
    start_time = time.time()
    LOGGER.debug("Combining import statements")

    try:
        unique_imports = set()
        for result in results:
            if "required_imports" in result:
                unique_imports.update(result["required_imports"])

        total_time = time.time() - start_time
        LOGGER.debug(
            f"Combined {len(unique_imports)} unique imports in {total_time:.3f}s"
        )
        return list(unique_imports)

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error combining imports after {total_time:.3f}s: {e}")
        return []



def prepare_spec_file(payload: SwaggerStreamRequest):
    """Prepare Swagger specification file from various sources."""
    start_time = time.time()
    LOGGER.info("Preparing Swagger specification")

    try:
        generator = SwaggerSpecGenerator()
        endpoints = []

        if payload.swagger_file_path:
            LOGGER.info(f"Generating endpoints from file: {payload.swagger_file_path}")
            endpoints = generator.generate_from_file(payload.swagger_file_path)
        elif payload.swagger_content:
            LOGGER.info("Generating endpoints from content")
            try:
                swagger_dict = json.loads(payload.swagger_content)
                endpoints = generator.generate_from_content(swagger_dict)
            except json.JSONDecodeError as e:
                LOGGER.error(f"Invalid JSON content provided: {e}")
                return None
        elif payload.swagger_url:
            LOGGER.info(f"Generating endpoints from URL: {payload.swagger_url}")
            endpoints = generator.generate_from_url(payload.swagger_url)
        else:
            LOGGER.error("No swagger source provided")
            return None

        total_time = time.time() - start_time
        LOGGER.info(
            f"Swagger spec preparation completed - Endpoints: {len(endpoints)}, "
            f"Time: {total_time:.3f}s"
        )
        return endpoints

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error preparing Swagger spec after {total_time:.3f}s: {e}")
        return None


def format_response_object(response):
    """Format response object for consistent structure."""
    start_time = time.time()
    LOGGER.debug("Formatting response object")

    try:
        formatted = {}
        for key in response.keys():
            formatted[key] = {
                "description": response[key]["description"],
                "schema": response[key].get("schema", {}),
                "headers": response[key].get("headers", {}),
            }

        total_time = time.time() - start_time
        LOGGER.debug(f"Response formatting completed in {total_time:.3f}s")
        return formatted

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error formatting response after {total_time:.3f}s: {e}")
        return {}
    

def parse_prompt(input_text):
    structure_pattern = re.compile(r"```structure\s*(.*?)\s*```", re.DOTALL)
    structure_match = structure_pattern.search(input_text)

    if structure_match:
        structure = structure_match.group(1).strip()
        prompt = input_text.replace(structure_match.group(0), "").strip()
    else:
        structure = None
        prompt = input_text.strip()

    return prompt, structure



async def handle_swagger_spec_gen_event_http(
    event_emitter: HTTPStreamEventEmitter, payload: SwaggerRequestData
) -> AsyncGenerator[str, None]:
    """HTTP streaming version of handle_swagger_spec_gen_event."""
    start_time = time.time()
    LOGGER.info("Starting Swagger spec generation")

    try:
        generator = SwaggerSpecGenerator()
        endpoints = []

        # Get endpoints based on the provided input method
        content_start = time.time()
        if payload.swagger_file_path:
            LOGGER.info(f"Generating endpoints from file: {payload.swagger_file_path}")
            endpoints = generator.generate_from_file(
                payload.swagger_file_path, base_uri=payload.base_url
            )
        elif payload.swagger_content:
            LOGGER.info("Generating endpoints from content")
            try:
                file_name = f"swagger_temp_{uuid.uuid4()}.json"
                with open(file_name, "w+") as f:
                    f.write(payload.swagger_content)
                endpoints = generator.generate_from_file(
                    file_name,
                    base_uri=payload.base_url,
                )
                os.remove(file_name)
            except json.JSONDecodeError:
                LOGGER.error("Invalid JSON content provided")
                await event_emitter.emit(
                    "swagger_gen:error",
                    data="Invalid JSON content provided"
                )
                yield event_emitter.format_as_sse({
                    "event": "swagger_gen:error",
                    "data": "Invalid JSON content provided"
                })
                return
        elif payload.swagger_url:
            LOGGER.info(f"Generating endpoints from URL: {payload.swagger_url}")
            endpoints = generator.generate_from_url(payload.swagger_url)
        else:
            LOGGER.error("No swagger source provided")
            await event_emitter.emit(
                "swagger_gen:error",
                data="No swagger source provided"
            )
            yield event_emitter.format_as_sse({
                "event": "swagger_gen:error",
                "data": "No swagger source provided"
            })
            return

        content_time = time.time() - content_start
        LOGGER.info(f"Content processing completed in {content_time:.3f}s")
        LOGGER.info(f"Found {len(endpoints)} endpoints to process")

        if not endpoints:
            LOGGER.warning("No endpoints found in swagger specification")
            await event_emitter.emit("swagger_gen:success", data={})
            yield event_emitter.format_as_sse({
                "event": "swagger_gen:success",
                "data": {}
            })
            return

        # 2. Process each endpoint
        processing_start = time.time()
        results = []

        for i, endpoint in enumerate(endpoints):
            try:
                result = await mock_process_endpoint_http(
                    endpoint,
                    payload.client_side_language,
                    payload.custom_instructions,
                    event_emitter
                )
                
                # Yield the endpoint start event
                yield event_emitter.format_as_sse({
                    "event": "swagger_gen:endpoint_start",
                    "data": {"id": endpoint.get("path", "unknown")}
                })
                
                # Yield the endpoint success event
                yield event_emitter.format_as_sse({
                    "event": "swagger_gen:endpoint_success",
                    "data": {
                        "id": endpoint.get("path", "unknown"),
                        "response": result,
                    }
                })
                
                if result:
                    results.append(result)
                    
                # Small delay to prevent overwhelming the client
                await asyncio.sleep(0.001)
                
            except Exception as e:
                LOGGER.error(f"Error processing endpoint {i}: {e}")
                yield event_emitter.format_as_sse({
                    "event": "swagger_gen:endpoint_error",
                    "data": {"id": endpoint.get("path", "unknown"), "error": str(e)}
                })
                continue

        processing_time = time.time() - processing_start
        LOGGER.info(
            f"Endpoint processing completed - Success: {len(results)}, "
            f"Failed: {len(endpoints) - len(results)}, "
            f"Time: {processing_time:.3f}s"
        )

        # 3. Combine all the imports into a single chunk
        imports = await combine_imports(results)
        await event_emitter.emit(
            "swagger_gen:imports",
            data={"imports": imports}
        )
        yield event_emitter.format_as_sse({
            "event": "swagger_gen:imports",
            "data": {"imports": imports}
        })

        # 4. Emit swagger_gen:success
        await event_emitter.emit("swagger_gen:success", data={})
        yield event_emitter.format_as_sse({
            "event": "swagger_gen:success",
            "data": {}
        })

        total_time = time.time() - start_time
        log_operation_stats(
            "swagger_spec_gen", start_time, len(endpoints), success=True
        )
        LOGGER.info(f"Swagger spec generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error processing swagger request after {total_time:.3f}s: {e}")
        await event_emitter.emit("swagger_gen:error", data=str(e))
        yield event_emitter.format_as_sse({
            "event": "swagger_gen:error",
            "data": str(e)
        })
        raise


async def handle_swagger_prompt_gen_event_http(
    event_emitter: HTTPStreamEventEmitter, payload: SwaggerRequestData
) -> AsyncGenerator[str, None]:
    """HTTP streaming version of handle_swagger_prompt_gen_event."""
    start_time = time.time()
    LOGGER.info("Starting Swagger prompt generation")

    base_url = G_BASE_URL.get().general

    try:
        user_prompt, structure_prompt = parse_prompt(payload.custom_instructions)
        kbid = payload.swagger_knowledgebase_id
        LOGGER.info(f"Processing prompt for KB: {kbid}")

        # Generate queries
        query_start = time.time()
        response = requests.post(
            url=f"{base_url}/swagger/generate_queries", json={"query": user_prompt}
        )
        query_time = time.time() - query_start

        log_api_call_stats(
            f"{base_url}/swagger/generate_queries",
            "POST",
            response.status_code,
            query_time,
            len(response.content),
        )

        if response.status_code != 200:
            LOGGER.error(f"Error generating queries: {response.status_code}")
            await event_emitter.emit(
                "swagger_gen:error",
                data="Error generating queries"
            )
            yield event_emitter.format_as_sse({
                "event": "swagger_gen:error",
                "data": "Error generating queries"
            })
            return

        queries = response.json()["queries"]
        LOGGER.info(f"Generated {len(queries)} queries in {query_time:.3f}s")

        # Perform searches using the swagger search utility
        search_start = time.time()
        search_results = []
        for query in queries["queries"]:
            LOGGER.debug(f"Performing search with query: {query}")
            result = await _perform_swagger_search(
                query=query,
                index_name=kbid,
            )
            search_results.extend(result)

        search_time = time.time() - search_start
        LOGGER.debug(
            f"Search completed - Results: {len(search_results)}, "
            f"Time: {search_time:.3f}s"
        )

        # Process results to remove duplicates
        processing_start = time.time()
        final_results = []
        for res in search_results:
            if len(final_results) != 0:
                paths = [x["metadata"]["endpoint"]["path"] for x in final_results]
                if res["metadata"]["endpoint"]["path"] not in paths:
                    final_results.append(res)
            else:
                final_results.append(res)

        search_references = SearchReferences(request_id="")
        for res in final_results:
            search_references.add_search_result(
                path=res["metadata"]["endpoint"]["path"],
                type="docs",
                content=json.dumps(res["metadata"]["endpoint"]),
                name=res["metadata"]["endpoint"]["path"],
            )

        processing_time = time.time() - processing_start
        LOGGER.debug(
            f"Result processing completed - Unique results: {len(final_results)}, "
            f"Time: {processing_time:.3f}s"
        )

        await event_emitter.emit(
            "swagger_gen:references",
            data=search_references.get_search_result()
        )
        yield event_emitter.format_as_sse({
            "event": "swagger_gen:references",
            "data": search_references.get_search_result()
        })

        # Generate calls
        calls_start = time.time()
        calls_response = requests.post(
            f"{base_url}/swagger/generate_calls",
            json={
                "query": user_prompt,
                "search_results": final_results,
                "spec_endpoints": prepare_spec_file(payload)
            }
        )
        calls_time = time.time() - calls_start

        log_api_call_stats(
            f"{base_url}/swagger/generate_calls",
            "POST",
            calls_response.status_code,
            calls_time,
            len(calls_response.content),
        )

        if calls_response.status_code != 200:
            LOGGER.error(f"Error generating calls: {calls_response.status_code}")
            await event_emitter.emit(
                "swagger_gen:error",
                data="Error generating calls"
            )
            yield event_emitter.format_as_sse({
                "event": "swagger_gen:error",
                "data": "Error generating calls"
            })
            return

        calls = calls_response.json()["calls"]
        LOGGER.info(f"Generated {len(calls)} calls in {calls_time:.3f}s")

        # Process calls
        response_json = {"name": "Swagger API", "calls": []}

        for call in calls:
            path = call["path"]
            spec_data = next(
                (
                    item
                    for item in final_results
                    if item["metadata"]["endpoint"]["path"] == path
                ),
                None,
            )
            response_json["calls"].append(
                {
                    "name": spec_data["metadata"]["endpoint"]["operation_id"],
                    "sequence": call["sequence"],
                    "endpoint": spec_data["metadata"]["endpoint"]["path"],
                    "method": spec_data["metadata"]["endpoint"]["method"],
                    "params": spec_data["metadata"]["endpoint"]["parameters"],
                    "response": format_response_object(
                        spec_data["metadata"]["endpoint"]["responses"]
                    ),
                }
            )

        # Apply structure if provided
        if structure_prompt:
            LOGGER.debug("Applying structure to output")
            structure_start = time.time()
            res = requests.post(
                f"{base_url}/swagger/structure_output",
                json={"prompt": structure_prompt, "swagger_output": response_json},
            )
            structure_time = time.time() - structure_start

            log_api_call_stats(
                f"{base_url}/swagger/structure_output",
                "POST",
                res.status_code,
                structure_time,
                len(res.content),
            )

            response_json = res.json()
            LOGGER.debug(f"Structure applied in {structure_time:.3f}s")

        await event_emitter.emit("swagger_gen:kb_response", data=response_json)
        yield event_emitter.format_as_sse({
            "event": "swagger_gen:kb_response",
            "data": response_json
        })

        total_time = time.time() - start_time
        log_operation_stats(
            "swagger_prompt_gen", start_time, len(calls), success=True
        )
        LOGGER.info(f"Swagger prompt generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error in Swagger prompt generation after {total_time:.3f}s: {e}")
        await event_emitter.emit("swagger_gen:error", data=str(e))
        yield event_emitter.format_as_sse({
            "event": "swagger_gen:error",
            "data": str(e)
        })
        raise


@route("POST", "/swagger/stream")
async def swagger_stream(request: Request):
    """
    HTTP streaming endpoint for swagger generation functionality.
    Replaces WebSocket swagger events with Server-Sent Events (SSE).
    """
    start_time = time.time()
    log_memory_usage("http_swagger_start")
    
    LOGGER.info("Starting HTTP swagger stream request")
    
    try:
        # Parse request body
        body = await request.body()
        request_data = json.loads(body.decode())
        
        # Extract session ID from headers or payload
        session_id = request.headers.get("x-session") or request_data.get("session__", "")
        if session_id:
            update_session_activity(session_id)
        
        # Validate request data
        validation_start = time.time()
        payload = SwaggerRequestData.model_validate(request_data)
        validation_time = time.time() - validation_start
        
        LOGGER.info(
            f"Swagger payload validated - "
            f"KB ID: {payload.swagger_knowledgebase_id}, "
            f"Validation time: {validation_time:.3f}s"
        )
        
        # Create event emitter for this request
        request_id = f"swagger_{int(time.time() * 1000)}"
        event_emitter = HTTPStreamEventEmitter(request_id)
        
        async def generate_swagger_stream():
            try:
                # Route to appropriate handler based on knowledge base ID
                if payload.swagger_knowledgebase_id == "":
                    LOGGER.info("Handling Swagger spec generation")
                    async for event in handle_swagger_spec_gen_event_http(event_emitter, payload):
                        yield event
                else:
                    LOGGER.info("Handling Swagger prompt generation")
                    async for event in handle_swagger_prompt_gen_event_http(event_emitter, payload):
                        yield event
                
                total_time = time.time() - start_time
                log_memory_usage("http_swagger_complete")

                # Update session activity again after successful completion
                if session_id:
                    update_session_activity(session_id)
                
                LOGGER.info(
                    f"HTTP swagger stream completed successfully - "
                    f"Total time: {total_time:.2f}s"
                )
                
            except Exception as e:
                total_time = time.time() - start_time
                LOGGER.error(f"Error in HTTP swagger stream after {total_time:.2f}s: {e}")
                LOGGER.error(f"HTTP swagger error traceback: {traceback.format_exc()}")
                log_memory_usage("http_swagger_error")
                
                # Send error event
                error_event = {
                    "event": "swagger_gen:error",
                    "data": "An error occurred while processing the swagger request."
                }
                yield f"event: swagger_gen:error\ndata: {json.dumps(error_event['data'])}\n\n"
        
        return StreamingResponse(
            generate_swagger_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )
        
    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error setting up HTTP swagger stream after {total_time:.2f}s: {e}")
        LOGGER.error(f"HTTP swagger setup error traceback: {traceback.format_exc()}")
        
        # Return error response
        error_response = {
            "error": "Failed to process swagger request",
            "details": str(e)
        }
        
        async def error_stream():
            yield f"event: swagger_gen:error\ndata: {json.dumps(error_response)}\n\n"
        
        return StreamingResponse(
            error_stream(),
            media_type="text/plain",
            status_code=500
        )
