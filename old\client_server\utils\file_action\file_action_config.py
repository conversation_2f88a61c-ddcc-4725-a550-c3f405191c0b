"""
Configuration and Storage Integration for File Actions

This module integrates with PathSelector for user preferences, database storage
for analysis results and fix history, and per-project configuration overrides.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, field

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.services.db_handler import Database
from client_server.utils.prompts.file_action_prompts import AnalysisDepth, SafetyLevel
from client_server.utils.models.file_actions import SeverityLevel


@dataclass
class FileActionConfig:
    """Configuration for file action operations"""
    
    # Analysis settings
    default_analysis_depth: AnalysisDepth = AnalysisDepth.STANDARD
    default_safety_level: SafetyLevel = SafetyLevel.REVIEW_REQUIRED
    auto_apply_safe_fixes: bool = False
    create_backups: bool = True
    
    # Queue settings
    max_queue_workers: int = 3
    max_queue_size: int = 100
    rate_limit_per_minute: int = 60
    enable_rate_limiting: bool = True
    
    # Security settings
    enable_security_scanning: bool = True
    security_scan_depth: AnalysisDepth = AnalysisDepth.STANDARD
    min_cvss_score_to_report: float = 4.0
    
    # Documentation settings
    auto_generate_docs: bool = False
    doc_types_enabled: List[str] = field(default_factory=lambda: ["inline", "comments"])
    include_examples_in_docs: bool = True
    
    # Review settings
    review_focus_areas: List[str] = field(default_factory=lambda: ["security", "performance", "style"])
    min_confidence_for_suggestions: float = 0.7
    max_issues_per_file: int = 50
    
    # File type specific settings
    file_type_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Notification settings
    notify_on_completion: bool = True
    notify_on_high_severity: bool = True
    notification_severity_threshold: SeverityLevel = SeverityLevel.HIGH
    
    # Storage settings
    store_analysis_results: bool = True
    store_fix_history: bool = True
    cleanup_old_results_days: int = 30
    
    # VS Code integration settings
    auto_trigger_on_save: bool = False
    auto_trigger_on_focus: bool = False
    debounce_delay_ms: int = 1000
    
    # Project-specific overrides
    project_overrides: Dict[str, Dict[str, Any]] = field(default_factory=dict)


class FileActionConfigManager:
    """
    Manages configuration for file actions with user preferences and project overrides
    """
    
    def __init__(self):
        self.db = Database()
        self.config_collection = self.db["file_action_config"]
        self.project_configs_collection = self.db["project_file_action_configs"]
        
        # Cache for loaded configurations
        self._global_config_cache: Optional[FileActionConfig] = None
        self._project_config_cache: Dict[str, FileActionConfig] = {}
        
        LOGGER.info("FileActionConfigManager initialized")
    
    def _get_default_config(self) -> FileActionConfig:
        """Get default configuration"""
        return FileActionConfig()
    
    def _load_global_config_from_settings(self) -> Dict[str, Any]:
        """Load global configuration from settings.json"""
        try:
            settings = PathSelector._load_settings()
            return settings.get("file_actions", {})
        except Exception as e:
            LOGGER.warning(f"Failed to load file action config from settings: {e}")
            return {}
    
    def _save_global_config_to_settings(self, config_dict: Dict[str, Any]):
        """Save global configuration to settings.json"""
        try:
            settings = PathSelector._load_settings()
            settings["file_actions"] = config_dict
            
            settings_file = PathSelector.get_settings_file()
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=2)
            
            LOGGER.info("Saved file action config to settings.json")
        except Exception as e:
            LOGGER.error(f"Failed to save file action config to settings: {e}")
    
    def _config_to_dict(self, config: FileActionConfig) -> Dict[str, Any]:
        """Convert FileActionConfig to dictionary"""
        return {
            "default_analysis_depth": config.default_analysis_depth.value,
            "default_safety_level": config.default_safety_level.value,
            "auto_apply_safe_fixes": config.auto_apply_safe_fixes,
            "create_backups": config.create_backups,
            "max_queue_workers": config.max_queue_workers,
            "max_queue_size": config.max_queue_size,
            "rate_limit_per_minute": config.rate_limit_per_minute,
            "enable_rate_limiting": config.enable_rate_limiting,
            "enable_security_scanning": config.enable_security_scanning,
            "security_scan_depth": config.security_scan_depth.value,
            "min_cvss_score_to_report": config.min_cvss_score_to_report,
            "auto_generate_docs": config.auto_generate_docs,
            "doc_types_enabled": config.doc_types_enabled,
            "include_examples_in_docs": config.include_examples_in_docs,
            "review_focus_areas": config.review_focus_areas,
            "min_confidence_for_suggestions": config.min_confidence_for_suggestions,
            "max_issues_per_file": config.max_issues_per_file,
            "file_type_configs": config.file_type_configs,
            "notify_on_completion": config.notify_on_completion,
            "notify_on_high_severity": config.notify_on_high_severity,
            "notification_severity_threshold": config.notification_severity_threshold.value,
            "store_analysis_results": config.store_analysis_results,
            "store_fix_history": config.store_fix_history,
            "cleanup_old_results_days": config.cleanup_old_results_days,
            "auto_trigger_on_save": config.auto_trigger_on_save,
            "auto_trigger_on_focus": config.auto_trigger_on_focus,
            "debounce_delay_ms": config.debounce_delay_ms,
            "project_overrides": config.project_overrides
        }
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> FileActionConfig:
        """Convert dictionary to FileActionConfig"""
        config = self._get_default_config()
        
        # Update config with values from dictionary
        if "default_analysis_depth" in config_dict:
            config.default_analysis_depth = AnalysisDepth(config_dict["default_analysis_depth"])
        if "default_safety_level" in config_dict:
            config.default_safety_level = SafetyLevel(config_dict["default_safety_level"])
        if "auto_apply_safe_fixes" in config_dict:
            config.auto_apply_safe_fixes = config_dict["auto_apply_safe_fixes"]
        if "create_backups" in config_dict:
            config.create_backups = config_dict["create_backups"]
        if "max_queue_workers" in config_dict:
            config.max_queue_workers = config_dict["max_queue_workers"]
        if "max_queue_size" in config_dict:
            config.max_queue_size = config_dict["max_queue_size"]
        if "rate_limit_per_minute" in config_dict:
            config.rate_limit_per_minute = config_dict["rate_limit_per_minute"]
        if "enable_rate_limiting" in config_dict:
            config.enable_rate_limiting = config_dict["enable_rate_limiting"]
        if "enable_security_scanning" in config_dict:
            config.enable_security_scanning = config_dict["enable_security_scanning"]
        if "security_scan_depth" in config_dict:
            config.security_scan_depth = AnalysisDepth(config_dict["security_scan_depth"])
        if "min_cvss_score_to_report" in config_dict:
            config.min_cvss_score_to_report = config_dict["min_cvss_score_to_report"]
        if "auto_generate_docs" in config_dict:
            config.auto_generate_docs = config_dict["auto_generate_docs"]
        if "doc_types_enabled" in config_dict:
            config.doc_types_enabled = config_dict["doc_types_enabled"]
        if "include_examples_in_docs" in config_dict:
            config.include_examples_in_docs = config_dict["include_examples_in_docs"]
        if "review_focus_areas" in config_dict:
            config.review_focus_areas = config_dict["review_focus_areas"]
        if "min_confidence_for_suggestions" in config_dict:
            config.min_confidence_for_suggestions = config_dict["min_confidence_for_suggestions"]
        if "max_issues_per_file" in config_dict:
            config.max_issues_per_file = config_dict["max_issues_per_file"]
        if "file_type_configs" in config_dict:
            config.file_type_configs = config_dict["file_type_configs"]
        if "notify_on_completion" in config_dict:
            config.notify_on_completion = config_dict["notify_on_completion"]
        if "notify_on_high_severity" in config_dict:
            config.notify_on_high_severity = config_dict["notify_on_high_severity"]
        if "notification_severity_threshold" in config_dict:
            config.notification_severity_threshold = SeverityLevel(config_dict["notification_severity_threshold"])
        if "store_analysis_results" in config_dict:
            config.store_analysis_results = config_dict["store_analysis_results"]
        if "store_fix_history" in config_dict:
            config.store_fix_history = config_dict["store_fix_history"]
        if "cleanup_old_results_days" in config_dict:
            config.cleanup_old_results_days = config_dict["cleanup_old_results_days"]
        if "auto_trigger_on_save" in config_dict:
            config.auto_trigger_on_save = config_dict["auto_trigger_on_save"]
        if "auto_trigger_on_focus" in config_dict:
            config.auto_trigger_on_focus = config_dict["auto_trigger_on_focus"]
        if "debounce_delay_ms" in config_dict:
            config.debounce_delay_ms = config_dict["debounce_delay_ms"]
        if "project_overrides" in config_dict:
            config.project_overrides = config_dict["project_overrides"]
        
        return config
    
    def get_global_config(self) -> FileActionConfig:
        """Get global file action configuration"""
        if self._global_config_cache is not None:
            return self._global_config_cache
        
        try:
            # Try to load from settings.json first
            settings_config = self._load_global_config_from_settings()
            
            # Try to load from database
            db_config_record = self.config_collection.find_one({"type": "global"})
            db_config = db_config_record.get("config", {}) if db_config_record else {}
            
            # Merge configurations (database takes precedence)
            merged_config = {**settings_config, **db_config}
            
            # Convert to FileActionConfig object
            config = self._dict_to_config(merged_config)
            
            # Cache the configuration
            self._global_config_cache = config
            
            LOGGER.info("Loaded global file action configuration")
            return config
            
        except Exception as e:
            LOGGER.error(f"Error loading global config, using defaults: {e}")
            config = self._get_default_config()
            self._global_config_cache = config
            return config
    
    def save_global_config(self, config: FileActionConfig) -> bool:
        """Save global file action configuration"""
        try:
            config_dict = self._config_to_dict(config)
            
            # Save to database
            self.config_collection.update_one(
                {"type": "global"},
                {"$set": {"config": config_dict, "updated_at": time.time()}},
                upsert=True
            )
            
            # Save to settings.json
            self._save_global_config_to_settings(config_dict)
            
            # Update cache
            self._global_config_cache = config
            
            LOGGER.info("Saved global file action configuration")
            return True
            
        except Exception as e:
            LOGGER.error(f"Error saving global config: {e}")
            return False

    def get_project_config(self, project_path: str) -> FileActionConfig:
        """
        Get file action configuration for a specific project

        Args:
            project_path: Path to the project directory

        Returns:
            FileActionConfig with project-specific overrides applied
        """
        # Normalize project path
        project_path = os.path.normpath(project_path)

        # Check cache first
        if project_path in self._project_config_cache:
            return self._project_config_cache[project_path]

        try:
            # Start with global configuration
            global_config = self.get_global_config()

            # Look for project-specific configuration file
            project_config_file = Path(project_path) / ".file_actions_config.json"
            project_overrides = {}

            if project_config_file.exists():
                try:
                    with open(project_config_file, 'r') as f:
                        project_overrides = json.load(f)
                    LOGGER.info(f"Loaded project config from {project_config_file}")
                except Exception as e:
                    LOGGER.warning(f"Failed to load project config file {project_config_file}: {e}")

            # Check database for project-specific overrides
            db_project_config = self.project_configs_collection.find_one({"project_path": project_path})
            if db_project_config:
                db_overrides = db_project_config.get("config", {})
                project_overrides = {**project_overrides, **db_overrides}

            # Apply project overrides to global config
            if project_overrides:
                global_config_dict = self._config_to_dict(global_config)
                merged_config_dict = {**global_config_dict, **project_overrides}
                project_config = self._dict_to_config(merged_config_dict)
            else:
                project_config = global_config

            # Cache the configuration
            self._project_config_cache[project_path] = project_config

            return project_config

        except Exception as e:
            LOGGER.error(f"Error loading project config for {project_path}, using global config: {e}")
            return self.get_global_config()

    def save_project_config(self, project_path: str, config_overrides: Dict[str, Any]) -> bool:
        """
        Save project-specific configuration overrides

        Args:
            project_path: Path to the project directory
            config_overrides: Dictionary of configuration overrides

        Returns:
            True if saved successfully
        """
        try:
            # Normalize project path
            project_path = os.path.normpath(project_path)

            # Save to database
            self.project_configs_collection.update_one(
                {"project_path": project_path},
                {
                    "$set": {
                        "config": config_overrides,
                        "updated_at": time.time()
                    }
                },
                upsert=True
            )

            # Optionally save to project config file
            project_config_file = Path(project_path) / ".file_actions_config.json"
            try:
                with open(project_config_file, 'w') as f:
                    json.dump(config_overrides, f, indent=2)
                LOGGER.info(f"Saved project config to {project_config_file}")
            except Exception as e:
                LOGGER.warning(f"Failed to save project config file: {e}")

            # Clear cache for this project
            self._project_config_cache.pop(project_path, None)

            LOGGER.info(f"Saved project configuration for {project_path}")
            return True

        except Exception as e:
            LOGGER.error(f"Error saving project config for {project_path}: {e}")
            return False

    def get_file_type_config(self, file_path: str, project_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Get file type specific configuration

        Args:
            file_path: Path to the file
            project_path: Optional project path for project-specific overrides

        Returns:
            File type specific configuration
        """
        # Get appropriate configuration
        if project_path:
            config = self.get_project_config(project_path)
        else:
            config = self.get_global_config()

        # Determine file type
        file_extension = Path(file_path).suffix.lower()
        file_name = Path(file_path).name.lower()

        # Check for specific file name configurations first
        if file_name in config.file_type_configs:
            return config.file_type_configs[file_name]

        # Check for extension-based configurations
        if file_extension in config.file_type_configs:
            return config.file_type_configs[file_extension]

        # Return default configuration
        return {}

    def detect_project_path(self, file_path: str) -> Optional[str]:
        """
        Detect project path from a file path by looking for common project indicators

        Args:
            file_path: Path to a file

        Returns:
            Project root path if detected, None otherwise
        """
        try:
            path = Path(file_path).resolve()

            # Common project indicators
            project_indicators = [
                ".git",
                ".gitignore",
                "package.json",
                "requirements.txt",
                "Cargo.toml",
                "go.mod",
                "pom.xml",
                "build.gradle",
                "composer.json",
                "Gemfile",
                ".project",
                ".vscode",
                "pyproject.toml",
                "setup.py",
                "CMakeLists.txt",
                "Makefile"
            ]

            # Walk up the directory tree
            current_path = path.parent if path.is_file() else path

            while current_path != current_path.parent:  # Not at root
                for indicator in project_indicators:
                    if (current_path / indicator).exists():
                        return str(current_path)
                current_path = current_path.parent

            return None

        except Exception as e:
            LOGGER.error(f"Error detecting project path for {file_path}: {e}")
            return None

    def get_effective_config(self, file_path: str) -> FileActionConfig:
        """
        Get effective configuration for a file (with project detection and file type overrides)

        Args:
            file_path: Path to the file

        Returns:
            Effective FileActionConfig for the file
        """
        # Detect project path
        project_path = self.detect_project_path(file_path)

        # Get base configuration (project or global)
        if project_path:
            config = self.get_project_config(project_path)
        else:
            config = self.get_global_config()

        # Apply file type specific overrides
        file_type_overrides = self.get_file_type_config(file_path, project_path)

        if file_type_overrides:
            # Create a copy of the config with file type overrides
            config_dict = self._config_to_dict(config)
            config_dict.update(file_type_overrides)
            config = self._dict_to_config(config_dict)

        return config

    def clear_cache(self):
        """Clear configuration cache"""
        self._global_config_cache = None
        self._project_config_cache.clear()
        LOGGER.info("Cleared file action configuration cache")

    def get_all_project_configs(self) -> List[Dict[str, Any]]:
        """Get all project configurations"""
        try:
            return list(self.project_configs_collection.find())
        except Exception as e:
            LOGGER.error(f"Error retrieving project configs: {e}")
            return []

    def delete_project_config(self, project_path: str) -> bool:
        """Delete project-specific configuration"""
        try:
            # Normalize project path
            project_path = os.path.normpath(project_path)

            # Delete from database
            result = self.project_configs_collection.delete_one({"project_path": project_path})

            # Clear cache
            self._project_config_cache.pop(project_path, None)

            LOGGER.info(f"Deleted project configuration for {project_path}")
            return result.deleted_count > 0

        except Exception as e:
            LOGGER.error(f"Error deleting project config for {project_path}: {e}")
            return False


# Global configuration manager instance
_global_config_manager: Optional[FileActionConfigManager] = None


def get_config_manager() -> FileActionConfigManager:
    """Get the global configuration manager instance (singleton)"""
    global _global_config_manager

    if _global_config_manager is None:
        _global_config_manager = FileActionConfigManager()

    return _global_config_manager


def get_config_for_file(file_path: str) -> FileActionConfig:
    """
    Convenience function to get effective configuration for a file

    Args:
        file_path: Path to the file

    Returns:
        Effective FileActionConfig for the file
    """
    return get_config_manager().get_effective_config(file_path)
