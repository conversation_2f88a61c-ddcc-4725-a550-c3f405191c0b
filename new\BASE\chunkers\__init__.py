# Simple data structures for chunks
def create_chunk_metadata(chunk_id: str, file_path: str, name: str, content: str, additional_metadata: dict = None):
    """Create a chunk metadata dictionary."""
    return {
        "id": chunk_id,
        "file": file_path,
        "name": name,
        "content": content,
        "additional_metadata": additional_metadata or {}
    }


def create_chunk(metadata: dict, embeddings: list = None):
    """Create a chunk dictionary."""
    return {
        "metadata": metadata,
        "embeddings": embeddings or []
    }
