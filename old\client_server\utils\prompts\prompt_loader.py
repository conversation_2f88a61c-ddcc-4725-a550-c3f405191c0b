"""
Prompt Loader System for Markdown-based Prompts

This module provides functionality to load and process Markdown prompt files,
replacing the hardcoded Python string prompts with file-based prompts.
"""

import os
from pathlib import Path
from typing import Dict, Optional, Any
from functools import lru_cache


class PromptLoader:
    """Loads and caches Markdown prompt files"""
    
    def __init__(self, base_path: Optional[str] = None):
        """
        Initialize the prompt loader
        
        Args:
            base_path: Base directory for prompt files. If None, uses the markdown subdirectory
        """
        if base_path is None:
            # Get the directory where this file is located
            current_dir = Path(__file__).parent
            self.base_path = current_dir / "markdown"
        else:
            self.base_path = Path(base_path)
    
    @lru_cache(maxsize=128)
    def load_prompt(self, category: str, prompt_name: str) -> str:
        """
        Load a prompt from a Markdown file
        
        Args:
            category: Category subdirectory (chat, codelens, file_actions)
            prompt_name: Name of the prompt file (without .md extension)
            
        Returns:
            The prompt content as a string
            
        Raises:
            FileNotFoundError: If the prompt file doesn't exist
        """
        prompt_path = self.base_path / category / f"{prompt_name}.md"
        
        if not prompt_path.exists():
            raise FileNotFoundError(f"Prompt file not found: {prompt_path}")
        
        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            return content
        except Exception as e:
            raise RuntimeError(f"Error reading prompt file {prompt_path}: {e}")
    
    def clear_cache(self):
        """Clear the prompt cache"""
        self.load_prompt.cache_clear()
    
    def list_prompts(self, category: str) -> list[str]:
        """
        List all available prompts in a category
        
        Args:
            category: Category subdirectory
            
        Returns:
            List of prompt names (without .md extension)
        """
        category_path = self.base_path / category
        if not category_path.exists():
            return []
        
        prompts = []
        for file_path in category_path.glob("*.md"):
            prompts.append(file_path.stem)
        
        return sorted(prompts)


# Global prompt loader instance
_prompt_loader = PromptLoader()


def load_prompt(category: str, prompt_name: str) -> str:
    """
    Convenience function to load a prompt using the global loader
    
    Args:
        category: Category subdirectory (chat, codelens, file_actions)
        prompt_name: Name of the prompt file (without .md extension)
        
    Returns:
        The prompt content as a string
    """
    return _prompt_loader.load_prompt(category, prompt_name)


def clear_prompt_cache():
    """Clear the global prompt cache"""
    _prompt_loader.clear_cache()


def list_prompts(category: str) -> list[str]:
    """List all available prompts in a category"""
    return _prompt_loader.list_prompts(category)
