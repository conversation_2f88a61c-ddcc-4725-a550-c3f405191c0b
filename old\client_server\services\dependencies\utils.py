from pathlib import Path

# Defer imports to prevent circular dependencies
from client_server.utils.path_selector import PathSelector
from client_server.services.dependencies.registry import DependencyRegistry

# -----------------------------------------------------------------------------
# Register dependencies
# -----------------------------------------------------------------------------


def register_all_dependencies():
    """
    Register all dependencies with the registry.
    This function is called after all modules are loaded to avoid circular imports.
    """
    # Import dependencies here to avoid circular imports
    from client_server.services.dependencies.inferx import InferXDependency
    from client_server.services.dependencies.inferx_model import InferXModelDependency
    from client_server.services.dependencies.ollama_model import OllamaModelDependency
    from client_server.services.dependencies.ollama import OllamaDependency

    # Ollama
    DependencyRegistry.register_dependency(
        OllamaDependency(
            PathSelector.get_cache_path() / "ollama",
            id="ollama",
            name="Ollama",
            description="Ollama is a lightweight, fast, and easy-to-use language model server.",
        ),
    )
    DependencyRegistry.register_dependency(
        OllamaModelDependency(
            PathSelector.get_cache_path() / "ollama",
            "nomic-embed-text:v1.5",
            id="ollama_model_embedding",
            name="Embedding Model",
            description="Embedding model required for capabilities like Codebase, Knowledge Base, inline suggestions, etc to work.",
        ),
    )
    DependencyRegistry.register_dependency(
        OllamaModelDependency(
            PathSelector.get_cache_path() / "ollama",
            "qwen3:4b",
            id="ollama_model_qwen",
            name="Chat LLM",
            description="Chat LLM required for chat capabilities to work.",
        ),
    )
    DependencyRegistry.register_dependency(
        OllamaModelDependency(
            PathSelector.get_cache_path() / "ollama",
            "qwen3:4b",
            id="ollama_model_qwen_coder",
            name="Inline auto-completion LLM",
            description="Inline auto-completion LLM required for inline suggestions to work.",
        ),
    )

    # InferX
    DependencyRegistry.register_dependency(
        InferXDependency(
            PathSelector.get_cache_path() / Path("inferx"),
            id="inferx",
            name="InferX",
            description="InferX is a lightweight, fast, and easy-to-use language model server.",
        ),
    )
    DependencyRegistry.register_dependency(
        InferXModelDependency(
            PathSelector.get_cache_path() / Path("inferx"),
            "all",
            id="inferx_all_models",
            name="InferX Chat LLM",
            description="Chat LLM for InferX",
        ),
    )


# -----------------------------------------------------------------------------
