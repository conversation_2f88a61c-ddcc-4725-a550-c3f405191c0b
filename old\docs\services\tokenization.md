# Tokenization Service

The Tokenization service provides a consistent interface for converting text into tokens and vice-versa. Tokenization is a critical preprocessing step for most Large Language Model (LLM) operations, such as calculating context size, truncating prompts, and preparing model inputs.

## Design

- **`ITokenizationBackend` Interface**: A simple contract defining the core tokenization functions: `count_tokens`, `tokenize`, `detokenize`, and `truncate_to_token_limit`.
- **Implementations**: Concrete classes that wrap actual tokenization libraries.
  - `OllamaTokenizationBackend`: Uses a local Ollama server to perform tokenization.
  - `TiktokenBackend`: Uses OpenAI's `tiktoken` library, which is a fast, local BPE tokenizer.
- **`TokenizationBuilder`**: A factory that intelligently selects the best available backend.
- **Utility Functions**: Helper functions built on top of the interface for common tasks like truncating a list of chat messages.

## Core Components

### `ITokenizationBackend` Interface

Located in `client_server/services/tokenization/__init__.py`, this ABC defines:

- `count_tokens(text, model)`: Returns the number of tokens in a string.
- `tokenize(text, model)`: Converts a string into a list of token IDs.
- `detokenize(tokens, model)`: Converts a list of token IDs back into a string.
- `truncate_to_token_limit(text, max_tokens, model)`: Truncates a string to a maximum token length.

### Implementations

1.  **`OllamaTokenizationBackend` (`ollama.py`)**

    - This backend leverages a running Ollama server.
    - It manages the Ollama server process, starting it if it's not already running.
    - It uses the `ollama.Client`'s `tokenize` and (a presumed) `detokenize` functionality.
    - This is the default backend for non-ARM platforms.

2.  **`TiktokenBackend` (`tiktoken.py`)**
    - This backend uses the `tiktoken` library directly in the same process.
    - It's generally faster for tokenization as it doesn't require communication with a separate server.
    - It uses `cl100k_base` as the default encoding, which is standard for many modern models. It can also select encodings based on a model name.
    - This is the preferred backend for Snapdragon ARM platforms.

### `TokenizationBuilder`

This factory, found in `client_server/services/tokenization/utils.py`, provides a single point of access.

- `create()`: This static method returns a singleton instance of the appropriate backend. It uses `PlatformDetector.is_snapdragon_arm()` to choose between `TiktokenBackend` (for ARM) and `OllamaTokenizationBackend` (for others).
- `dispose()`: Resets the singleton instance.

### Utility Functions

The `utils.py` file also contains important helper functions that use the `ITokenizationBackend`:

- `count_tokens()`: A simple wrapper.
- `truncate_text()`: A wrapper for `truncate_to_token_limit`.
- `ensure_messages_within_token_limit()`: Takes a list of chat messages and a token limit. It calculates the total token count and, if it exceeds the limit, truncates messages starting from the oldest ones until the total is within the budget. This is essential for managing chat history before sending it to an LLM.

## Workflow

1.  **Instantiation**: The application calls `TokenizationBuilder.create()` to get the platform-appropriate tokenizer.
2.  **Usage**: The returned tokenizer instance is then used to perform tokenization tasks.
3.  **Chat History Management**: A common use case is to pass the tokenizer and a list of chat messages to `ensure_messages_within_token_limit` before sending them to the `Inference` service. This prevents context overflow errors.
