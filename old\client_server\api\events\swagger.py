from typing import Any
import uuid
from socketio import AsyncServer
import os
from pydantic import BaseModel, Field
import json
import asyncio
from client_server.services.swagger.generator import SwaggerSpecGenerator
from client_server.core.constants import SSL_CONTEXT
from client_server.core.state import G_BASE_URL
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_operation_stats, log_api_call_stats
import httpx
import requests
import yaml
from client_server.utils.actions.swagger_search import _perform_swagger_search
from client_server.utils.actions.utils import SearchReferences
import re
import time


class SwaggerRequestData(BaseModel):
    """Request model for Swagger operations"""

    swagger_file_path: str = Field(default="", description="Path to Swagger file")
    swagger_content: str = Field(default="", description="Raw Swagger content")
    swagger_url: str = Field(default="", description="URL to Swagger spec")
    swagger_knowledgebase_id: str = Field(default="", description="Knowledge base ID")
    client_side_language: str = Field(default="", description="Client language")
    custom_instructions: str = Field(default="", description="Custom instructions")
    base_url: str = Field(default="", description="Base URL for API")
    provider: str | dict[str, Any] = Field(..., description="Provider information")


async def mock_process_endpoint(
    endpoint, client_language, custom_instructions, sio, sid
):
    """Process a single endpoint and send the result back to the client."""
    start_time = time.time()
    endpoint_path = endpoint.get("path", "unknown")
    LOGGER.info(f"Processing mock endpoint: {endpoint_path}")

    try:
        await sio.emit(
            "swagger_gen:endpoint_start",
            data={"id": endpoint_path},
            to=sid,
        )

        payload = {
            "required_imports": ["import requests"],
            "code": "print('Hello, world!')",
        }

        await sio.emit(
            "swagger_gen:endpoint_success",
            data={
                "id": endpoint_path,
                "response": payload,
            },
            to=sid,
        )

        total_time = time.time() - start_time
        LOGGER.debug(f"Mock endpoint processed in {total_time:.3f}s")
        return payload

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error processing mock endpoint after {total_time:.3f}s: {e}")
        raise


async def process_endpoint(
    endpoint, base_url, client_language, custom_instructions, provider, sio, sid
):
    """Process a single endpoint and send the result back to the client."""
    start_time = time.time()
    endpoint_path = endpoint.get("path", "unknown")
    endpoint_method = endpoint.get("method", "unknown")

    LOGGER.info(f"Processing endpoint: {endpoint_method.upper()} {endpoint_path}")

    base_url = G_BASE_URL.get().general

    try:
        # Emit start event
        await sio.emit(
            "swagger_gen:endpoint_start",
            data={"id": endpoint_path},
            to=sid,
        )

        # Prepare request payload
        request_payload = {
            "endpoint_spec": endpoint,
            "provider": provider,
            "client_language": client_language,
            "custom_instructions": custom_instructions,
            "base_url": base_url,
        }
        request_payload = json.loads(json.dumps(request_payload, default=str))

        max_retries = 5
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                api_start = time.time()
                async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
                    response = await client.post(
                        f"{base_url}/swagger/gen",
                        json=request_payload,
                        headers={"Content-Type": "application/json"},
                        timeout=120,
                    )
                api_time = time.time() - api_start

                log_api_call_stats(
                    f"{base_url}/swagger/gen",
                    "POST",
                    response.status_code,
                    api_time,
                    len(response.content),
                )

                if response.status_code == 200:
                    response_data = response.json()
                    LOGGER.info(
                        f"Generated code for endpoint: {endpoint_method.upper()} {endpoint_path}"
                    )

                    await sio.emit(
                        "swagger_gen:endpoint_success",
                        data={"id": endpoint_path, "response": response_data},
                        to=sid,
                    )

                    total_time = time.time() - start_time
                    LOGGER.debug(
                        f"Endpoint processed successfully in {total_time:.3f}s"
                    )
                    return response_data
                else:
                    error_text = response.text
                    last_error = error_text
                    LOGGER.warning(
                        f"Backend returned status {response.status_code}: {error_text}"
                    )
                    raise Exception(
                        f"Backend returned status {response.status_code}: {error_text}"
                    )

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    wait_time = 2**retry_count * 0.5  # exponential backoff
                    LOGGER.warning(
                        f"Attempt {retry_count} failed for {endpoint_method.upper()} "
                        f"{endpoint_path}. Retrying in {wait_time:.1f}s... Error: {str(e)}"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    error_message = (
                        f"Failed after {max_retries} attempts. Last error: {str(e)}"
                    )
                    LOGGER.error(
                        f"Backend error for {endpoint_method.upper()} {endpoint_path}: {error_message}"
                    )
                    await sio.emit(
                        "swagger_gen:endpoint_error",
                        data={"id": endpoint_path, "error": error_message},
                        to=sid,
                    )
                    return None

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(
            f"Error processing endpoint {endpoint_path} after {total_time:.3f}s: {e}"
        )
        await sio.emit(
            "swagger_gen:endpoint_error",
            data={"id": endpoint_path, "error": str(e)},
            to=sid,
        )
        return None


async def combine_imports(results):
    """Combine import statements from multiple results."""
    start_time = time.time()
    LOGGER.debug("Combining import statements")

    try:
        unique_imports = set()
        for result in results:
            if "required_imports" in result:
                unique_imports.update(result["required_imports"])

        total_time = time.time() - start_time
        LOGGER.debug(
            f"Combined {len(unique_imports)} unique imports in {total_time:.3f}s"
        )
        return list(unique_imports)

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error combining imports after {total_time:.3f}s: {e}")
        return []


def prepare_spec_file(payload: SwaggerRequestData):
    """Prepare Swagger specification file from various sources."""
    start_time = time.time()
    LOGGER.info("Preparing Swagger specification")

    try:
        generator = SwaggerSpecGenerator()
        endpoints = []

        if payload.swagger_file_path:
            LOGGER.info(f"Generating endpoints from file: {payload.swagger_file_path}")
            endpoints = generator.generate_from_file(payload.swagger_file_path)
        elif payload.swagger_content:
            LOGGER.info("Generating endpoints from content")
            try:
                swagger_dict = json.loads(payload.swagger_content)
                endpoints = generator.generate_from_content(swagger_dict)
            except json.JSONDecodeError as e:
                LOGGER.error(f"Invalid JSON content provided: {e}")
                return None
        elif payload.swagger_url:
            LOGGER.info(f"Generating endpoints from URL: {payload.swagger_url}")
            endpoints = generator.generate_from_url(payload.swagger_url)
        else:
            LOGGER.error("No swagger source provided")
            return None

        total_time = time.time() - start_time
        LOGGER.info(
            f"Swagger spec preparation completed - Endpoints: {len(endpoints)}, "
            f"Time: {total_time:.3f}s"
        )
        return endpoints

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error preparing Swagger spec after {total_time:.3f}s: {e}")
        return None


def format_response_object(response):
    """Format response object for consistent structure."""
    start_time = time.time()
    LOGGER.debug("Formatting response object")

    try:
        formatted = {}
        for key in response.keys():
            formatted[key] = {
                "description": response[key]["description"],
                "schema": response[key].get("schema", {}),
                "headers": response[key].get("headers", {}),
            }

        total_time = time.time() - start_time
        LOGGER.debug(f"Response formatting completed in {total_time:.3f}s")
        return formatted

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error formatting response after {total_time:.3f}s: {e}")
        return {}


async def handle_swagger_spec_gen_event(
    sio: AsyncServer, sid: str, payload: SwaggerRequestData
):
    """Handle Swagger specification generation event."""
    start_time = time.time()
    LOGGER.info("Starting Swagger spec generation")

    try:
        generator = SwaggerSpecGenerator()
        endpoints = []

        # Get endpoints based on the provided input method
        content_start = time.time()
        if payload.swagger_file_path:
            LOGGER.info(f"Generating endpoints from file: {payload.swagger_file_path}")
            endpoints = generator.generate_from_file(
                payload.swagger_file_path, base_uri=payload.base_url
            )
        elif payload.swagger_content:
            LOGGER.info("Generating endpoints from content")
            try:
                file_name = f"swagger_temp_{uuid.uuid4()}.json"
                with open(file_name, "w+") as f:
                    f.write(payload.swagger_content)
                endpoints = generator.generate_from_file(
                    file_name,
                    base_uri=payload.base_url,
                )
                os.remove(file_name)
            except json.JSONDecodeError:
                LOGGER.error("Invalid JSON content provided")
                await sio.emit(
                    "swagger_gen:error",
                    data="Invalid JSON content provided",
                    to=sid,
                )
                return
        elif payload.swagger_url:
            LOGGER.info(f"Generating endpoints from URL: {payload.swagger_url}")
            endpoints = generator.generate_from_url(payload.swagger_url)
        else:
            LOGGER.error("No swagger source provided")
            await sio.emit(
                "swagger_gen:error",
                data="No swagger source provided",
                to=sid,
            )
            return

        content_time = time.time() - content_start
        LOGGER.info(f"Generated {len(endpoints)} endpoints in {content_time:.3f}s")

        # 1. Emit swagger_gen:begin event with endpoints
        endpoint_list = [
            {
                "id": endpoint.get("path", "unknown"),
                "method": endpoint.get("method", "unknown"),
            }
            for endpoint in endpoints
        ]
        await sio.emit(
            "swagger_gen:begin",
            data={"endpoints": endpoint_list},
            to=sid,
        )

        # 2. Process each endpoint sequentially
        results = []
        processing_start = time.time()
        try:
            LOGGER.info(f"Processing {len(endpoints)} endpoints sequentially")
            for endpoint in endpoints:
                result = await process_endpoint(
                    endpoint,
                    payload.base_url,
                    payload.client_side_language,
                    payload.custom_instructions,
                    payload.provider,
                    sio,
                    sid,
                )
                if result is not None:
                    results.append(result)
        except Exception as e:
            LOGGER.error(f"Error during sequential processing: {e}")
            raise

        if not results:
            LOGGER.error("No results returned from endpoint processing")
            raise Exception("No results returned")

        processing_time = time.time() - processing_start
        LOGGER.info(
            f"Endpoint processing completed - Success: {len(results)}, "
            f"Failed: {len(endpoints) - len(results)}, "
            f"Time: {processing_time:.3f}s"
        )

        # 3. Combine all the imports into a single chunk
        imports = await combine_imports(results)
        await sio.emit(
            "swagger_gen:imports",
            data={"imports": imports},
            to=sid,
        )

        # 4. Emit swagger_gen:success
        await sio.emit("swagger_gen:success", to=sid)

        total_time = time.time() - start_time
        stats = log_operation_stats(
            "swagger_spec_gen", start_time, len(endpoints), success=True
        )
        LOGGER.info(f"Swagger spec generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error processing swagger request after {total_time:.3f}s: {e}")
        await sio.emit("swagger_gen:error", data=str(e), to=sid)
        raise


def parse_prompt(input_text):
    structure_pattern = re.compile(r"```structure\s*(.*?)\s*```", re.DOTALL)
    structure_match = structure_pattern.search(input_text)

    if structure_match:
        structure = structure_match.group(1).strip()
        prompt = input_text.replace(structure_match.group(0), "").strip()
    else:
        structure = None
        prompt = input_text.strip()

    return prompt, structure


async def handle_swagger_prompt_gen_event(
    sio: AsyncServer, sid: str, payload: SwaggerRequestData
):
    """Handle Swagger prompt generation event."""
    start_time = time.time()
    LOGGER.info("Starting Swagger prompt generation")

    base_url = G_BASE_URL.get().general

    try:
        user_prompt, structure_prompt = parse_prompt(payload.custom_instructions)
        kbid = payload.swagger_knowledgebase_id
        LOGGER.info(f"Processing prompt for KB: {kbid}")

        # Generate queries
        query_start = time.time()
        response = requests.post(
            url=f"{base_url}/swagger/generate_queries", json={"query": user_prompt}
        )
        query_time = time.time() - query_start

        log_api_call_stats(
            f"{base_url}/swagger/generate_queries",
            "POST",
            response.status_code,
            query_time,
            len(response.content),
        )

        if response.status_code != 200:
            LOGGER.error(f"Error generating queries: {response.status_code}")
            return await sio.emit(
                "swagger_gen:error",
                data="Error generating queries",
                to=sid,
            )

        queries = response.json()
        LOGGER.debug(f"Generated {len(queries)} queries in {query_time:.3f}s")

        # Perform searches
        search_start = time.time()
        search_results = []
        for query in queries:
            LOGGER.debug(f"Performing search with query: {query}")
            result = await _perform_swagger_search(
                query=query,
                index_name=kbid,
            )
            search_results.extend(result)

        search_time = time.time() - search_start
        LOGGER.debug(
            f"Search completed - Results: {len(search_results)}, "
            f"Time: {search_time:.3f}s"
        )

        # Process results
        processing_start = time.time()
        final_results = []
        for res in search_results:
            if len(final_results) != 0:
                paths = [x["metadata"]["endpoint"]["path"] for x in final_results]
                if res["metadata"]["endpoint"]["path"] not in paths:
                    final_results.append(res)
            else:
                final_results.append(res)

        search_references = SearchReferences(request_id="")
        for res in final_results:
            search_references.add_search_result(
                path=res["metadata"]["endpoint"]["path"],
                type="docs",
                content=json.dumps(res["metadata"]["endpoint"]),
                name=res["metadata"]["endpoint"]["path"],
            )

        processing_time = time.time() - processing_start
        LOGGER.debug(
            f"Result processing completed - Unique results: {len(final_results)}, "
            f"Time: {processing_time:.3f}s"
        )

        await sio.emit(
            "swagger_gen:references",
            data=search_references.get_search_result(),
            to=sid,
        )

        # Prepare data for sequence generation
        data_to_be_sent = []
        for res in final_results:
            data_to_be_sent.append(
                {
                    "summary": res["metadata"]["summary"],
                    "path": res["metadata"]["endpoint"]["path"],
                    "method": res["metadata"]["endpoint"]["method"],
                    "description": res["metadata"]["endpoint"]["description"],
                }
            )

        search_content_in_yaml = yaml.dump(data_to_be_sent)

        # Generate sequence
        sequence_start = time.time()
        response = requests.post(
            f"{base_url}/swagger/sequence_endpoints",
            json={"prompt": user_prompt, "search_content": search_content_in_yaml},
        )
        sequence_time = time.time() - sequence_start

        log_api_call_stats(
            f"{base_url}/swagger/sequence_endpoints",
            "POST",
            response.status_code,
            sequence_time,
            len(response.content),
        )

        LOGGER.debug(f"Sequence generation completed in {sequence_time:.3f}s")
        seq_data = response.json()
        calls = seq_data["endpoints"]["call"]

        if not isinstance(calls, list):
            calls = [calls]

        # Process calls
        spec_endpoint_list = prepare_spec_file(payload)
        response_json = {"name": "Swagger API", "calls": []}

        for call in calls:
            path = call["path"]
            spec_data = next(
                (
                    item
                    for item in final_results
                    if item["metadata"]["endpoint"]["path"] == path
                ),
                None,
            )
            response_json["calls"].append(
                {
                    "name": spec_data["metadata"]["endpoint"]["operation_id"],
                    "sequence": call["sequence"],
                    "endpoint": spec_data["metadata"]["endpoint"]["path"],
                    "method": spec_data["metadata"]["endpoint"]["method"],
                    "params": spec_data["metadata"]["endpoint"]["parameters"],
                    "response": format_response_object(
                        spec_data["metadata"]["endpoint"]["responses"]
                    ),
                }
            )

        # Apply structure if provided
        if structure_prompt:
            LOGGER.debug("Applying structure to output")
            structure_start = time.time()
            res = requests.post(
                f"{base_url}/swagger/structure_output",
                json={"prompt": structure_prompt, "swagger_output": response_json},
            )
            structure_time = time.time() - structure_start

            log_api_call_stats(
                f"{base_url}/swagger/structure_output",
                "POST",
                res.status_code,
                structure_time,
                len(res.content),
            )

            response_json = res.json()
            LOGGER.debug(f"Structure applied in {structure_time:.3f}s")

        await sio.emit("swagger_gen:kb_response", data=response_json, to=sid)

        total_time = time.time() - start_time
        stats = log_operation_stats(
            "swagger_prompt_gen", start_time, len(calls), success=True
        )
        LOGGER.info(f"Swagger prompt generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error in Swagger prompt generation after {total_time:.3f}s: {e}")
        await sio.emit("swagger_gen:error", data=str(e), to=sid)
        raise


async def handle_swagger_gen_event(sio: AsyncServer, sid: str, data: dict = {}):
    """Main handler for Swagger generation events."""
    start_time = time.time()
    LOGGER.info(f"Processing Swagger generation request for session {sid}")

    try:
        validation_start = time.time()
        payload = SwaggerRequestData.model_validate(data)
        validation_time = time.time() - validation_start
        LOGGER.debug(f"Request validation completed in {validation_time:.3f}s")

        if payload.swagger_knowledgebase_id == "":
            LOGGER.info("Handling Swagger spec generation")
            await handle_swagger_spec_gen_event(sio, sid, payload)
        else:
            LOGGER.info("Handling Swagger prompt generation")
            await handle_swagger_prompt_gen_event(sio, sid, payload)

        total_time = time.time() - start_time
        LOGGER.info(f"Swagger generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error in Swagger generation after {total_time:.3f}s: {e}")
        await sio.emit("swagger_gen:error", data=str(e), to=sid)
        raise

    # Signal completion
    await sio.emit("swagger_gen:end", to=sid)
