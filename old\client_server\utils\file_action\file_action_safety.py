"""
Safety and Rollback Mechanisms for File Actions

This module implements safety levels, rollback functionality, change tracking,
and audit logging for all automated changes with confidence scoring and impact assessment.
"""

import os
import shutil
import time
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.services.db_handler import Database
from client_server.utils.prompts.file_action_prompts import SafetyLevel
from client_server.utils.models.file_actions import SafetyRating, ImpactLevel, Fix
from client_server.utils.path_selector import PathSelecto


class ChangeType(Enum):
    """Types of changes that can be tracked"""
    FILE_MODIFIED = "file_modified"
    FILE_CREATED = "file_created"
    FILE_DELETED = "file_deleted"
    FILE_RENAMED = "file_renamed"
    BACKUP_CREATED = "backup_created"


@dataclass
class FileChange:
    """Represents a single file change for tracking and rollback"""
    id: str = field(default_factory=lambda: str(int(time.time() * 1000000)))
    file_path: str = ""
    change_type: ChangeType = ChangeType.FILE_MODIFIED
    timestamp: float = field(default_factory=time.time)
    backup_path: Optional[str] = None
    original_content: Optional[str] = None
    new_content: Optional[str] = None
    file_hash_before: Optional[str] = None
    file_hash_after: Optional[str] = None
    operation_id: Optional[str] = None
    session_id: Optional[str] = None
    user_approved: bool = False
    confidence_score: float = 0.0
    safety_rating: SafetyRating = SafetyRating.RISKY
    impact_level: ImpactLevel = ImpactLevel.MODERATE
    rollback_available: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


class FileActionSafetyManager:
    """
    Manages safety levels, change tracking, and rollback functionality for file actions
    """
    
    def __init__(self):
        self.db = Database()
        self.changes_collection = self.db["file_changes"]
        self.audit_collection = self.db["file_audit_log"]
        
        # Create backups directory
        self.backups_dir = PathSelector.get_base_path() / "file_action_backups"
        self.backups_dir.mkdir(exist_ok=True)
        
        LOGGER.info(f"FileActionSafetyManager initialized, backups dir: {self.backups_dir}")
    
    def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """Calculate SHA256 hash of file content"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            LOGGER.error(f"Error calculating hash for {file_path}: {e}")
            return None
    
    def _read_file_content(self, file_path: str) -> Optional[str]:
        """Read file content safely"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            LOGGER.error(f"Error reading file {file_path}: {e}")
            return None
    
    def _create_backup(self, file_path: str, operation_id: str) -> Optional[str]:
        """
        Create a backup of the file in a safe manner
        
        Args:
            file_path: Path to the file to backup
            operation_id: Unique identifier for the operation
            
        Returns:
            Optional[str]: Path to the backup file if successful, None otherwise
        """
        try:
            # Validate input file exists
            if not os.path.exists(file_path):
                LOGGER.warning(f"Cannot create backup - file does not exist: {file_path}")
                return None
            
            # Ensure backup directory exists
            backup_folder = PathSelector.get_user_home_path() / "file_action_backups"
            backup_folder.mkdir(parents=True, exist_ok=True)
            
            # Generate unique backup filename
            file_stem = Path(file_path).stem
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            backup_filename = f"{file_stem}_{operation_id}_{timestamp}.bak"
            backup_path = backup_folder / backup_filename
            
            # Create backup with metadata
            shutil.copy2(file_path, backup_path)
            
            LOGGER.info(f"Created backup at {backup_path} with metadata")
            return str(backup_path)
            
        except Exception as e:
            LOGGER.error(f"Failed to create backup for {file_path}: {str(e)}")
            return None
    
    def validate_fix_safety(
        self,
        fix: Fix,
        safety_level: SafetyLevel,
        file_content: str
    ) -> Tuple[bool, str]:
        """
        Validate if a fix meets the safety requirements
        
        Args:
            fix: The fix to validate
            safety_level: Required safety level
            file_content: Current file content
        
        Returns:
            Tuple of (is_safe, reason)
        """
        # Check confidence score
        min_confidence = {
            SafetyLevel.SAFE_ONLY: 0.9,
            SafetyLevel.REVIEW_REQUIRED: 0.8,
            SafetyLevel.MANUAL_APPROVAL: 0.0
        }
        
        if fix.confidence < min_confidence[safety_level]:
            return False, f"Confidence score {fix.confidence} below required {min_confidence[safety_level]}"
        
        # Check safety rating
        allowed_ratings = {
            SafetyLevel.SAFE_ONLY: [SafetyRating.SAFE],
            SafetyLevel.REVIEW_REQUIRED: [SafetyRating.SAFE, SafetyRating.LIKELY_SAFE],
            SafetyLevel.MANUAL_APPROVAL: [SafetyRating.SAFE, SafetyRating.LIKELY_SAFE, SafetyRating.RISKY, SafetyRating.DANGEROUS]
        }
        
        if fix.safety_rating not in allowed_ratings[safety_level]:
            return False, f"Safety rating {fix.safety_rating.value} not allowed for level {safety_level.value}"
        
        # Check impact level for SAFE_ONLY
        if safety_level == SafetyLevel.SAFE_ONLY and fix.impact not in [ImpactLevel.MINIMAL, ImpactLevel.MODERATE]:
            return False, f"Impact level {fix.impact.value} too high for SAFE_ONLY mode"
        
        # Additional validation for dangerous fixes
        if fix.safety_rating == SafetyRating.DANGEROUS and safety_level != SafetyLevel.MANUAL_APPROVAL:
            return False, "Dangerous fixes require manual approval"
        
        return True, "Fix meets safety requirements"
    
    def prepare_file_change(
        self,
        file_path: str,
        operation_id: str,
        session_id: Optional[str] = None,
        confidence_score: float = 0.0,
        safety_rating: SafetyRating = SafetyRating.RISKY,
        impact_level: ImpactLevel = ImpactLevel.MODERATE,
        create_backup: bool = True
    ) -> FileChange:
        """
        Prepare a file change for tracking
        
        Args:
            file_path: Path to the file being changed
            operation_id: Unique identifier for the operation
            session_id: Optional session identifier
            confidence_score: Confidence score for the change
            safety_rating: Safety rating for the change
            impact_level: Impact level of the change
            create_backup: Whether to create a backup
        
        Returns:
            FileChange object for tracking
        """
        # Determine change type
        change_type = ChangeType.FILE_MODIFIED
        if not os.path.exists(file_path):
            change_type = ChangeType.FILE_CREATED
        
        # Read current content and calculate hash
        original_content = None
        file_hash_before = None
        if os.path.exists(file_path):
            original_content = self._read_file_content(file_path)
            file_hash_before = self._calculate_file_hash(file_path)
        
        # Create backup if requested and file exists
        backup_path = None
        if create_backup and os.path.exists(file_path):
            backup_path = self._create_backup(file_path, operation_id)
        
        # Create change record
        change = FileChange(
            file_path=file_path,
            change_type=change_type,
            backup_path=backup_path,
            original_content=original_content,
            file_hash_before=file_hash_before,
            operation_id=operation_id,
            session_id=session_id,
            confidence_score=confidence_score,
            safety_rating=safety_rating,
            impact_level=impact_level,
            rollback_available=backup_path is not None
        )
        
        LOGGER.info(f"Prepared file change tracking for {file_path} (operation: {operation_id})")
        return change
    
    def finalize_file_change(
        self,
        change: FileChange,
        new_content: Optional[str] = None,
        success: bool = True
    ) -> bool:
        """
        Finalize a file change and store it in the database
        
        Args:
            change: The FileChange object to finalize
            new_content: New content of the file (if available)
            success: Whether the change was successful
        
        Returns:
            True if finalization was successful
        """
        try:
            # Update change with final information
            if os.path.exists(change.file_path):
                if new_content is None:
                    change.new_content = self._read_file_content(change.file_path)
                else:
                    change.new_content = new_content
                change.file_hash_after = self._calculate_file_hash(change.file_path)
            
            # Store in database
            change_record = {
                "id": change.id,
                "file_path": change.file_path,
                "change_type": change.change_type.value,
                "timestamp": change.timestamp,
                "backup_path": change.backup_path,
                "file_hash_before": change.file_hash_before,
                "file_hash_after": change.file_hash_after,
                "operation_id": change.operation_id,
                "session_id": change.session_id,
                "user_approved": change.user_approved,
                "confidence_score": change.confidence_score,
                "safety_rating": change.safety_rating.value,
                "impact_level": change.impact_level.value,
                "rollback_available": change.rollback_available,
                "success": success,
                "metadata": change.metadata
            }
            
            self.changes_collection.insert_one(change_record)
            
            # Log audit entry
            self._log_audit_entry(
                action="file_change_finalized",
                file_path=change.file_path,
                operation_id=change.operation_id,
                session_id=change.session_id,
                details={
                    "change_id": change.id,
                    "success": success,
                    "confidence_score": change.confidence_score,
                    "safety_rating": change.safety_rating.value,
                    "impact_level": change.impact_level.value
                }
            )
            
            LOGGER.info(f"Finalized file change {change.id} for {change.file_path}")
            return True
            
        except Exception as e:
            LOGGER.error(f"Error finalizing file change {change.id}: {e}")
            return False

    def rollback_change(self, change_id: str) -> Tuple[bool, str]:
        """
        Rollback a file change using its backup

        Args:
            change_id: ID of the change to rollback

        Returns:
            Tuple of (success, message)
        """
        try:
            # Find the change record
            change_record = self.changes_collection.find_one({"id": change_id})
            if not change_record:
                return False, f"Change record {change_id} not found"

            if not change_record.get("rollback_available", False):
                return False, "Rollback not available for this change"

            backup_path = change_record.get("backup_path")
            if not backup_path or not os.path.exists(backup_path):
                return False, "Backup file not found"

            file_path = change_record["file_path"]

            # Create a backup of current state before rollback
            rollback_operation_id = f"rollback_{change_id}_{int(time.time())}"
            current_backup = self._create_backup(file_path, rollback_operation_id)

            # Restore from backup
            shutil.copy2(backup_path, file_path)

            # Update change record to mark as rolled back
            self.changes_collection.update_one(
                {"id": change_id},
                {"$set": {"rolled_back": True, "rollback_timestamp": time.time()}}
            )

            # Log audit entry
            self._log_audit_entry(
                action="file_change_rolled_back",
                file_path=file_path,
                operation_id=rollback_operation_id,
                details={
                    "original_change_id": change_id,
                    "backup_path": backup_path,
                    "current_state_backup": current_backup
                }
            )

            LOGGER.info(f"Successfully rolled back change {change_id} for {file_path}")
            return True, f"Successfully rolled back change {change_id}"

        except Exception as e:
            error_msg = f"Error rolling back change {change_id}: {e}"
            LOGGER.error(error_msg)
            return False, error_msg

    def rollback_operation(self, operation_id: str) -> Tuple[bool, str, List[str]]:
        """
        Rollback all changes from a specific operation

        Args:
            operation_id: ID of the operation to rollback

        Returns:
            Tuple of (success, message, list of rolled back change IDs)
        """
        try:
            # Find all changes for this operation
            changes = list(self.changes_collection.find({"operation_id": operation_id}))

            if not changes:
                return False, f"No changes found for operation {operation_id}", []

            rolled_back_changes = []
            failed_rollbacks = []

            # Rollback changes in reverse chronological order
            changes.sort(key=lambda x: x["timestamp"], reverse=True)

            for change in changes:
                if change.get("rolled_back", False):
                    continue  # Already rolled back

                success, message = self.rollback_change(change["id"])
                if success:
                    rolled_back_changes.append(change["id"])
                else:
                    failed_rollbacks.append(f"Change {change['id']}: {message}")

            if failed_rollbacks:
                error_msg = f"Partial rollback of operation {operation_id}. Failed: {'; '.join(failed_rollbacks)}"
                return False, error_msg, rolled_back_changes

            success_msg = f"Successfully rolled back operation {operation_id} ({len(rolled_back_changes)} changes)"
            return True, success_msg, rolled_back_changes

        except Exception as e:
            error_msg = f"Error rolling back operation {operation_id}: {e}"
            LOGGER.error(error_msg)
            return False, error_msg, []

    def get_change_history(
        self,
        file_path: Optional[str] = None,
        operation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get change history with optional filtering

        Args:
            file_path: Filter by file path
            operation_id: Filter by operation ID
            session_id: Filter by session ID
            limit: Maximum number of results

        Returns:
            List of change records
        """
        try:
            query = {}
            if file_path:
                query["file_path"] = file_path
            if operation_id:
                query["operation_id"] = operation_id
            if session_id:
                query["session_id"] = session_id

            changes = list(self.changes_collection.find(
                query,
                sort=[("timestamp", -1)],
                limit=limit
            ))

            return changes

        except Exception as e:
            LOGGER.error(f"Error retrieving change history: {e}")
            return []

    def cleanup_old_backups(self, days_old: int = 30) -> Tuple[int, int]:
        """
        Clean up old backup files and change records

        Args:
            days_old: Remove backups older than this many days

        Returns:
            Tuple of (files_removed, records_cleaned)
        """
        try:
            cutoff_time = time.time() - (days_old * 24 * 3600)
            files_removed = 0
            records_cleaned = 0

            # Find old change records
            old_changes = list(self.changes_collection.find({"timestamp": {"$lt": cutoff_time}}))

            for change in old_changes:
                # Remove backup file if it exists
                backup_path = change.get("backup_path")
                if backup_path and os.path.exists(backup_path):
                    try:
                        os.remove(backup_path)
                        files_removed += 1
                        LOGGER.debug(f"Removed old backup: {backup_path}")
                    except Exception as e:
                        LOGGER.warning(f"Failed to remove backup {backup_path}: {e}")

                # Remove change record
                self.changes_collection.delete_one({"id": change["id"]})
                records_cleaned += 1

            LOGGER.info(f"Cleanup completed: removed {files_removed} backup files, {records_cleaned} records")
            return files_removed, records_cleaned

        except Exception as e:
            LOGGER.error(f"Error during backup cleanup: {e}")
            return 0, 0

    def _log_audit_entry(
        self,
        action: str,
        file_path: Optional[str] = None,
        operation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Log an audit entry"""
        try:
            audit_entry = {
                "timestamp": time.time(),
                "action": action,
                "file_path": file_path,
                "operation_id": operation_id,
                "session_id": session_id,
                "details": details or {}
            }

            self.audit_collection.insert_one(audit_entry)

        except Exception as e:
            LOGGER.error(f"Error logging audit entry: {e}")

    def get_audit_log(
        self,
        action: Optional[str] = None,
        file_path: Optional[str] = None,
        operation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get audit log entries with optional filtering

        Args:
            action: Filter by action type
            file_path: Filter by file path
            operation_id: Filter by operation ID
            session_id: Filter by session ID
            limit: Maximum number of results

        Returns:
            List of audit log entries
        """
        try:
            query = {}
            if action:
                query["action"] = action
            if file_path:
                query["file_path"] = file_path
            if operation_id:
                query["operation_id"] = operation_id
            if session_id:
                query["session_id"] = session_id

            entries = list(self.audit_collection.find(
                query,
                sort=[("timestamp", -1)],
                limit=limit
            ))

            return entries

        except Exception as e:
            LOGGER.error(f"Error retrieving audit log: {e}")
            return []


# Global safety manager instance
_global_safety_manager: Optional[FileActionSafetyManager] = None


def get_safety_manager() -> FileActionSafetyManager:
    """Get the global safety manager instance (singleton)"""
    global _global_safety_manager

    if _global_safety_manager is None:
        _global_safety_manager = FileActionSafetyManager()

    return _global_safety_manager
