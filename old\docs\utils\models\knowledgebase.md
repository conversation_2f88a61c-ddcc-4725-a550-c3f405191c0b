# Knowledge Base Models (`knowledgebase.py`)

This module is the single source of truth for all data structures related to knowledge bases (KBs). It uses Pydantic models extensively to define a clear, validated, and serializable schema for how KBs and their components are represented in the application.

## Enumerations

The module defines several `Enum` classes to create controlled vocabularies for different properties of a knowledge base.

- `QdrantKnowledgebaseType`: Defines the type of the KB (e.g., `Codebase`, `Github`, `Docs`, `Swagger`).
- `QdrantKnowledgebaseStatus`: Represents the current state of the KB (e.g., `READY`, `DRAFT`, `PROGRESS`, `ERROR`).
- `QdrantKnowledgebaseSource`: `LOCAL` or `REMOTE`.
- `QdrantKnowledgebaseScope`: `Personal` or `Organization`.

## Chunk Models

These models define the structure of a single, indivisible piece of chunked data.

- **`QdrantKnowledgebaseChunkMetadata`**: Contains the metadata for a chunk, including its `id`, the `file` it came from, its `name`, the raw `content` (the text of the chunk), and a flexible `additional_metadata` dictionary (e.g., to store line numbers).
- **`QdrantKnowledgeBaseChunk`**: The main chunk model. It wraps the `metadata` and includes a list for the `embeddings` vector. It also has an async method `fill_embeddings` to populate the embedding from a given backend.

## Knowledge Base Metadata Models

This is a set of hierarchical models that define the metadata for a knowledge base.

- **`QdrantBaseMetadata`**: A base class for type-specific metadata.
- **`QdrantCodebaseMetadata`**: Metadata for a `codebase` type KB, containing the `path` and list of `files`.
- **`QdrantGithubMetadata`**: For a `git` type KB, containing `repo_url`, `branch`, and an optional `accessToken`.
- **`QdrantDocsMetadata`**: For a `docs` type KB, containing a list of `urls`.
- **`QdrantSwaggerMetadata`**: For a `swagger` type KB, containing a list of `endpoints` and source information.
- **`QdrantKnowledgeBaseMetadata`**: The main top-level metadata model. It brings together all the common properties like `id`, `name`, `description`, `type`, `status`, and contains the type-specific metadata in a `Union` field.

## The `QdrantKnowledgeBase` Class

This is the primary class for working with knowledge bases. It inherits from `QdrantKnowledgeBaseMetadata` and adds functionality for persistence and lifecycle management.

- **Database Interaction:** It provides static methods to interact with a local database collection (`_get_kbdb`).
  - `exists(name: str)`: Checks if a KB with a given name exists.
  - `get(kbid: str)`: Retrieves a KB by its ID.
  - `from_chunks(...)`: A crucial static method that takes KB metadata and a list of processed chunks, creates a new Qdrant collection, upserts the points, and saves the final KB record to the database.
- **Cloud Sync:**
  - `update_cloud_id(...)`: Updates the local KB record with an ID from the cloud.
  - `sync_to_cloud(...)`: An async method that handles the entire process of uploading the processed chunks and their embeddings to the cloud service. It includes retry logic and progress reporting.
