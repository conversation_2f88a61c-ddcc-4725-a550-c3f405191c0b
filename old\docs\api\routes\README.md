# REST API Routes

The handlers in `client_server/api/routes` define the RESTful API endpoints for the application. These are used for synchronous, request-response interactions.

## Routing Mechanism

The routing is managed by a custom `@route` decorator defined in `client_server/api/routes/__init__.py`.

```python
@route("GET", "/health_check")
async def health(request: Request = None):
    # ...
```

This decorator registers the handler, its HTTP method, and its path to a global `ROUTES` list. The `setup_routes` function in the `__init__.py` module then iterates over this list and registers all the handlers with the FastAPI application.

This approach keeps route definitions clean and co-located with their handler functions.

## API Endpoints

This section contains documentation for each of the API endpoints, grouped by their functionality. Each file in this directory typically corresponds to a specific resource or feature.
