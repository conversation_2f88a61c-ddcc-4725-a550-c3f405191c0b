from typing import Any
import time

import socketio
from pydantic import BaseModel

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_operation_stats, log_memory_usage
from client_server.services.dependencies import DependencyStatus
from client_server.services.dependencies.utils import DependencyRegistry


# -----------------------------------------------------------------------------
# Models
# -----------------------------------------------------------------------------


class DependencyUninstallPayload(BaseModel):
    """Payload for dependency uninstallation request"""

    request_id: str
    dependencies: list[str]


# -----------------------------------------------------------------------------
# Dependency uninstallation handling
# -----------------------------------------------------------------------------


async def process_dependency_uninstall(
    sio: socketio.AsyncServer,
    sid: str,
    payload: DependencyUninstallPayload,
):
    """Process the dependency uninstallation request and handle the response streaming"""
    overall_start = time.time()
    log_memory_usage("dependency_uninstall_start")

    LOGGER.info(
        f"Starting dependency uninstallation - Request ID: {payload.request_id}, "
        f"Dependencies: {payload.dependencies}"
    )

    try:
        # Emit start event
        await sio.emit(
            "dependency_uninstall:start",
            data={"request_id": payload.request_id},
            to=sid,
        )

        successful_uninstalls = 0
        failed_uninstalls = 0

        for dependency_id in payload.dependencies:
            dependency_start = time.time()
            LOGGER.info(f"Processing dependency removal: {dependency_id}")

            dependency = DependencyRegistry.get_dependency_by_id(dependency_id)
            if dependency is None:
                failed_uninstalls += 1
                LOGGER.warning(f"Unknown dependency: {dependency_id}")
                await sio.emit(
                    "dependency_uninstall:status",
                    data={
                        "request_id": payload.request_id,
                        "dependency_id": dependency_id,
                        "status": DependencyStatus.ERROR.value,
                        "data": f"Unknown dependency: {dependency_id}",
                    },
                    to=sid,
                )
                continue

            status_count = 0
            for status, data in dependency.uninstall():
                status_count += 1
                LOGGER.debug(
                    f"Dependency {dependency_id} uninstall status {status_count}: {status.value}"
                )

                # Emit status event
                await sio.emit(
                    "dependency_uninstall:status",
                    data={
                        "request_id": payload.request_id,
                        "dependency_id": dependency_id,
                        "status": status.value,
                        "data": data,
                    },
                    to=sid,
                )

                if status == DependencyStatus.ERROR:
                    failed_uninstalls += 1
                    LOGGER.error(
                        f"Dependency {dependency_id} uninstallation failed: {data}"
                    )
                elif status == DependencyStatus.READY:
                    successful_uninstalls += 1
                    LOGGER.info(f"Dependency {dependency_id} uninstallation completed")

            dependency_time = time.time() - dependency_start
            LOGGER.debug(
                f"Dependency {dependency_id} uninstall processed in {dependency_time:.2f}s"
            )

        # Emit end event
        await sio.emit(
            "dependency_uninstall:end",
            data={"request_id": payload.request_id},
            to=sid,
        )

        overall_time = time.time() - overall_start
        final_memory = log_memory_usage("dependency_uninstall_complete")

        # Log final statistics
        stats = log_operation_stats(
            "dependency_uninstallation",
            overall_start,
            len(payload.dependencies),
            success=(failed_uninstalls == 0),
        )

        LOGGER.info(
            f"Dependency uninstallation completed - Success: {successful_uninstalls}, "
            f"Failed: {failed_uninstalls}, Total time: {overall_time:.2f}s"
        )

    except Exception as e:
        overall_time = time.time() - overall_start
        LOGGER.error(
            f"Error during dependency uninstallation after {overall_time:.2f}s: {e}"
        )
        await sio.emit(
            "dependency_uninstall:error",
            data={"request_id": payload.request_id, "error": str(e)},
            to=sid,
        )
        raise


# -----------------------------------------------------------------------------
# Dependency event handler
# -----------------------------------------------------------------------------


async def handle_dependency_uninstall_event(
    sio: socketio.AsyncServer, sid: str, data: dict[str, Any]
):
    """Main handler for dependency uninstallation events"""
    start_time = time.time()
    LOGGER.info(f"Handling dependency uninstallation event for session {sid}")

    try:
        validation_start = time.time()
        payload = DependencyUninstallPayload.model_validate(data)
        validation_time = time.time() - validation_start
        LOGGER.debug(f"Payload validation completed in {validation_time:.3f}s")

        await process_dependency_uninstall(sio, sid, payload)

        total_time = time.time() - start_time
        LOGGER.info(f"Dependency uninstallation event completed in {total_time:.2f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(
            f"Error in dependency_uninstall_handler after {total_time:.2f}s: {e}"
        )
        raise
