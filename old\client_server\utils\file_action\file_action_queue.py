"""
Background Processing Queue System for File Actions

This module implements a background processing queue system for handling
multiple file analysis requests with configurable analysis depth, rate limiting,
and resource management.
"""

import asyncio
import time
import threading
from typing import Dict, List, Any, Optional, Callable, Coroutine
from dataclasses import dataclass, field
from enum import Enum
from queue import Queue, PriorityQueue
from concurrent.futures import ThreadPoolExecutor
import uuid

from client_server.core.logger import LOGGER
from client_server.utils.models.file_actions import FileActionRequest, FileActionResponse
from client_server.utils.prompts.file_action_prompts import AnalysisDepth


class QueuePriority(Enum):
    """Priority levels for queue items"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


class QueueStatus(Enum):
    """Status of queue items"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class QueueItem:
    """Represents an item in the processing queue"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    operation: str = ""
    request_data: Optional[FileActionRequest] = None
    priority: QueuePriority = QueuePriority.NORMAL
    status: QueueStatus = QueueStatus.PENDING
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Optional[FileActionResponse] = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    session_id: Optional[str] = None
    callback: Optional[Callable] = None
    
    def __lt__(self, other):
        """For priority queue ordering"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_at < other.created_at


class FileActionQueue:
    """
    Background processing queue for file actions with rate limiting and resource management
    """
    
    def __init__(
        self,
        max_workers: int = 3,
        max_queue_size: int = 100,
        rate_limit_per_minute: int = 60,
        enable_rate_limiting: bool = True
    ):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        self.rate_limit_per_minute = rate_limit_per_minute
        self.enable_rate_limiting = enable_rate_limiting
        
        # Queue and processing state
        self.queue = PriorityQueue(maxsize=max_queue_size)
        self.processing_items: Dict[str, QueueItem] = {}
        self.completed_items: Dict[str, QueueItem] = {}
        self.failed_items: Dict[str, QueueItem] = {}
        
        # Rate limiting
        self.request_timestamps: List[float] = []
        self.rate_limit_lock = threading.Lock()
        
        # Worker management
        self.workers: List[threading.Thread] = []
        self.shutdown_event = threading.Event()
        self.stats_lock = threading.Lock()
        
        # Statistics
        self.stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "average_processing_time": 0.0,
            "queue_size": 0,
            "active_workers": 0
        }
        
        # Start worker threads
        self._start_workers()
        
        LOGGER.info(f"FileActionQueue initialized with {max_workers} workers, max queue size: {max_queue_size}")
    
    def _start_workers(self):
        """Start worker threads for processing queue items"""
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"FileActionWorker-{i}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
            LOGGER.debug(f"Started worker thread: {worker.name}")
    
    def _worker_loop(self):
        """Main worker loop for processing queue items"""
        while not self.shutdown_event.is_set():
            try:
                # Get item from queue with timeout
                try:
                    item = self.queue.get(timeout=1.0)
                except:
                    continue  # Timeout, check shutdown event
                
                if item is None:  # Shutdown signal
                    break
                
                # Check rate limiting
                if self.enable_rate_limiting and not self._check_rate_limit():
                    # Put item back in queue and wait
                    self.queue.put(item)
                    time.sleep(1.0)
                    continue
                
                # Process the item
                self._process_item(item)
                
            except Exception as e:
                LOGGER.error(f"Error in worker loop: {e}")
                time.sleep(1.0)  # Brief pause before continuing
    
    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits"""
        with self.rate_limit_lock:
            current_time = time.time()
            
            # Remove timestamps older than 1 minute
            self.request_timestamps = [
                ts for ts in self.request_timestamps
                if current_time - ts < 60
            ]
            
            # Check if we're under the limit
            if len(self.request_timestamps) >= self.rate_limit_per_minute:
                return False
            
            # Add current timestamp
            self.request_timestamps.append(current_time)
            return True
    
    def _process_item(self, item: QueueItem):
        """Process a single queue item"""
        try:
            # Update item status
            item.status = QueueStatus.PROCESSING
            item.started_at = time.time()
            self.processing_items[item.id] = item
            
            LOGGER.info(f"Processing queue item {item.id}: {item.operation} for {item.request_data.file_path}")
            
            # Import here to avoid circular imports
            from client_server.api.routes.file_actions import _process_file_action_request
            
            # Process the request (this is async, so we need to run it in event loop)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    _process_file_action_request(
                        item.operation,
                        item.request_data,
                        item.session_id
                    )
                )
                
                # Update item with result
                item.result = result
                item.status = QueueStatus.COMPLETED if result.success else QueueStatus.FAILED
                item.completed_at = time.time()
                
                # Move to appropriate collection
                if result.success:
                    self.completed_items[item.id] = item
                    with self.stats_lock:
                        self.stats["successful"] += 1
                else:
                    self.failed_items[item.id] = item
                    item.error = result.error
                    with self.stats_lock:
                        self.stats["failed"] += 1
                
                # Call callback if provided
                if item.callback:
                    try:
                        item.callback(item)
                    except Exception as e:
                        LOGGER.error(f"Error calling callback for item {item.id}: {e}")
                
                LOGGER.info(f"Completed queue item {item.id} in {item.completed_at - item.started_at:.3f}s")
                
            finally:
                loop.close()
            
        except Exception as e:
            # Handle processing error
            item.status = QueueStatus.FAILED
            item.error = str(e)
            item.completed_at = time.time()
            self.failed_items[item.id] = item
            
            LOGGER.error(f"Error processing queue item {item.id}: {e}")
            
            # Retry if under retry limit
            if item.retry_count < item.max_retries:
                item.retry_count += 1
                item.status = QueueStatus.PENDING
                item.started_at = None
                self.queue.put(item)
                LOGGER.info(f"Retrying queue item {item.id} (attempt {item.retry_count}/{item.max_retries})")
            else:
                with self.stats_lock:
                    self.stats["failed"] += 1
        
        finally:
            # Remove from processing items
            self.processing_items.pop(item.id, None)
            
            # Update statistics
            with self.stats_lock:
                self.stats["total_processed"] += 1
                self.stats["queue_size"] = self.queue.qsize()
                self.stats["active_workers"] = len(self.processing_items)
                
                # Update average processing time
                if item.completed_at and item.started_at:
                    processing_time = item.completed_at - item.started_at
                    total_time = self.stats["average_processing_time"] * (self.stats["total_processed"] - 1)
                    self.stats["average_processing_time"] = (total_time + processing_time) / self.stats["total_processed"]
    
    def enqueue(
        self,
        operation: str,
        request_data: FileActionRequest,
        priority: QueuePriority = QueuePriority.NORMAL,
        session_id: Optional[str] = None,
        callback: Optional[Callable] = None,
        max_retries: int = 3
    ) -> str:
        """
        Add an item to the processing queue
        
        Args:
            operation: Type of operation to perform
            request_data: Request data for the operation
            priority: Priority level for the item
            session_id: Optional session identifier
            callback: Optional callback function to call when processing completes
            max_retries: Maximum number of retry attempts
        
        Returns:
            Queue item ID
        
        Raises:
            ValueError: If queue is full
        """
        if self.queue.full():
            raise ValueError("Queue is full")
        
        item = QueueItem(
            operation=operation,
            request_data=request_data,
            priority=priority,
            session_id=session_id,
            callback=callback,
            max_retries=max_retries
        )
        
        self.queue.put(item)
        
        with self.stats_lock:
            self.stats["queue_size"] = self.queue.qsize()
        
        LOGGER.info(f"Enqueued {operation} operation for {request_data.file_path} with priority {priority.name}")
        
        return item.id
    
    def get_item_status(self, item_id: str) -> Optional[QueueItem]:
        """Get the status of a queue item"""
        # Check processing items
        if item_id in self.processing_items:
            return self.processing_items[item_id]
        
        # Check completed items
        if item_id in self.completed_items:
            return self.completed_items[item_id]
        
        # Check failed items
        if item_id in self.failed_items:
            return self.failed_items[item_id]
        
        # Check if still in queue
        # Note: This is inefficient for large queues, but necessary for status checking
        queue_items = []
        try:
            while not self.queue.empty():
                item = self.queue.get_nowait()
                queue_items.append(item)
                if item.id == item_id:
                    # Put all items back
                    for qi in queue_items:
                        self.queue.put(qi)
                    return item
        except:
            pass
        
        # Put all items back
        for item in queue_items:
            self.queue.put(item)
        
        return None

    def cancel_item(self, item_id: str) -> bool:
        """
        Cancel a queue item if it hasn't started processing

        Args:
            item_id: ID of the item to cancel

        Returns:
            True if item was cancelled, False if not found or already processing
        """
        # Can't cancel items that are already processing
        if item_id in self.processing_items:
            return False

        # Can't cancel completed or failed items
        if item_id in self.completed_items or item_id in self.failed_items:
            return False

        # Try to find and remove from queue
        queue_items = []
        found_item = None

        try:
            while not self.queue.empty():
                item = self.queue.get_nowait()
                if item.id == item_id:
                    found_item = item
                    break
                queue_items.append(item)
        except:
            pass

        # Put remaining items back
        for item in queue_items:
            self.queue.put(item)

        if found_item:
            found_item.status = QueueStatus.CANCELLED
            found_item.completed_at = time.time()
            self.failed_items[item_id] = found_item

            with self.stats_lock:
                self.stats["queue_size"] = self.queue.qsize()

            LOGGER.info(f"Cancelled queue item {item_id}")
            return True

        return False

    def get_queue_stats(self) -> Dict[str, Any]:
        """Get current queue statistics"""
        with self.stats_lock:
            stats = self.stats.copy()

        stats.update({
            "pending_items": self.queue.qsize(),
            "processing_items": len(self.processing_items),
            "completed_items": len(self.completed_items),
            "failed_items": len(self.failed_items),
            "worker_threads": len(self.workers),
            "rate_limit_per_minute": self.rate_limit_per_minute,
            "rate_limiting_enabled": self.enable_rate_limiting
        })

        return stats

    def get_pending_items(self) -> List[Dict[str, Any]]:
        """Get list of pending items in the queue"""
        queue_items = []
        temp_items = []

        try:
            while not self.queue.empty():
                item = self.queue.get_nowait()
                temp_items.append(item)
                queue_items.append({
                    "id": item.id,
                    "operation": item.operation,
                    "file_path": item.request_data.file_path if item.request_data else None,
                    "priority": item.priority.name,
                    "created_at": item.created_at,
                    "retry_count": item.retry_count
                })
        except:
            pass

        # Put items back
        for item in temp_items:
            self.queue.put(item)

        return queue_items

    def get_processing_items(self) -> List[Dict[str, Any]]:
        """Get list of currently processing items"""
        return [
            {
                "id": item.id,
                "operation": item.operation,
                "file_path": item.request_data.file_path if item.request_data else None,
                "started_at": item.started_at,
                "session_id": item.session_id
            }
            for item in self.processing_items.values()
        ]

    def clear_completed_items(self, older_than_hours: int = 24):
        """Clear completed items older than specified hours"""
        current_time = time.time()
        cutoff_time = current_time - (older_than_hours * 3600)

        # Clear completed items
        to_remove = [
            item_id for item_id, item in self.completed_items.items()
            if item.completed_at and item.completed_at < cutoff_time
        ]

        for item_id in to_remove:
            del self.completed_items[item_id]

        # Clear failed items
        to_remove = [
            item_id for item_id, item in self.failed_items.items()
            if item.completed_at and item.completed_at < cutoff_time
        ]

        for item_id in to_remove:
            del self.failed_items[item_id]

        LOGGER.info(f"Cleared {len(to_remove)} old completed/failed items")

    def shutdown(self, timeout: float = 30.0):
        """
        Shutdown the queue and all worker threads

        Args:
            timeout: Maximum time to wait for workers to finish
        """
        LOGGER.info("Shutting down FileActionQueue...")

        # Signal shutdown
        self.shutdown_event.set()

        # Add None items to wake up workers
        for _ in range(len(self.workers)):
            try:
                self.queue.put(None, timeout=1.0)
            except:
                pass

        # Wait for workers to finish
        start_time = time.time()
        for worker in self.workers:
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time > 0:
                worker.join(timeout=remaining_time)

            if worker.is_alive():
                LOGGER.warning(f"Worker {worker.name} did not shutdown gracefully")

        LOGGER.info("FileActionQueue shutdown complete")


# Global queue instance
_global_queue: Optional[FileActionQueue] = None
_queue_lock = threading.Lock()


def get_file_action_queue() -> FileActionQueue:
    """Get the global file action queue instance (singleton)"""
    global _global_queue

    if _global_queue is None:
        with _queue_lock:
            if _global_queue is None:
                # Load configuration from settings if available
                try:
                    from client_server.utils.path_selector import PathSelector
                    settings = PathSelector._load_settings()
                    queue_config = settings.get("file_action_queue", {})

                    _global_queue = FileActionQueue(
                        max_workers=queue_config.get("max_workers", 3),
                        max_queue_size=queue_config.get("max_queue_size", 100),
                        rate_limit_per_minute=queue_config.get("rate_limit_per_minute", 60),
                        enable_rate_limiting=queue_config.get("enable_rate_limiting", True)
                    )
                except Exception as e:
                    LOGGER.warning(f"Failed to load queue config from settings, using defaults: {e}")
                    _global_queue = FileActionQueue()

    return _global_queue


def shutdown_file_action_queue():
    """Shutdown the global file action queue"""
    global _global_queue

    if _global_queue is not None:
        _global_queue.shutdown()
        _global_queue = None
