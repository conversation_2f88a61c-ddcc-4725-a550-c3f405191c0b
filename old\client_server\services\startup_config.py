import json
from pathlib import Path
from typing import Optional, List, Dict, Any

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.services.db_handler import Database


def normalize_path_to_unix(path_str: str) -> str:
    """
    Convert Windows-style paths to Unix format for cross-platform compatibility.

    Args:
        path_str: Path string to normalize

    Returns:
        Normalized path in Unix format
    """
    if not path_str:
        return path_str

    # Convert Path object to string if needed
    if isinstance(path_str, Path):
        path_str = str(path_str)

    # Convert Windows drive letters (C:\) to Unix format (/c/)
    if len(path_str) >= 3 and path_str[1:3] == ':\\':
        drive_letter = path_str[0].lower()
        rest_of_path = path_str[3:]
        path_str = f"/{drive_letter}/{rest_of_path}"

    # Replace backslashes with forward slashes
    path_str = path_str.replace('\\', '/')

    # Remove duplicate slashes
    while '//' in path_str:
        path_str = path_str.replace('//', '/')

    return path_str


def extract_kb_paths_from_database() -> List[str]:
    """
    Extract file paths from existing knowledge bases in the database.

    Returns:
        List of all valid local paths found, empty list if no valid paths exist
    """
    try:
        # Use existing database infrastructure
        db = Database()
        knowledge_base_collection = db["knowledge_bases"]

        LOGGER.info("🔍 Querying database for existing knowledge bases...")

        # Get all knowledge bases from the database
        all_kbs = list(knowledge_base_collection.find())
        total_kbs = len(all_kbs)

        LOGGER.info(f"📊 Found {total_kbs} total knowledge bases in database")

        if total_kbs == 0:
            LOGGER.info("📭 No knowledge bases found in database")
            return []

        valid_paths = []
        local_kb_count = 0
        auto_indexed_count = 0

        # Process each knowledge base
        for kb_record in all_kbs:
            try:
                kb_id = kb_record.get('id', 'Unknown')
                source = kb_record.get('source', 'Unknown')
                is_auto_indexed = kb_record.get('isAutoIndexed', False)

                LOGGER.debug(f"📋 Processing KB: ID={kb_id}, Source={source}, AutoIndexed={is_auto_indexed}")

                # Only process LOCAL knowledge bases
                if source != 'LOCAL':
                    LOGGER.debug(f"⏭️  Skipping non-local KB: {kb_id} (source: {source})")
                    continue

                local_kb_count += 1

                if is_auto_indexed:
                    auto_indexed_count += 1

                # Extract path from metadata
                metadata = kb_record.get('metadata', {})
                if not isinstance(metadata, dict):
                    LOGGER.debug(f"⚠️  KB {kb_id} has invalid metadata structure")
                    continue

                kb_path = metadata.get('path')
                if not kb_path:
                    LOGGER.debug(f"⚠️  KB {kb_id} has no path in metadata")
                    continue

                LOGGER.debug(f"📁 Found path in KB {kb_id}: {kb_path}")

                # Validate that the path exists on the file system
                try:
                    path_obj = Path(kb_path)
                    if path_obj.exists():
                        valid_paths.append(kb_path)
                        LOGGER.info(f"✅ Valid path found: {kb_path} (from KB: {kb_id})")
                    else:
                        LOGGER.debug(f"❌ Path does not exist: {kb_path} (from KB: {kb_id})")
                except Exception as e:
                    LOGGER.debug(f"❌ Error validating path {kb_path}: {e}")

            except Exception as e:
                LOGGER.warning(f"⚠️  Error processing KB record: {e}")
                continue

        # Log summary statistics
        LOGGER.info(f"📈 Knowledge base summary:")
        LOGGER.info(f"   Total KBs: {total_kbs}")
        LOGGER.info(f"   Local KBs: {local_kb_count}")
        LOGGER.info(f"   Auto-indexed KBs: {auto_indexed_count}")
        LOGGER.info(f"   Valid paths found: {len(valid_paths)}")

        # Return all valid paths found
        if valid_paths:
            LOGGER.info(f"🎯 Returning {len(valid_paths)} valid paths for configuration")
            for i, path in enumerate(valid_paths, 1):
                LOGGER.info(f"   {i}. {path}")
            return valid_paths
        else:
            LOGGER.info("❌ No valid local paths found in knowledge bases")
            return []

    except Exception as e:
        LOGGER.error(f"❌ Error querying knowledge base database: {e}")
        LOGGER.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
        return []


def create_file_path_config(codemate_folder: Path) -> bool:
    """
    Create file_path.json configuration file if it doesn't exist.
    Dynamically determines the path based on existing knowledge bases.

    Args:
        codemate_folder: Path to .codemate folder

    Returns:
        True if file was created, False if it already existed
    """
    config_file = codemate_folder / "file_path.json"

    # Check if config file already exists
    if config_file.exists():
        LOGGER.info(f"📄 Configuration file already exists: {config_file}")
        return False

    LOGGER.info("🔍 Determining configuration paths from existing knowledge bases...")

    # Try to extract paths from existing knowledge bases
    kb_paths = extract_kb_paths_from_database()

    if kb_paths:
        # Use paths from knowledge bases, normalize each one
        monitored_paths = [path for path in kb_paths]
        LOGGER.info(f"✅ Using {len(monitored_paths)} paths from knowledge bases:")
        for i, path in enumerate(monitored_paths, 1):
            LOGGER.info(f"   {i}. {path}")
    else:
        # Use fallback paths
        LOGGER.info(f"🔄 Using {len(monitored_paths)} fallback paths:")
        for i, path in enumerate(monitored_paths, 1):
            LOGGER.info(f"   {i}. {path}")

    # Create configuration data with monitored_paths array
    config_data = {
        "monitored_paths": monitored_paths
    }

    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)

        LOGGER.info(f"✅ Created configuration file: {config_file}")
        LOGGER.info(f"📍 Final monitored paths saved: {len(monitored_paths)} paths")
        return True

    except Exception as e:
        LOGGER.error(f"❌ Failed to create configuration file: {e}")
        LOGGER.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
        return False


def perform_startup_configuration() -> bool:
    """
    Perform automatic file path configuration during server startup.
    Uses existing PathSelector infrastructure to manage configuration.

    Returns:
        True if configuration was performed, False if skipped or failed
    """
    try:
        LOGGER.info("🚀 Starting automatic file path configuration...")

        # Use existing PathSelector to get .codemate folder
        # This automatically creates the folder if it doesn't exist
        codemate_folder = PathSelector.get_base_path()
        LOGGER.info(f"📁 Using .codemate folder: {codemate_folder}")

        # Check if configuration file already exists
        config_file = codemate_folder / "file_path.json"
        if config_file.exists():
            LOGGER.info("⏭️  Configuration file already exists, skipping setup")
            LOGGER.info(f"📄 Configuration file location: {config_file}")
            return False

        # Create configuration file
        config_created = create_file_path_config(codemate_folder)
        if not config_created:
            LOGGER.error("❌ Failed to create configuration file")
            return False

        LOGGER.info("✅ Automatic file path configuration completed successfully")
        return True

    except Exception as e:
        LOGGER.error(f"❌ Error during startup configuration: {e}")
        LOGGER.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
        return False
