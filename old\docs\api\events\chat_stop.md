# Chat Stop Event (`chat_stop.py`)

The `chat_stop.py` module provides a mechanism to gracefully cancel an ongoing chat request.

## Overview

When a user decides to stop a chat response that is in the process of being generated, the client sends a `chat_stop` event. This handler adds the `request_id` of the chat to a global set of cancelled IDs. Other parts of the application, particularly the chat streaming loop in `chat.py`, check this set to determine whether they should stop processing and terminate the stream.

## Data Models

- `ChatStopPayload`:
  - `request_id: str`: The unique identifier of the chat request to be cancelled.

## Event Handler

- `handle_chat_stop_event(sio, sid, data)`:
  - Validates the incoming payload to extract the `request_id`.
  - Adds the `request_id` to the `G_CANCELLED_REQUEST_IDS` global state.

## Event Flow

The `chat_stop` event interrupts an in-progress chat stream.

<details>
<summary>View Chat Stop Event Flow</summary>

```mermaid
sequenceDiagram
    participant E as User's Editor
    participant S as Our Server
    participant R as Response Generator

    E->>S: User clicks "Stop"
    S->>S: Receive stop request
    S->>S: Flag this conversation to be stopped
    Note over R: The next time the generator tries to add a word...
    R->>R: Check for a stop flag
    alt Stop flag is found
        R->>R: Stop generating the response
    end
```

</details>
