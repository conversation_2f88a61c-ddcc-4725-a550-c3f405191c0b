# Docs Chunker (`docs.py`)

The `docs.py` module provides the `DocsChunker`, an `IChunker` implementation for processing online documentation or other web-based content. It operates in two distinct phases: scraping and chunking.

## Classes

### `<PERSON>s<PERSON>hunker(IChunker)`

This class handles the end-to-end process of turning a website into a collection of text chunks.

#### `__init__(self, metadata: QdrantDocsMetadata)`

- **Parameters:**
  - `metadata` (`QdrantDocsMetadata`): An object containing the list of starting URLs to be scraped (`metadata.urls`).

#### `process(self, progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

This is the main entry point, which calls the internal `_make_chunks` method.

#### `_make_chunks(self, urls: list[str], progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

This method orchestrates the two-phase process:

1.  **Scraping:**

    - It initializes a `PlaywrightCrawlerBackend`.
    - It calls the crawler's `scrape` method to recursively navigate from the starting URL(s) and collect the content of the pages.
    - During this phase, it reports progress back to the user via the `progress_callback`, mapping the scraping progress to the first 70% of the total progress bar.

2.  **Chunking:**
    - Once scraping is complete, it takes the dictionary of scraped content (`url -> content`).
    - It uses a `ThreadPoolExecutor` to process the content of each URL in parallel. It dispatches a `make_qdrant_knowledgebase_chunks_from_content` task (from `chunkers.utils`) for each page's content.
    - It reports the progress of this second phase, mapping it to the remaining 30% of the progress bar.
    - **Returns:**
      - A flattened list of `QdrantKnowledgeBaseChunk` objects from all the scraped pages.
