# GitHub Chunker (`github.py`)

The `github.py` module provides the `GithubChunker`, an `IChunker` implementation for creating a knowledge base from a GitHub repository. It automates the process of fetching the code and preparing it for embedding.

The process involves three main stages: cloning, discovering, and chunking.

## Classes

### `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(IChunker)`

This class manages the entire workflow for processing a GitHub repository.

#### `__init__(self, metadata: QdrantGithubMetadata)`

- **Parameters:**
  - `metadata` (`QdrantGithubMetadata`): An object containing the repository details, such as the `repo_url`, `branch`, and an optional `accessToken` for private repositories.

#### `process(self, progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

The main entry point that executes the three stages in sequence.

### Stage 1: Clone Repository

#### `_clone_repository(self)`

- This method constructs and executes a `git clone` command.
- It uses the repository URL, branch, and access token from the metadata.
- The repository is cloned into a temporary directory managed by the `PathSelector`.
- It handles errors, such as if the Git CLI is not installed.

### Stage 2: Discover Files

#### `_discover_files(self) -> list[str]`

- After cloning, this method scans the local repository directory.
- It uses the `get_files` utility to find all relevant, text-readable source files, respecting the standard exclusion rules.
- It logs statistics about the discovered files, such as the total repository size.

### Stage 3: Make Chunks

#### `_make_chunks(self, files: list[str], progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

- This final stage is very similar to the `CodebaseChunker`.
- It takes the list of discovered file paths.
- It uses a `ThreadPoolExecutor` to process and chunk the files in parallel.
- It reports progress using the `progress_callback`.
- **Returns:**
  - A flattened list of `QdrantKnowledgeBaseChunk` objects from all the repository files.
