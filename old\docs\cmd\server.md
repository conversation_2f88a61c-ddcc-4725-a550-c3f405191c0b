# Server Script (`client_server/cmd/server.py`)

This script is the main entry point for running the client server. It sets up and runs **dual-port architecture** with separate servers for HTTP REST APIs and WebSocket communication, providing better resource isolation and scalability.

## Core Components

- **HTTP Server (Port 45213)**: FastAPI application serving RESTful API endpoints for synchronous operations.
- **WebSocket Server (Port 45214)**: Socket.IO server enabling real-time, bidirectional event-based communication for features like chat, progress updates, and notifications.
- **Uvicorn**: Acts as the ASGI server to run both applications concurrently.

## Key Functionality

### 1. Dual-Port Server Initialization

- **HTTP Server**: A `FastAPI` app instance is created to handle REST API requests on port 45213.
- **WebSocket Server**: A `Socket.IO` `AsyncServer` is set up for WebSocket connections on port 45214, configured for asynchronous operation with `asgi`.
- **Concurrent Execution**: Both servers run concurrently using `asyncio.gather()` for optimal resource utilization.

### 2. Middleware

The server uses two layers of CORS (Cross-Origin Resource Sharing) middleware:

- **`CORSMiddleware`**: A standard FastAPI middleware configured to allow requests from any origin (`*`), which is useful for development and general API access.
- **`VSCodeWebviewCORSMiddleware`**: A custom middleware specifically designed to handle requests from VSCode Webviews. It dynamically sets the `Access-Control-Allow-Origin` header to the specific `vscode-webview://` origin of the request, which is a security requirement for extensions running in a webview context.

### 3. WebSocket Event Handling

The server listens for and handles numerous WebSocket events from the client. A `make_handler` helper function is used to simplify the registration of these event handlers.

Key events include:

- **Connection Management**: `connect`, `disconnect`, and `heartbeat` for managing client sessions and ensuring connections are alive.
- **Core Features**: `chat`, `chat_stop` (for LLM interactions), `inline_suggestion` (for code completion), and `upload` (for knowledge base creation).
- **Filesystem**: `get_folder_paths_recursive` and `get_file_paths_recursive` to interact with the user's workspace.
- **Swagger**: `swagger_gen` to generate API documentation.
- **Dependencies**: `dependency_install` and `dependency_uninstall` for managing project dependencies.

### 4. REST API Routes

All REST endpoints are attached to the FastAPI application by calling `setup_routes(app)`. This keeps the route definitions organized and separate from the main server script.

### 5. Startup and Shutdown Procedures

The `main` function orchestrates the server's lifecycle:

- **Pre-flight Checks & Setup**:
  - `setup_qdrant()`: Ensures the Qdrant vector database is running and accessible.
  - `preload_models_if_needed()`: To reduce latency on initial requests, the server preloads necessary machine learning models (for embeddings, chat, and autocomplete) into memory at startup. This is controlled by constants.
- **Background Tasks**:
  - A background thread is launched to periodically run `cleanup_old_sessions`, which removes stale or disconnected client session data.
- **Graceful Shutdown**:
  - `atexit` handlers are registered to properly release resources, such as disposing of loaded ML models, when the server process exits.

## How to Run the Server

To start the server for development, run the script directly:

```sh
python -m client_server.cmd.server
```

The server will start on `127.0.0.1` with:
- **HTTP REST API**: Port 45213
- **WebSocket connections**: Port 45214
