# Embeddings Service

The Embeddings service is responsible for converting text into numerical vector representations, also known as embeddings. These embeddings are fundamental for semantic search, knowledge base lookups, and other AI-powered features.

## Design

The service follows a clear interface-implementation pattern and uses a builder to select the appropriate backend, adhering to the principles outlined in the project overview.

- **`IEmbeddingBackend` Interface**: Defines the contract for all embedding providers. It ensures that any backend can be used interchangeably.
- **Implementations**: Concrete classes that implement `IEmbeddingBackend` for different services or platforms.
  - `CloudEmbeddingsBackend`: Uses a remote API to generate embeddings.
  - `OllamaEmbeddingsBackend`: Uses a local Ollama instance.
  - `InferXEmbeddingsBackend`: Uses the local InferX engine, optimized for Snapdragon ARM platforms.
- **`EmbeddingInferenceBuilder`**: A factory class that decides which implementation to use based on the environment (e.g., internet connectivity, platform architecture).

## Core Components

### `IEmbeddingBackend` Interface

Located in `client_server/services/embeddings/__init__.py`, this ABC defines two methods:

- `generate(content: str)`: Asynchronously generates an embedding for a single string of text.
- `generate_batch(content: list[str])`: Asynchronously generates embeddings for a list of strings, which is more efficient for multiple texts.

### Implementations

1.  **`CloudEmbeddingsBackend` (`cloud.py`)**

    - This is the default backend when an internet connection is available.
    - It sends a POST request to a cloud endpoint (`G_BASE_URL.get().embeddings`).
    - It includes retry logic to handle transient network errors.

2.  **`OllamaEmbeddingsBackend` (`ollama.py`)**

    - Used for local embedding generation on non-ARM platforms.
    - It manages a local Ollama server process, starting it if necessary.
    - It uses the `ollama.embed` function with the `nomic-embed-text:v1.5` model.

3.  **`InferXEmbeddingsBackend` (`inferx.py`)**
    - A highly specialized backend for Snapdragon ARM-based devices.
    - It uses a local ONNX model (`model.onnx`) and the `QNNExecutionProvider` for hardware-accelerated inference.
    - It includes its own tokenizer (`_InferXSimpleTokenizer`) tailored for the specific ONNX model.

### `EmbeddingInferenceBuilder`

Found in `client_server/services/embeddings/utils.py`, this builder class is the primary way to get an embedding backend instance.

- `create(is_local: bool = False)`: This static method is the factory.
  - It first checks for internet connectivity using `check_internet_connection()`.
  - If `is_local` is `True` or if there's no internet, it chooses a local backend.
    - It calls `PlatformDetector.is_snapdragon_arm()` to decide between `InferXEmbeddingsBackend` (for ARM) and `OllamaEmbeddingsBackend` (for other platforms).
  - If there is internet and `is_local` is `False`, it returns the `CloudEmbeddingsBackend`.
- `dispose()`: A static method to clear the cached singleton instance.

## Workflow

1.  **Instantiation**: Code that needs to generate embeddings calls `EmbeddingInferenceBuilder.create()`. The builder handles the logic of selecting the best backend.
2.  **Generation**: The calling code then uses the `generate()` or `generate_batch()` method on the returned instance, without needing to know which specific backend is being used.
3.  **Example**:

    ```python
    # Get the appropriate backend
    embedding_backend = EmbeddingInferenceBuilder.create()

    # Generate an embedding
    vector = await embedding_backend.generate("This is a test sentence.")
    ```
