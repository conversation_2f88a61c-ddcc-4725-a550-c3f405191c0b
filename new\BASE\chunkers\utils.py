"""
WHAT DOES THIS FILE DO?

This file defines supported programming languages and their corresponding text splitters.
It uses the RecursiveCharacterTextSplitter to create language-specific text splitters
for various programming languages. These splitters are used to chunk code files into
smaller, manageable pieces for processing or analysis.
"""

import os
import time
from uuid import uuid4

import array
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
from typing import List, Tuple, Optional

from langchain_text_splitters import Language
from langchain_text_splitters import RecursiveCharacterTextSplitter as RCTS

from . import create_chunk, create_chunk_metadata


EXTENSION_LANGAUGE_LIST = {
    "py": "python",
    "js": "javascript",
    "ts": "typescript",
    "java": "java",
    "cpp": "c++",
    "go": "go",
    "php": "php",
    "html": "html",
    "css": "css",
    "json": "json",
    "yaml": "yaml",
    "yml": "yaml",
    "md": "markdown",
    "txt": "markdown",
    "rst": "restructuredtext",
    "rb": "ruby",
    "rs": "rust",
    "scala": "scala",
    "swift": "swift",
    "sol": "solidity",
    "csharp": "csharp",
    "cobol": "cobol",
    "c": "c",
    "lua": "lua",
    "perl": "perl",
    "haskell": "haskell",
    "elixir": "elixir",
    "powershell": "powershell",
    "sql": "sql",
    "xml": "xml",
    "toml": "toml",
    "ini": "ini",
}


# Cache for line position arrays
@lru_cache(maxsize=128)
def get_line_positions(content: str) -> array.array:
    """Cache and return line positions for a given content."""
    positions = array.array("L", [0])  # 'L' for unsigned long
    pos = 0
    for line in content.splitlines(keepends=True):
        pos += len(line)
        positions.append(pos)
    return positions


# Cache for splitter instances based on language, chunk size, and overlap
@lru_cache(maxsize=32)
def get_cached_splitter(language: str, chunk_size: int, chunk_overlap: int) -> RCTS:
    supported_languages = [x.value.lower() for x in Language]
    language = language if language in supported_languages else "markdown"
    return RCTS.from_language(
        language=Language(language),
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
    )


def binary_search(
    arr: array.array, target: int, start: int = 0, end: Optional[int] = None
) -> int:
    """Optimized binary search using array.array for better performance."""
    if end is None:
        end = len(arr) - 1

    while start <= end:
        mid = (start + end) >> 1  # Faster integer division
        if arr[mid] <= target:
            start = mid + 1
        else:
            end = mid - 1
    return start


def find_line_numbers_batch(content: str, chunks: List[str]) -> List[Tuple[int, int]]:
    """
    Efficiently find line numbers for multiple chunks using cached line positions.
    """
    line_positions = get_line_positions(content)
    total_lines = len(line_positions)
    results = []

    for chunk_content in chunks:
        chunk_start = content.find(chunk_content)
        if chunk_start == -1:
            results.append((1, 1))
            continue

        chunk_end = chunk_start + len(chunk_content)
        start_line = binary_search(line_positions, chunk_start)
        end_line = binary_search(line_positions, chunk_end)

        results.append((start_line, end_line))

    return results


def process_chunk(content: str, splitter: RCTS) -> List[str]:
    """Process a single chunk of content."""
    return [chunk.page_content for chunk in splitter.create_documents([content])]


def create_chunks(
    content: str,
    extension: str,
    SPLIT_CHUNK_SIZE: int = 4000,
    SPLIT_CHUNK_OVERLAP: int = 0,
) -> List[Tuple[str, int, int]]:
    """
    Create chunks from content with optimized parallel processing.
    Returns list of tuples (chunk_content, start_line, end_line)
    """
    if not content:
        return []

    language = EXTENSION_LANGAUGE_LIST.get(extension, "markdown")
    splitter = get_cached_splitter(language, SPLIT_CHUNK_SIZE, SPLIT_CHUNK_OVERLAP)

    # Calculate chunk size based on content length
    content_length = len(content)
    chunk_size = max(
        SPLIT_CHUNK_SIZE, content_length // 8
    )  # Reasonable default division

    # Process in parallel
    chunks = []
    with ThreadPoolExecutor() as executor:
        futures = []
        # Create chunks with overlap to avoid missing content at boundaries
        for i in range(0, content_length, chunk_size):
            # Add overlap to avoid missing content at chunk boundaries
            start = max(0, i - SPLIT_CHUNK_OVERLAP)
            end = min(content_length, i + chunk_size + SPLIT_CHUNK_OVERLAP)
            content_chunk = content[start:end]
            futures.append(executor.submit(process_chunk, content_chunk, splitter))

        # Gather results
        for future in futures:
            chunks.extend(future.result())

    # Remove duplicate chunks that might have been created due to overlap
    seen = set()
    unique_chunks = []
    for chunk in chunks:
        if chunk not in seen:
            seen.add(chunk)
            unique_chunks.append(chunk)
    chunks = unique_chunks

    # Find line numbers efficiently in batch
    line_numbers = find_line_numbers_batch(content, chunks)

    # Combine results
    return list(
        zip(
            chunks,
            (start for start, _ in line_numbers),
            (end for _, end in line_numbers),
        )
    )


def make_chunks_from_content(content: str, extension: str) -> list[dict]:
    """Create chunks from content and return as simple dictionaries."""
    try:
        content_length = len(content)
        content_lines = content.count("\n") + 1
        print(f"File content stats - Length: {content_length} chars, Lines: {content_lines}")

        print(f"Processing file with extension '{extension}'")

        chunk_start_time = time.time()
        text_chunks = create_chunks(content=content, extension=extension)
        chunk_time = time.time() - chunk_start_time

        print(f"Created {len(text_chunks)} text chunks (took {chunk_time:.2f}s)")

        skipped_chunks = 0
        processed_chunks = []
        for i, (text, start_line, end_line) in enumerate(text_chunks):
            if start_line is None or end_line is None:
                skipped_chunks += 1
                print(f"Skipping chunk {i} with invalid line numbers")
                continue

            metadata = create_chunk_metadata(
                chunk_id=str(uuid4()),
                file_path="",
                name="",
                content=text,
                additional_metadata={
                    "line_start": start_line,
                    "line_end": end_line,
                }
            )
            chunk = create_chunk(metadata=metadata, embeddings=[])
            processed_chunks.append(chunk)

        if skipped_chunks > 0:
            print(f"Skipped {skipped_chunks} chunks with invalid line numbers")

        print(f"Successfully processed {len(processed_chunks)} chunks")
        return processed_chunks

    except Exception as e:
        print(f"File processing error: {e}")
        return []


def make_chunks_from_file(path: str) -> list[dict]:
    """Create chunks from file and return as simple dictionaries."""
    print(f"Processing file for chunks: {path}")

    # Check if it's a directory
    if os.path.isdir(path):
        print(f"Skipping directory: {path}")
        return []

    # Get file size for logging
    try:
        file_size = os.path.getsize(path)
        print(f"File size: {file_size} bytes")
    except OSError:
        print(f"Could not get file size for: {path}")

    content = ""

    # Try UTF-8 first
    try:
        read_start = time.time()
        with open(path, "r", encoding="utf-8") as f:
            content = f.read()
        read_time = time.time() - read_start
        print(f"Successfully read file with UTF-8 encoding: {path} (took {read_time:.2f}s)")
    except UnicodeDecodeError:
        print(f"UTF-8 failed, trying cp1252 encoding for: {path}")
        # Fall back to cp1252 (Windows default) if UTF-8 fails
        try:
            read_start = time.time()
            with open(path, "r", encoding="cp1252") as f:
                content = f.read()
            read_time = time.time() - read_start
            print(f"Successfully read file with cp1252 encoding: {path} (took {read_time:.2f}s)")
        except Exception as inner_e:
            print(f"Failed to read file with cp1252 encoding {path}: {inner_e}")
            return []
    except Exception as e:
        print(f"Failed to read file {path}: {e}")
        return []

    if not content:
        print(f"No content found for file: {path}")
        return []

    file_extension = os.path.splitext(path)[1].replace(".", "")
    chunks = make_chunks_from_content(content, file_extension)
    for chunk in chunks:
        chunk["metadata"]["file"] = path
        chunk["metadata"]["name"] = os.path.basename(path)
    return chunks
