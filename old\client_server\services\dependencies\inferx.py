import zipfile
from pathlib import Path
from typing import Generator, <PERSON><PERSON>, Any

import requests
from overrides import override

from client_server.core.logger import LOGGER
from client_server.utils.platform_detector import PlatformDetector
from . import IDependency, DependencyStatus
from .registry import DependencyRegistry

# -----------------------------------------------------------------------------


class InferXDependency(IDependency):
    def __init__(
        self,
        target_dir: Path,
        *,
        id: str | None = None,
        name: str | None = None,
        version: str | None = None,
        description: str | None = None,
    ):
        super().__init__()
        self.target_dir = target_dir
        # Store properties
        self._id = id or "inferx"
        self._name = name or "InferX"
        self._version = version or "1.0.0"
        self._description = (
            description or "InferX is a specialized inference engine for CodeMate AI."
        )

    @property
    @override
    def id(self) -> str:
        return self._id

    @property
    @override
    def name(self) -> str:
        return self._name

    @property
    @override
    def version(self) -> str:
        return self._version

    @property
    @override
    def description(self) -> str:
        return self._description

    def _get_download_url(self) -> str:
        """Get the InferX download URL."""
        return "https://huggingface.co/datasets/codemateai/subsystem/resolve/main/inferx.zip?download=true"

    def _setup_inferx(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Download and extract InferX to the target directory."""
        self.target_dir.mkdir(parents=True, exist_ok=True)
        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Creating target directory"

        download_url = self._get_download_url()
        archive_path = self.target_dir / "inferx.zip"

        # Download the file with progress bar
        status = DependencyStatus.INSTALLING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, f"Downloading InferX"
        response = requests.get(download_url, stream=True)
        response.raise_for_status()

        total_size = int(response.headers.get("content-length", 0))
        block_size = 8192
        downloaded_size = 0

        with open(archive_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=block_size):
                size = f.write(chunk)
                downloaded_size += size
                progress = (downloaded_size / total_size) * 100
                yield status, progress

        # Extract the archive
        yield status, "Extracting InferX archive"
        with zipfile.ZipFile(archive_path, "r") as zip_ref:
            file_list = zip_ref.namelist()
            for i, file in enumerate(file_list):
                zip_ref.extract(file, self.target_dir.parent)
                progress = ((i + 1) / len(file_list)) * 100
                yield status, progress

        # Make files executable on Unix-like systems
        if PlatformDetector.is_linux() or PlatformDetector.is_darwin():
            for item in self.target_dir.glob("**/*"):
                if item.is_file() and not item.name.endswith((".py", ".txt", ".md")):
                    item.chmod(0o755)
            yield status, "Setting executable permissions"

        # Clean up the archive
        archive_path.unlink()
        status = DependencyStatus.INSTALLED
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "InferX installation complete"

    @override
    def get_status(self) -> DependencyStatus:
        """Get the current status of InferX."""
        # Use DependencyRegistry to get the stored status
        status_str = DependencyRegistry.get_status(self._id)

        # If status is None, determine it and update
        if status_str is None:
            if not self.target_dir.exists():
                status = DependencyStatus.MISSING
            # Check if the directory has contents
            elif not any(self.target_dir.iterdir()):
                status = DependencyStatus.MISSING
            else:
                status = DependencyStatus.READY

            # Update the status in the registry
            DependencyRegistry.update_status(self._id, status.value)
            return status
        else:
            # Convert string status to enum
            return DependencyStatus(status_str)

    @override
    def install(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Install InferX."""
        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Starting InferX installation"

        try:
            yield from self._setup_inferx()
            status = DependencyStatus.INSTALLED
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "InferX installation complete"

            status = DependencyStatus.READY
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "InferX is ready to use"
        except Exception as e:
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to install InferX: {e}"

    @override
    def uninstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Uninstall InferX."""
        if not self.target_dir.exists():
            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "InferX is not installed"
            return

        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Removing InferX installation"

        try:
            import shutil

            shutil.rmtree(self.target_dir)
            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "InferX has been uninstalled"
        except Exception as e:
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to uninstall InferX: {e}"

    @override
    def reinstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Reinstall InferX by first uninstalling and then installing."""
        yield from self.uninstall()
        yield from self.install()

    @override
    def ensure(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Ensure InferX is installed and ready.
        First reports the current status, then handles installation if needed.
        Yields status updates during the process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """
        match self.get_status():
            case DependencyStatus.INSTALLED:
                yield DependencyStatus.INSTALLED, "InferX is already installed and ready"
                return
            case DependencyStatus.MISSING | DependencyStatus.ERROR:
                yield DependencyStatus.MISSING, "InferX is not installed"
                yield from self.install()
            case DependencyStatus.CORRUPTED:
                yield DependencyStatus.CORRUPTED, "InferX installation is corrupted"
                yield from self.reinstall()


# -----------------------------------------------------------------------------
