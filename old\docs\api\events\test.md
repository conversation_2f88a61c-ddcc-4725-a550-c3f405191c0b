# Test Event (`test.py`)

The `test.py` module provides a simple event handler for testing the WebSocket connection.

## Overview

This handler is used to verify that the client-server communication is working correctly. When the client sends a `test` event, the server processes it and sends a `test:response` event back.

## Event Handler

- `handle_test_event(sio, sid, data)`:
  - Receives any JSON data payload from the client.
  - Logs the received data for debugging purposes.
  - Pauses for 1 second to simulate work.
  - Emits a `test:response` event, echoing the original data back to the client along with a success message.
  - Includes basic error handling to emit a `test:error` event if something goes wrong.

## Event Flow

1. The client sends a `test` event.
2. The `handle_test_event` function logs the incoming data.
3. After a 1-second delay, it emits a `test:response` event back to the same client, containing the original data.
