[project]
name = "client-server"
version = "0.1.0"
description = ""
authors = [
    {name = "CodeMate Inc",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11,<3.14"
dependencies = [
    "beautifulsoup4==4.12.3",
    "certifi==2024.8.30",
    "fastapi==0.115.5",
    "jsonref>=1.1.0,<2.0.0",
    "langchain-text-splitters==0.3.8",
    "litellm==1.60.8",
    "loguru==0.7.2",
    "markdownify==0.14.1",
    "ollama==0.4.7",
    "onnxruntime>=1.20.1,<2.0.0",
    "overrides>=7.7.0,<8.0.0",
    "playwright==1.49.0",
    "psutil==6.1.1",
    "pydantic==2.10.2",
    "python-socketio==5.11.4",
    "PyYAML==6.0.2",
    "qdrant-client==1.12.1",
    "requests==2.32.3",
    "uvicorn==0.32.1",
    "pyinstaller>=6.12.0,<7.0.0",
    "poetry>=2.1.1,<3.0.0",
    "python-dotenv>=1.1.0,<2.0.0",
    "pillow>=11.2.1,<12.0.0",
    "tqdm>=4.67.1,<5.0.0",
]

[tool.poetry.dependencies]
python = ">=3.11,<3.14"

[tool.poetry.scripts]
server = "client_server.cmd.server:main"
setup = "client_server.cmd.setup:main"
build = "client_server.cmd.build:main"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.poetry.group.dev.dependencies]
mypy = ">=1.9.0,<2.0.0"

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
strict = true
