from BASE.utils.platform_detector import PlatformDetector
from ipc import IPC
from BASE.utils.path_selector import PathSelector
import numpy as np

ipc_ = IPC.connect()



# Identify the appropriate runtime based on the platform and CUDA availability
if PlatformDetector.is_snapdragon_arm():
    from BASE.ai_runtime.qnn import inference
if PlatformDetector.is_amd():
    if ipc_.get("cuda_available"):
        from BASE.ai_runtime.gpu_cuda import inference
    else:
        from BASE.ai_runtime.cpu import inference

from tokenizers import Tokenizer
TOKENIZER_PATH = PathSelector.get_base_path() / "models/embeddings/tokenizer.json"
MODEL_PATH = PathSelector.get_base_path() / "models/embeddings/model_int8.onnx"



MODEL = None
TOKENIZER = None


def load_model():
    global MODEL, TOKENIZER
    MODEL = inference(model_path=MODEL_PATH)
    TOKENIZER = Tokenizer.from_file(TOKENIZER_PATH)

def generate_local_embeddings(text):
    global MODEL, TOKENIZER
    if MODEL is None:
        load_model()
    
    
    encoded = TOKENIZER.encode(text)
    input_ids = np.array([encoded.ids], dtype=np.int64)
    attention_mask = np.array([[1] * len(encoded.ids)], dtype=np.int64)
    position_ids = np.array([list(range(len(encoded.ids)))], dtype=np.int64)

    inputs = {
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "position_ids": position_ids,
    }


    num_layers = 28
    batch = 1
    num_heads = 8  # might be different for your model, you can confirm via model graph
    head_dim = 128   # head_dim * num_heads = hidden size
    seq_len = 0     # past = empty


    for layer in range(num_layers):
        zero_key = np.zeros((batch, num_heads, seq_len, head_dim), dtype=np.float32)
        zero_value = np.zeros((batch, num_heads, seq_len, head_dim), dtype=np.float32)
        inputs[f"past_key_values.{layer}.key"] = zero_key
        inputs[f"past_key_values.{layer}.value"] = zero_value

    # ==== Run Inference ====
    outputs = MODEL.run(None, inputs)

    # ==== Extract Embedding ====
    embedding = outputs[0][0]
    sentence_embeddings = embedding.mean(axis=0).tolist()
    return sentence_embeddings