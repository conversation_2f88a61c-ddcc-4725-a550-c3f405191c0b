from typing import List, Dict, Any, Optional
from client_server.core.logger import LOGGER
from client_server.utils.platform_detector import PlatformDetector

from . import ITokenizationBackend


class TokenizationBuilder:
    """
    A builder class that selects and instantiates the appropriate tokenization backend
    based on available libraries and platform considerations.
    """

    _instance: Optional[ITokenizationBackend] = None

    @staticmethod
    def create() -> ITokenizationBackend:
        """
        Creates and returns the appropriate tokenization backend.

        Returns:
            ITokenizationBackend: The appropriate tokenization backend instance
        """
        if TokenizationBuilder._instance is not None:
            return TokenizationBuilder._instance

        if PlatformDetector.is_snapdragon_arm():
            from .tiktoken import TiktokenBackend

            TokenizationBuilder._instance = TiktokenBackend()
            return TokenizationBuilder._instance

        else:
            # Default to Ollama
            from .ollama import OllamaTokenizationBackend

            TokenizationBuilder._instance = OllamaTokenizationBackend()
            return TokenizationBuilder._instance

    @staticmethod
    def dispose():
        """
        Disposes of the current tokenization backend instance if it exists.
        """
        if TokenizationBuilder._instance is not None:
            TokenizationBuilder._instance = None


# Helper functions for common operations - these avoid having to directly interact with the backend


def count_tokens(tokenizer: ITokenizationBackend, text: str, model: str = None) -> int:
    """Count the number of tokens in a text string."""
    return tokenizer.count_tokens(text, model)


def truncate_text(
    tokenizer: ITokenizationBackend, text: str, max_tokens: int, model: str = None
) -> str:
    """Truncate text to a specified token limit."""
    return tokenizer.truncate_to_token_limit(text, max_tokens, model)


def ensure_messages_within_token_limit(
    tokenizer: ITokenizationBackend,
    messages: List[Dict[str, Any]],
    max_tokens: int = 70000,
    model: str = None,
) -> List[Dict[str, Any]]:
    """
    Ensure the total token count of a list of messages is within the specified limit.
    Truncates content starting from oldest messages.

    Args:
        messages: List of message dictionaries with 'role' and 'content' keys
        max_tokens: Maximum number of tokens to allow (default 70k)
        model: Optional model name to use for tokenization

    Returns:
        List of messages that fit within the token limit
    """
    if not messages:
        return []

    # First check if we're already within the limit
    all_text = " ".join(
        [m.get("content", "") for m in messages if isinstance(m.get("content"), str)]
    )
    total_tokens = tokenizer.count_tokens(all_text, model)
    LOGGER.info(f"Total tokens in all messages: {total_tokens}")

    if total_tokens <= max_tokens:
        return messages

    # We need to truncate
    result_messages = []
    tokens_to_keep = max_tokens

    # Process from newest to oldest to prioritize recent messages
    for message in reversed(messages):
        if not isinstance(message.get("content"), str):
            # Non-string content - add as is
            result_messages.insert(0, message.copy())
            continue

        message_tokens = tokenizer.count_tokens(message["content"], model)

        if tokens_to_keep >= message_tokens:
            # Keep this entire message
            result_messages.insert(0, message.copy())
            tokens_to_keep -= message_tokens
        else:
            # Need to truncate this message
            if tokens_to_keep > 0:
                truncated_msg = message.copy()
                truncated_msg["content"] = tokenizer.truncate_to_token_limit(
                    message["content"], tokens_to_keep, model
                )
                result_messages.insert(0, truncated_msg)
            tokens_to_keep = 0
            break

    LOGGER.info(f"Truncated messages from {len(messages)} to {len(result_messages)}")
    return result_messages
