import asyncio
import time
import traceback
from typing import Literal, Optional, Callable, Coroutine, Any

import socketio
from pydantic import BaseModel

from client_server.services.embeddings.utils import (
    EmbeddingInferenceBuilder,
    IEmbeddingBackend,
)
from client_server.core.logger import LOGGER
from client_server.utils.models.knowledgebase import (
    QdrantKnowledgeBase,
    QdrantKnowledgeBaseChunk,
    QdrantKnowledgebaseType,
    QdrantKnowledgeBaseMetadata,
    QdrantKnowledgebaseStatus,
    QdrantKnowledgebaseSyncConfig_t,
)

from client_server.utils.chunkers.codebase import CodebaseChunker
from client_server.utils.chunkers.github import GithubChunker
from client_server.utils.chunkers.docs import DocsChunker
from client_server.utils.chunkers.swagger import Swagger<PERSON>hunker
from client_server.utils.chunkers import IChunker


# -----------------------------------------------------------------------------
# Models
# -----------------------------------------------------------------------------


class UploadData(QdrantKnowledgeBase):
    request_id: str
    session: str


class UploadProgressData(BaseModel):
    request_id: str
    status: str
    progress: float | str
    message: Optional[str] = None


class UploadResponseData(BaseModel):
    request_id: str
    status: Literal["success", "error"]
    message: str
    data: Optional[dict] = None


class CloudUploadData(BaseModel):
    kb_id: str
    request_id: str
    session: str
    sync_config: Optional[QdrantKnowledgebaseSyncConfig_t] = None


class CloudUploadProgressData(BaseModel):
    request_id: str
    status: str
    progress: float | str
    message: Optional[str] = None


class CloudUploadResponseData(BaseModel):
    request_id: str
    status: Literal["success", "error"]
    message: str
    data: Optional[dict] = None


class CloudSyncData(BaseModel):
    kb_id: str
    request_id: str
    session: Optional[str] = None


class CloudSyncProgressData(BaseModel):
    request_id: str
    status: str
    progress: float | str
    message: Optional[str] = None


class CloudSyncResponseData(BaseModel):
    request_id: str
    status: Literal["success", "error"]
    message: str
    data: Optional[dict] = None


# -----------------------------------------------------------------------------
# Helper Functions
# -----------------------------------------------------------------------------


def _validate_kb_for_cloud_upload(kb_id: str) -> tuple[bool, str, Optional[QdrantKnowledgeBase]]:
    """
    Validate that a knowledge base can be uploaded to cloud.

    Returns:
        tuple: (is_valid, error_message, kb_object)
    """
    try:
        # Check if KB exists
        if not QdrantKnowledgeBase.exists_id(kb_id):
            return False, f"Knowledge base with ID {kb_id} not found", None

        # Get the KB object
        kb = QdrantKnowledgeBase.get(kb_id)

        # Check if KB is local (not remote)
        if kb.source.value.upper() == "REMOTE":
            return False, "Cannot upload remote knowledge bases - they are already in the cloud", None

        # Check if KB already has a cloud_id (already uploaded)
        if kb.cloud_id:
            return False, f"Knowledge base already has cloud ID {kb.cloud_id}. Use sync instead of upload.", None

        # All validations passed
        return True, "", kb

    except Exception as e:
        LOGGER.error(f"Error validating KB for cloud upload: {e}")
        return False, f"Error validating knowledge base: {str(e)}", None


def _validate_kb_for_cloud_sync(kb_id: str) -> tuple[bool, str, Optional[QdrantKnowledgeBase]]:
    """
    Validate that a knowledge base can be synced to cloud.

    Returns:
        tuple: (is_valid, error_message, kb_object)
    """
    try:
        # Check if KB exists
        if not QdrantKnowledgeBase.exists_id(kb_id):
            return False, f"Knowledge base with ID {kb_id} not found", None

        # Get the KB object
        kb = QdrantKnowledgeBase.get(kb_id)

        # Check if KB is local (not remote)
        if kb.source.value.upper() == "REMOTE":
            return False, "Cannot sync remote knowledge bases - they are already in the cloud", None

        # Check if KB has a cloud_id (required for sync)
        if not kb.cloud_id:
            return False, f"Knowledge base does not have a cloud ID. Upload to cloud first before syncing.", None

        # All validations passed
        return True, "", kb

    except Exception as e:
        LOGGER.error(f"Error validating KB for cloud sync: {e}")
        return False, f"Error validating knowledge base: {str(e)}", None


def _get_modified_chunks_for_sync(
    kb: QdrantKnowledgeBase,
    all_chunks: list[QdrantKnowledgeBaseChunk],
    buffer_time_ms: int = 5000  # 5 second buffer to avoid edge cases
) -> list[QdrantKnowledgeBaseChunk]:
    """
    Get chunks from files that have been modified since the last sync.

    Args:
        kb: The knowledge base object
        all_chunks: All chunks from the knowledge base
        buffer_time_ms: Buffer time in milliseconds to avoid edge cases

    Returns:
        List of chunks from modified files only
    """
    try:
        # Get last sync timestamp
        last_synced = kb.syncConfig.lastSynced
        if last_synced <= 0:
            # If never synced, return all chunks
            LOGGER.info("Knowledge base has never been synced, returning all chunks")
            return all_chunks

        # Get file timestamps from metadata
        if not kb.metadata or not hasattr(kb.metadata, 'file_timestamps'):
            LOGGER.warning("No file timestamps available, returning all chunks")
            return all_chunks

        file_timestamps = kb.metadata.file_timestamps
        if not file_timestamps:
            LOGGER.warning("File timestamps dictionary is empty, returning all chunks")
            return all_chunks

        # Find modified files (with buffer time)
        sync_threshold = last_synced - buffer_time_ms
        modified_files = set()

        for file_path, file_timestamp in file_timestamps.items():
            if file_timestamp > sync_threshold:
                modified_files.add(file_path)

        LOGGER.info(f"Found {len(modified_files)} modified files since last sync")
        LOGGER.debug(f"Modified files: {list(modified_files)[:5]}...")  # Log first 5 files

        # Filter chunks to only include those from modified files
        modified_chunks = []
        for chunk in all_chunks:
            chunk_file = chunk.metadata.file
            # Normalize file path for comparison
            from client_server.utils.file_timestamp_manager import normalize_path_format
            normalized_chunk_file = normalize_path_format(chunk_file)

            if normalized_chunk_file in modified_files:
                modified_chunks.append(chunk)

        LOGGER.info(f"Filtered to {len(modified_chunks)} chunks from modified files")
        return modified_chunks

    except Exception as e:
        LOGGER.error(f"Error filtering modified chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        # On error, return all chunks to be safe
        return all_chunks


async def _make_chunks(
    upload_data: UploadData,
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[QdrantKnowledgeBaseChunk]]:
    try:
        await progress_fn(1)

        chunker: IChunker | None = None
        match upload_data.type:
            case QdrantKnowledgebaseType.Codebase:
                chunker = CodebaseChunker(upload_data.metadata)
            case QdrantKnowledgebaseType.Github:
                chunker = GithubChunker(upload_data.metadata)
            case QdrantKnowledgebaseType.Docs:
                chunker = DocsChunker(upload_data.metadata)
            case QdrantKnowledgebaseType.Swagger:
                chunker = SwaggerChunker(upload_data.metadata)
            case _:
                raise ValueError(f"Unknown knowledge base type: {upload_data.type}")

        return await chunker.process(progress_fn)
    except Exception as e:
        LOGGER.error(f"Error chunking files: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await error_fn(str(e))


async def _fill_embeddings(
    chunks: list[QdrantKnowledgeBaseChunk],
    backend: IEmbeddingBackend,
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[QdrantKnowledgeBaseChunk]]:
    try:
        if len(chunks) == 0:
            raise ValueError("No chunks found")

        LOGGER.info("Starting embedding generation phase")
        LOGGER.info(f"Using backend: {backend}")

        await progress_fn(1)

        completed_chunks = 0

        async def _process_batch(batch: list[QdrantKnowledgeBaseChunk]):
            nonlocal completed_chunks
            contents = [chunk.metadata.content for chunk in batch]
            embeddings = await backend.generate_batch(contents)
            for chunk, embedding in zip(batch, embeddings):
                chunk.embeddings = embedding
                completed_chunks += 1
                await progress_fn(completed_chunks / len(chunks) * 100)

        embedding_phase_start = time.time()
        BATCH_SIZE = 10
        # use asyncio.gather to process batches in parallel
        await asyncio.gather(
            *[
                _process_batch(chunks[i : i + BATCH_SIZE])
                for i in range(0, len(chunks), BATCH_SIZE)
            ]
        )
        embedding_phase_time = time.time() - embedding_phase_start
        avg_embedding_time = embedding_phase_time / len(chunks)

        LOGGER.info(
            f"Embedding generation completed. Total chunks with embeddings: {len(chunks)} "
            f"in {embedding_phase_time:.2f}s (avg {avg_embedding_time:.3f}s per embedding)"
        )

        return chunks
    except Exception as e:
        LOGGER.error(f"Error filling embeddings: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await error_fn(f"Failed to fill embeddings: {e}")


async def handle_upload_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    try:
        upload_data = UploadData.model_validate(data)
    except Exception as e:
        LOGGER.error(f"Error validating upload data: {e}")
        LOGGER.error(f"Validation error traceback: {traceback.format_exc()}")
        await sio.emit(
            "upload:error",
            data=UploadResponseData(
                request_id=data["request_id"],
                status="error",
                message="Invalid upload data.",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 1. Migrate legacy knowledge bases and check for path-based duplicates ------------------
    # -----------------------------------------------------------------------------------------

    existence_check_start = time.time()

    # Run migration for legacy knowledge bases (this is idempotent)
    try:
        QdrantKnowledgeBase.migrate_legacy_knowledge_bases()
    except Exception as e:
        LOGGER.warning(f"Error during legacy KB migration: {e}")
        # Continue with upload process even if migration fails

    # Strict path-based duplicate detection for all knowledge base types
    if upload_data.metadata and hasattr(upload_data.metadata, 'path'):
        kb_path = upload_data.metadata.path
        existing_kb = QdrantKnowledgeBase.exists_by_path(kb_path)

        if existing_kb:
            existence_check_time = time.time() - existence_check_start

            # Strict duplicate prevention: reject ANY existing KB for the same path
            # This includes both auto-indexed and manually created knowledge bases
            kb_type_description = "auto-indexed" if existing_kb.isAutoIndexed else "manually created"
            LOGGER.warning(
                f"KB:ADD path {kb_path} already has a {kb_type_description} KB (ID: {existing_kb.id}) "
                f"(check took {existence_check_time:.3f}s)"
            )

            error_message = "A knowledge base already exists for this path. Please choose a different path or remove the existing knowledge base first."

            await sio.emit(
                "upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=error_message,
                ).model_dump(),
                to=sid,
            )
            return
    elif upload_data.type == QdrantKnowledgebaseType.Codebase:
        # For codebase type, path is required
        LOGGER.error("Codebase knowledge base upload requires path metadata")
        await sio.emit(
            "upload:error",
            data=UploadResponseData(
                request_id=upload_data.request_id,
                status="error",
                message="Codebase knowledge base requires path information.",
            ).model_dump(),
            to=sid,
        )
        return

    existence_check_time = time.time() - existence_check_start
    LOGGER.debug(
        f"Knowledge base existence check passed (took {existence_check_time:.3f}s)"
    )

    LOGGER.info(f"Starting knowledge base creation process for: {upload_data.name}")

    # Start to process files
    files = []
    LOGGER.info(f"Processing knowledge base type: {upload_data.type}")

    # -----------------------------------------------------------------------------------------
    # 2. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    embeddings_backend = EmbeddingInferenceBuilder.create()
    prepare_chunks_steps = [
        # 1. Make chunks
        lambda: _make_chunks(
            upload_data,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data=UploadProgressData(
                    request_id=upload_data.request_id,
                    status=QdrantKnowledgebaseStatus.PROGRESS,
                    progress=progress,
                    message="(1/3) Chunking files",
                ).model_dump(),
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=message,
                ).model_dump(),
            ),
        ),
        # 2. Fill embeddings
        lambda: _fill_embeddings(
            chunks,
            backend=embeddings_backend,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data=UploadProgressData(
                    request_id=upload_data.request_id,
                    status=QdrantKnowledgebaseStatus.PROGRESS,
                    progress=progress,
                    message="(2/3) Generating embeddings",
                ).model_dump(),
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=message,
                ).model_dump(),
            ),
        ),
    ]

    chunks = []
    for step in prepare_chunks_steps:
        chunks = await step()
        if chunks is None:
            LOGGER.error("No chunks returned from step")
            return

    # -----------------------------------------------------------------------------------------
    # 3. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    metadata_phase_start = time.time()
    LOGGER.info("Starting metadata processing phase")
    await sio.emit(
        to=sid,
        event="upload:progress",
        data=UploadProgressData(
            request_id=upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=0,
            message="(3/3) Creating knowledge base",
        ).model_dump(),
    )

    try:
        db_creation_start = time.time()
        LOGGER.info("Creating knowledge base entry in local database")
        kb_metadata_dict = upload_data.model_dump()
        del kb_metadata_dict["session"]
        del kb_metadata_dict["request_id"]
        LOGGER.debug(
            f"Knowledge base metadata prepared: {list(kb_metadata_dict.keys())}"
        )

        kb = QdrantKnowledgeBase.from_chunks(
            metadata=QdrantKnowledgeBaseMetadata(**kb_metadata_dict), chunks=chunks
        )
        db_creation_time = time.time() - db_creation_start
        metadata_phase_time = time.time() - metadata_phase_start

        LOGGER.info(
            f"Knowledge base created successfully with {len(chunks)} chunks "
            f"(DB creation: {db_creation_time:.2f}s, total metadata phase: {metadata_phase_time:.2f}s)"
        )
    except Exception as e:
        LOGGER.error(f"Error processing metadata: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            event="upload:error",
            data=UploadResponseData(
                request_id=upload_data.request_id,
                status="error",
                message="Failed to process metadata.",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 4. Upload to cloud if required ----------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    progress_fn = lambda progress: sio.emit(
        to=sid,
        event="upload:progress",
        data=UploadProgressData(
            request_id=upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=progress,
            message="Preparing data to upload",
        ).model_dump(),
    )
    if upload_data.syncConfig.enabled:
        cloud_sync_start = time.time()
        LOGGER.info("Cloud sync is enabled, starting upload to cloud")
        LOGGER.debug(f"Cloud sync config: {upload_data.syncConfig}")

        await progress_fn(0)
        try:
            LOGGER.debug(f"Syncing to cloud with session: {upload_data.session}")

            await kb.sync_to_cloud(upload_data.session, chunks, progress_fn=progress_fn)

            cloud_sync_time = time.time() - cloud_sync_start
            LOGGER.info(
                f"Cloud sync completed successfully in {cloud_sync_time:.2f} seconds"
            )
        except Exception as e:
            cloud_sync_time = time.time() - cloud_sync_start
            LOGGER.error(f"ERROR FROM sync_to_cloud after {cloud_sync_time:.2f}s: {e}")
            LOGGER.error(f"Cloud sync error traceback: {traceback.format_exc()}")
            await sio.emit(
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=f"Failed to upload data: {e}",
                ).model_dump(),
                to=sid,
            )
            return
    else:
        LOGGER.info("Cloud sync is disabled, skipping upload to cloud")

    # -----------------------------------------------------------------------------------------
    # 6. Report success to the client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Upload process completed successfully for knowledge base: {upload_data.name}"
    )
    LOGGER.info(f"Processed {len(files)} files into {len(chunks)} chunks")

    # Report success to the client
    await sio.emit(
        event="upload:success",
        to=sid,
        data=UploadResponseData(
            request_id=upload_data.request_id,
            status="success",
            message="Knowledgebase prepared",
            data=kb.model_dump(),
        ).model_dump(),
    )
    LOGGER.info(
        f"Success event emitted to client for request: {upload_data.request_id}"
    )


async def handle_upload_to_cloud_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    """Handle uploading an existing local knowledge base to the cloud."""
    try:
        cloud_upload_data = CloudUploadData.model_validate(data)
    except Exception as e:
        LOGGER.error(f"Error validating cloud upload data: {e}")
        LOGGER.error(f"Validation error traceback: {traceback.format_exc()}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=data.get("request_id", "unknown"),
                status="error",
                message="Invalid cloud upload data.",
            ).model_dump(),
            to=sid,
        )
        return

    LOGGER.info(f"Starting cloud upload process for KB: {cloud_upload_data.kb_id}")

    # -----------------------------------------------------------------------------------------
    # 1. Validate Knowledge Base for Cloud Upload --------------------------------------------
    # -----------------------------------------------------------------------------------------

    is_valid, error_message, kb = _validate_kb_for_cloud_upload(cloud_upload_data.kb_id)
    if not is_valid:
        LOGGER.error(f"KB validation failed: {error_message}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=cloud_upload_data.request_id,
                status="error",
                message=error_message,
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 2. Retrieve Existing Chunks from Knowledge Base ---------------------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "upload_to_cloud:progress",
        data=CloudUploadProgressData(
            request_id=cloud_upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=10,
            message="Retrieving knowledge base chunks",
        ).model_dump(),
        to=sid,
    )

    try:
        chunks_retrieval_start = time.time()
        LOGGER.info(f"Retrieving chunks for KB: {kb.name}")
        chunks = kb.get_all_chunks()
        chunks_retrieval_time = time.time() - chunks_retrieval_start

        LOGGER.info(
            f"Retrieved {len(chunks)} chunks from KB {kb.name} in {chunks_retrieval_time:.2f}s"
        )

        if not chunks:
            raise ValueError("No chunks found in knowledge base")

    except Exception as e:
        LOGGER.error(f"Error retrieving chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=cloud_upload_data.request_id,
                status="error",
                message=f"Failed to retrieve knowledge base chunks: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 3. Update Sync Configuration if Provided ----------------------------------------------
    # -----------------------------------------------------------------------------------------

    if cloud_upload_data.sync_config:
        kb.syncConfig = cloud_upload_data.sync_config
        LOGGER.info(f"Updated sync config for KB: {kb.syncConfig}")

    # Enable cloud sync for the upload
    kb.syncConfig.enabled = True

    # -----------------------------------------------------------------------------------------
    # 4. Upload to Cloud ---------------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    cloud_sync_start = time.time()
    LOGGER.info("Starting upload to cloud")

    progress_fn = lambda progress: sio.emit(
        "upload_to_cloud:progress",
        data=CloudUploadProgressData(
            request_id=cloud_upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=progress,
            message="Uploading to cloud",
        ).model_dump(),
        to=sid,
    )

    try:
        LOGGER.debug(f"Syncing to cloud with session: {cloud_upload_data.session}")

        await kb.sync_to_cloud(cloud_upload_data.session, chunks, progress_fn=progress_fn)

        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.info(
            f"Cloud upload completed successfully in {cloud_sync_time:.2f} seconds"
        )
    except Exception as e:
        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.error(f"ERROR FROM sync_to_cloud after {cloud_sync_time:.2f}s: {e}")
        LOGGER.error(f"Cloud sync error traceback: {traceback.format_exc()}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=cloud_upload_data.request_id,
                status="error",
                message=f"Failed to upload to cloud: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 5. Report Success to the Client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Cloud upload process completed successfully for knowledge base: {kb.name}"
    )
    LOGGER.info(f"Uploaded {len(chunks)} chunks to cloud")

    # Report success to the client
    await sio.emit(
        "upload_to_cloud:success",
        data=CloudUploadResponseData(
            request_id=cloud_upload_data.request_id,
            status="success",
            message="Knowledge base uploaded to cloud successfully",
            data=kb.model_dump(),
        ).model_dump(),
        to=sid,
    )
    LOGGER.info(
        f"Success event emitted to client for request: {cloud_upload_data.request_id}"
    )


async def handle_sync_to_cloud_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    """Handle syncing an existing cloud knowledge base with incremental updates."""
    try:
        cloud_sync_data = CloudSyncData.model_validate(data)
    except Exception as e:
        LOGGER.error(f"Error validating cloud sync data: {e}")
        LOGGER.error(f"Validation error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=data.get("request_id", "unknown"),
                status="error",
                message="Invalid cloud sync data.",
            ).model_dump(),
            to=sid,
        )
        return

    LOGGER.info(f"Starting cloud sync process for KB: {cloud_sync_data.kb_id}")

    # -----------------------------------------------------------------------------------------
    # 1. Validate Knowledge Base for Cloud Sync ----------------------------------------------
    # -----------------------------------------------------------------------------------------

    is_valid, error_message, kb = _validate_kb_for_cloud_sync(cloud_sync_data.kb_id)
    if not is_valid:
        LOGGER.error(f"KB validation failed: {error_message}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=error_message,
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 2. Retrieve All Chunks from Knowledge Base --------------------------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=10,
            message="Retrieving knowledge base chunks",
        ).model_dump(),
        to=sid,
    )

    try:
        chunks_retrieval_start = time.time()
        LOGGER.info(f"Retrieving all chunks for KB: {kb.name}")
        all_chunks = kb.get_all_chunks()
        chunks_retrieval_time = time.time() - chunks_retrieval_start

        LOGGER.info(
            f"Retrieved {len(all_chunks)} total chunks from KB {kb.name} in {chunks_retrieval_time:.2f}s"
        )

        if not all_chunks:
            raise ValueError("No chunks found in knowledge base")

    except Exception as e:
        LOGGER.error(f"Error retrieving chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to retrieve knowledge base chunks: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 3. Filter Chunks Based on File Timestamps (Incremental Sync) -------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=30,
            message="Analyzing modified files for incremental sync",
        ).model_dump(),
        to=sid,
    )

    try:
        filter_start = time.time()
        modified_chunks = _get_modified_chunks_for_sync(kb, all_chunks)
        filter_time = time.time() - filter_start

        LOGGER.info(
            f"Filtered to {len(modified_chunks)} modified chunks out of {len(all_chunks)} total chunks in {filter_time:.2f}s"
        )

        if not modified_chunks:
            # No modified chunks, sync is already up to date
            LOGGER.info("No modified chunks found - knowledge base is already up to date")
            await sio.emit(
                "sync_to_cloud:success",
                data=CloudSyncResponseData(
                    request_id=cloud_sync_data.request_id,
                    status="success",
                    message="Knowledge base is already up to date - no sync needed",
                    data={
                        "total_chunks": len(all_chunks),
                        "modified_chunks": 0,
                        "sync_skipped": True
                    },
                ).model_dump(),
                to=sid,
            )
            return

    except Exception as e:
        LOGGER.error(f"Error filtering modified chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to analyze modified files: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 4. Prepare Update Payload for Cloud API -----------------------------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=50,
            message="Preparing incremental update payload",
        ).model_dump(),
        to=sid,
    )

    try:
        # Prepare vectors, metadata, and IDs for modified chunks only
        vectors = []
        metadata = []
        ids = []

        for chunk in modified_chunks:
            vectors.append(chunk.embeddings)
            metadata.append({
                "id": chunk.metadata.id,
                "content": chunk.metadata.content,
                "file": chunk.metadata.file,
                "name": chunk.metadata.name,
                "additional_metadata": chunk.metadata.additional_metadata,
            })
            ids.append(chunk.metadata.id)

        # Create update payload with exact schema
        current_timestamp = int(time.time())
        update_payload = {
            "collection_id": kb.cloud_id,  # Use the KB's cloud_id
            "update_queries": {
                "vectors": vectors,
                "metadata": metadata,
                "ids": ids
            },
            "timestamp": current_timestamp
        }

        LOGGER.info(f"Prepared update payload with {len(modified_chunks)} modified chunks")
        LOGGER.debug(f"Update payload structure: collection_id={kb.cloud_id}, chunks={len(modified_chunks)}, timestamp={current_timestamp}")

    except Exception as e:
        LOGGER.error(f"Error preparing update payload: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to prepare update payload: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 5. Send Update Request to Cloud API ---------------------------------------------------
    # -----------------------------------------------------------------------------------------

    cloud_sync_start = time.time()
    LOGGER.info("Starting incremental sync to cloud")

    progress_fn = lambda progress: sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=progress,
            message="Syncing modified chunks to cloud",
        ).model_dump(),
        to=sid,
    )

    try:
        await progress_fn(70)

        # Get session for authentication
        session = cloud_sync_data.session
        if not session:
            raise ValueError("Session is required for cloud sync authentication")

        LOGGER.debug(f"Syncing to cloud with session: {session}")

        # Import required modules for HTTP request
        import httpx
        from client_server.core.state import G_BASE_URL
        from client_server.core.constants import SSL_CERT_FILE

        base_url = G_BASE_URL.get().cloud
        LOGGER.info(f"Sending incremental sync request to: {base_url}/knowledge/update")

        async with httpx.AsyncClient(
            verify=SSL_CERT_FILE,
            timeout=300.0,  # 5 minute timeout
        ) as client:
            response = await client.post(
                f"{base_url}/knowledge/update",
                json=update_payload,
                headers={
                    "x-session": session,
                    "Content-Type": "application/json",
                },
            )

        await progress_fn(90)

        LOGGER.info(f"Cloud sync response: {response.status_code}")

        if response.status_code != 200:
            response_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {"error": response.text}
            error_message = response_data.get("error", f"HTTP {response.status_code}")
            raise RuntimeError(f"Cloud sync failed: {error_message}")

        response_data = response.json()
        LOGGER.info(f"Cloud sync successful: {response_data}")

        # Update lastSynced timestamp after successful sync
        kb.syncConfig.lastSynced = current_timestamp * 1000  # Convert to milliseconds
        kb.save()

        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.info(
            f"Incremental cloud sync completed successfully in {cloud_sync_time:.2f} seconds"
        )

    except Exception as e:
        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.error(f"ERROR FROM incremental sync_to_cloud after {cloud_sync_time:.2f}s: {e}")
        LOGGER.error(f"Cloud sync error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to sync to cloud: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 6. Report Success to the Client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Incremental cloud sync process completed successfully for knowledge base: {kb.name}"
    )
    LOGGER.info(f"Synced {len(modified_chunks)} modified chunks out of {len(all_chunks)} total chunks")

    # Report success to the client
    await sio.emit(
        "sync_to_cloud:success",
        data=CloudSyncResponseData(
            request_id=cloud_sync_data.request_id,
            status="success",
            message="Knowledge base synced to cloud successfully",
            data={
                "total_chunks": len(all_chunks),
                "modified_chunks": len(modified_chunks),
                "sync_time_seconds": cloud_sync_time,
                "last_synced": kb.syncConfig.lastSynced,
                "kb_data": kb.model_dump()
            },
        ).model_dump(),
        to=sid,
    )
    LOGGER.info(
        f"Success event emitted to client for request: {cloud_sync_data.request_id}"
    )
