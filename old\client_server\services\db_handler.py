import json
import os  # Add this import at the top
import sqlite3
from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector


class Database:
    def __init__(self):
        # Create directory path if it doesn't exist (except for in-memory database)
        self.path = PathSelector.get_qdrant_db_path() / "records.db"
        self.conn = sqlite3.connect(self.path)
        self.conn.row_factory = sqlite3.Row
        self._enable_json1()

    def _enable_json1(self):
        """Enable JSON1 extension if available."""
        try:
            # For some SQLite builds (e.g., default on Windows), JSON1 is not enabled by default
            self.conn.execute("SELECT json('null')")
        except sqlite3.OperationalError:
            self.conn.enable_load_extension(True)
            try:
                self.conn.execute("SELECT load_extension('mod_spatialite')")
            except sqlite3.OperationalError:
                pass  # Can't load JSON1 extension

    def __getitem__(self, name):
        """Get a collection by name."""
        return Collection(self.conn, name)

    def close(self):
        """Close the database connection."""
        self.conn.close()


class Collection:
    def __init__(self, conn, name):
        self.conn = conn
        self.name = name
        self.create_table()

    def create_table(self):
        """Create a table for the collection if it doesn't exist."""
        self.conn.execute(
            f"""  
            CREATE TABLE IF NOT EXISTS "{self.name}" (  
                _id INTEGER PRIMARY KEY AUTOINCREMENT,  
                document TEXT NOT NULL  
            )  
        """
        )
        self.conn.commit()

    # Insertion Methods

    def insert_one(self, document):
        """Insert a single document into the collection."""
        return self._insert(document)

    def insert_many(self, documents):
        """Insert multiple documents into the collection."""
        ids = []
        for document in documents:
            result = self._insert(document)
            ids.append(result["_id"])
        return {"inserted_ids": ids}

    def _insert(self, document):
        """Helper method to insert a document."""
        document_json = json.dumps(document)
        cur = self.conn.cursor()
        cur.execute(
            f'INSERT INTO "{self.name}" (document) VALUES (?)', (document_json,)
        )
        self.conn.commit()
        document_id = cur.lastrowid
        return {"_id": document_id}

    # Find Methods

    def find(self, query=None, projection=None, sort=None, limit=None, skip=None):
        """Find documents matching the query."""
        cur = self.conn.cursor()
        sql, params = self._build_find_sql(query, projection, sort, limit, skip)
        cur.execute(sql, params)
        for row in cur:
            document = json.loads(row["document"])
            document["_id"] = row["_id"]
            if projection:
                document = self._apply_projection(document, projection)
            yield document

    def find_one(self, query=None, projection=None):
        """Find a single document matching the query."""
        results = list(self.find(query, projection, limit=1))
        return results[0] if results else None

    def count_documents(self, query=None):
        """Count documents matching the query."""
        cur = self.conn.cursor()
        sql, params = self._build_find_sql(query, count=True)
        cur.execute(sql, params)
        count = cur.fetchone()[0]
        return count

    # Helper Methods

    def _build_find_sql(
        self, query=None, projection=None, sort=None, limit=None, skip=None, count=False
    ):
        """Build SQL query for find operations."""
        select_clause = "COUNT(*)" if count else "_id, document"
        sql = f'SELECT {select_clause} FROM "{self.name}"'
        where_clause, params = self._parse_query(query)
        if where_clause:
            sql += f" WHERE {where_clause}"
        if not count and sort:
            sort_clause = ", ".join(
                [
                    f"json_extract(document, '$.{field}') {'ASC' if direction > 0 else 'DESC'}"
                    for field, direction in sort
                ]
            )
            sql += f" ORDER BY {sort_clause}"
        if not count and limit is not None:
            sql += f" LIMIT {limit}"
        if not count and skip is not None:
            sql += f" OFFSET {skip}"
        return sql, params

    def _parse_query(self, query):
        """Parse the query dictionary into SQL WHERE clause."""
        if not query:
            return "", []

        where_clauses = []
        params = []
        for key, value in query.items():
            clause, param = self._parse_query_condition(key, value)
            where_clauses.append(clause)
            params.extend(param)
        where_stmt = " AND ".join(where_clauses)
        return where_stmt, params

    def _parse_query_condition(self, key, value):
        """Parse individual query conditions."""
        if isinstance(value, dict):
            clauses = []
            params = []
            for op, val in value.items():
                clause, param = self._parse_operator(key, op, val)
                clauses.append(clause)
                params.extend(param)
            where_clause = " AND ".join(clauses)
            return f"({where_clause})", params
        else:
            clause = f"json_extract(document, '$.{key}') = ?"
            return clause, [value]

    def _parse_operator(self, key, operator, value):
        """Parse MongoDB query operators into SQL conditions."""
        field = f"json_extract(document, '$.{key}')"
        if operator == "$gt":
            clause = f"{field} > ?"
            return clause, [value]
        elif operator == "$gte":
            clause = f"{field} >= ?"
            return clause, [value]
        elif operator == "$lt":
            clause = f"{field} < ?"
            return clause, [value]
        elif operator == "$lte":
            clause = f"{field} <= ?"
            return clause, [value]
        elif operator == "$ne":
            clause = f"{field} != ?"
            return clause, [value]
        elif operator == "$in":
            placeholders = ",".join("?" for _ in value)
            clause = f"{field} IN ({placeholders})"
            return clause, value
        elif operator == "$nin":
            placeholders = ",".join("?" for _ in value)
            clause = f"{field} NOT IN ({placeholders})"
            return clause, value
        elif operator == "$exists":
            if value:
                clause = f"{field} IS NOT NULL"
            else:
                clause = f"{field} IS NULL"
            return clause, []
        elif operator == "$regex":
            clause = f"{field} REGEXP ?"
            return clause, [value]
        else:
            raise ValueError(f"Unsupported operator: {operator}")

    def _apply_projection(self, document, projection):
        """Apply field projection to the document."""
        if not projection:
            return document
        include_fields = {key for key, value in projection.items() if value}
        if include_fields:
            return {
                key: value for key, value in document.items() if key in include_fields
            }
        else:
            exclude_fields = {key for key, value in projection.items() if not value}
            return {
                key: value
                for key, value in document.items()
                if key not in exclude_fields
            }

    # Deletion Methods

    def delete_one(self, query):
        """Delete a single document matching the query."""
        return self._delete(query, multiple=False)

    def delete_many(self, query):
        """Delete multiple documents matching the query."""
        return self._delete(query, multiple=True)

    def _delete(self, query, multiple):
        """Helper method to delete documents."""
        where_clause, params = self._parse_query(query)
        sql = f'DELETE FROM "{self.name}"\n'
        if where_clause:
            sql += f" WHERE rowid=(SELECT rowid FROM {self.name} WHERE {where_clause}"
        if not multiple:
            sql += " LIMIT 1"
        sql += ");"
        cur = self.conn.cursor()
        cur.execute(sql, params)
        deleted_count = cur.rowcount
        self.conn.commit()
        return {"acknowledged": True, "deleted_count": deleted_count}

    # Update Methods

    def update_one(self, query, update, upsert=False):
        """Update a single document matching the query."""
        return self._update(query, update, multiple=False, upsert=upsert)

    def update_many(self, query, update, upsert=False):
        """Update multiple documents matching the query."""
        return self._update(query, update, multiple=True, upsert=upsert)

    def _update(self, query, update, multiple, upsert):
        """Helper method to update documents."""
        documents = list(self.find(query))
        if not documents and upsert:
            # Perform an insert if no documents match and upsert is True
            new_document = query.copy()
            for op, fields in update.items():
                if op == "$set":
                    new_document.update(fields)
                # Handle other operators if necessary
            self.insert_one(new_document)
            return {
                "acknowledged": True,
                "matched_count": 0,
                "modified_count": 0,
                "upserted_id": new_document.get("_id"),
            }
        modified_count = 0
        for doc in documents:
            updated_doc = self.apply_update_operators(doc, update)
            _id = doc["_id"]
            updated_doc.pop("_id", None)
            document_json = json.dumps(updated_doc)
            self.conn.execute(
                f'UPDATE "{self.name}" SET document = ? WHERE _id = ?',
                (document_json, _id),
            )
            modified_count += 1
            if not multiple:
                break
        self.conn.commit()
        return {
            "acknowledged": True,
            "matched_count": len(documents),
            "modified_count": modified_count,
        }

    def apply_update_operators(self, document, update):
        """Apply update operators (e.g., $set, $inc) to the document."""
        for op, fields in update.items():
            if op == "$set":
                for key, value in fields.items():
                    self._set_value(document, key, value)
            elif op == "$inc":
                for key, value in fields.items():
                    current_value = self._get_value(document, key)
                    if current_value is None:
                        current_value = 0
                    self._set_value(document, key, current_value + value)
            elif op == "$unset":
                for key in fields.keys():
                    self._unset_value(document, key)
            else:
                raise ValueError(f"Unsupported update operator: {op}")
        return document

    def _get_value(self, document, key):
        """Get nested value from document using dot notation."""
        keys = key.split(".")
        value = document
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        return value

    def _set_value(self, document, key, new_value):
        """Set nested value in document using dot notation."""
        keys = key.split(".")
        d = document
        for k in keys[:-1]:
            d = d.setdefault(k, {})
        d[keys[-1]] = new_value

    def _unset_value(self, document, key):
        """Unset nested value in document using dot notation."""
        keys = key.split(".")
        d = document
        for k in keys[:-1]:
            d = d.get(k, {})
        if isinstance(d, dict):
            d.pop(keys[-1], None)

    # Additional Methods

    def drop(self):
        """Drop the collection."""
        self.conn.execute(f'DROP TABLE IF EXISTS "{self.name}"')
        self.conn.commit()

    def rename(self, new_name):
        """Rename the collection."""
        self.conn.execute(f'ALTER TABLE "{self.name}" RENAME TO "{new_name}"')
        self.conn.commit()
        self.name = new_name
