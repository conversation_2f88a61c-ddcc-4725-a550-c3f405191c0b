# Logger

The logging system is built on the `loguru` library.

## Configuration

The logger is configured in `client_server/core/logger/__init__.py` with the following key features:

- **Dual-Channel Output**: Logs are written to both a file and the console, with different log levels for each to reduce noise in the development environment.
- **File Logging**:
  - **Path**: Logs are stored in a `logs.log` file within a directory managed by `PathSelector`.
  - **Rotation**: Log files are rotated when they reach 10 MB to prevent them from growing too large.
  - **Retention**: Old log files are kept for 10 days before being deleted.
  - **Level**: The file logger is set to the `INFO` level, capturing informational messages, warnings, and errors.
- **Cloud Reporting**:
  - **Trigger**: On `ERROR` level events, the logger attempts to send the error details to a cloud reporting service.
  - **Mechanism**: This is achieved by making a `POST` request to a local endpoint (`/report_to_cloud`), which then forwards the report. This is designed to fail silently to prevent logging loops.

## Utility Functions

The `client_server/core/logger/utils.py` module provides a suite of helper functions to standardize the logging of common metrics and events across the application:

- `log_memory_usage(stage: str)`: Records the application's current memory footprint at a given stage of execution.
- `log_file_stats(file_path: str)`: Logs the size and path of a file.
- `log_execution_time(func_name: str, start_time: float)`: Calculates and logs the execution time of a function.
- `log_api_call_stats(...)`: Records detailed statistics about an API call, including URL, method, status code, response time, and size.
- `log_system_info()`: Logs essential system information like CPU count, total RAM, and available disk space.
- `log_operation_stats(...)`: A comprehensive logger for tracking the performance of specific operations, including duration, success/failure status, item count, and data throughput.
