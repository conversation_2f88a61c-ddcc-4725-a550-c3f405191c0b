# Chat Event (`chat.py`)

The `chat.py` module contains the core logic for handling chat conversations within the application. It manages chat messages, processes different types of content and actions, and streams responses back to the client.

## Overview

The chat event handler is responsible for:

- Receiving chat payloads from the client.
- Processing context attached to messages (files, folders, terminal output, etc.).
- Handling different chat modes (`NORMAL`, `ECO`, `PRO`).
- Interacting with backend services for language model inference.
- Managing "actions" or "tools" that can be triggered during a conversation, such as searching for context, browsing the web, or querying a knowledge base.
- Streaming chat responses, actions, and other events back to the client in real-time.

## Data Models

The module defines several Pydantic models to structure the chat data:

- `ChatMessageContext`: Represents a single piece of context attached to a message (e.g., a file, a folder, a warning).
- `ChatMessage`: Represents a single message in the conversation, including its role, content, and associated context.
- `ChatPayload`: The main payload for a chat request, containing the list of messages, chat mode, provider information, and other metadata.
- `ChatStreamChunk`: A generic model for a chunk of data in the response stream.
- `ChatStreamContentChunk`: A chunk containing part of the assistant's text response.
- `ChatStreamActionChunk`: A chunk indicating that the assistant wants to perform an action.

## Main Event Handler

- `handle_chat_event(sio, sid, data)`: This is the primary entry point for all chat-related WebSocket events. It validates the incoming payload, determines the chat mode, and routes the request to the appropriate processor.

## Chat Modes

The chat handler supports multiple modes:

- **NORMAL & PRO**: In these modes, the chat request is processed by the backend server (`process_chat_request_from_server`). This typically involves more powerful language models and a wider range of capabilities.
- **ECO**: In this mode, the chat request is handled locally on the client's machine (`process_local_chat_request`). This uses a local language model for inference, which can be faster for simpler tasks and works offline. Before processing, it ensures that all necessary dependencies (like Ollama) are installed.

## Chat Processing Functions

- `process_chat_request_from_server(sio, sid, payload)`:

  - Pre-processes the message contexts, reading file contents and performing searches for context like Swagger API specs.
  - Sends the final payload to the backend chat service.
  - Receives a stream of events from the backend, which can be either content chunks or action requests.
  - Forwards content chunks directly to the client.
  - Handles action requests by calling the appropriate action handler (e.g., `_handle_action`).

- `process_local_chat_request(sio, sid, payload)`:
  - Pre-processes message contexts similarly to the server-based approach but tailored for local execution.
  - Performs local searches in knowledge bases (`_perform_folder_search`, `_perform_local_search`).
  - Constructs a sequence of messages and ensures it fits within the token limit of the local model.
  - Uses a local inference backend (`InferenceBuilder.create()`) to generate a response.
  - Streams the response back to the client chunk by chunk.

## Action Handling

- `_handle_action(action, ...)`: This function acts as a router for different types of actions requested by the language model. Based on the `action.name`, it calls the appropriate function to perform the action:
  - `context_search`: Searches within the provided context.
  - `folder_search`: Searches within a specific folder.
  - `web_search`: Performs a web search.
  - `swagger_search`: Searches within a Swagger/OpenAPI specification.

The results of these actions are then sent back to the language model to continue the conversation.

## Event Flow

The diagram below illustrates the sequence of events for both server-based and local chat processing.

<details>
<summary>View Chat Event Flow</summary>

```mermaid
sequenceDiagram
    participant E as User's Editor
    participant S as Our Server
    participant AI as Cloud AI

    E->>S: User sends a message
    S->>S: Process incoming message
    alt Cloud Mode (Normal/Pro)
        S->>S: Prepare user's code for AI
        S->>AI: Send message to Cloud AI
        AI-->>S: Stream back a response
        loop For each part of the response
            alt If it's a text response
                S->>E: Show text to user
            else If the AI wants to use a tool
                S->>E: Notify user "Searching..."
                S->>S: Perform the search (e.g., look in a folder)
                S->>E: Show search results to user
                S->>S: Give search results back to the AI
            end
        end
        AI-->>S: (response finished)
        S->>AI: Ask for follow-up questions
        AI-->>S: Get suggestions
        S->>E: Show suggestions to user
        S->>E: Tell user "All done!"
    else Local Mode (Eco)
        S->>S: Search through local files
        S->>S: Generate response using local AI
        loop For each word in response
            S->>E: Show text to user
        end
        S->>E: Tell user "All done!"
    end
```

</details>
