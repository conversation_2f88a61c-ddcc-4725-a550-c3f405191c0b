#!/usr/bin/env python3
"""
Standalone Chunking Analysis Tool

This script provides detailed chunking analysis for a single knowledge base.
Takes a KB ID as input and provides comprehensive information about how that
specific knowledge base is chunked, including chunk IDs, content previews, and metadata.

NEW FEATURE: Cross-KB File Path Search Test Function
====================================================
This script now includes a cross-KB search test for the get_chunks_by_file_path method:

Usage:
    python chunking_analysis_test.py --test-file-path

The test function demonstrates:
1. How to search for a file path across ALL knowledge bases automatically
2. Interactive prompt for file path input only (no KB ID needed!)
3. Automatic discovery and searching of all available knowledge bases
4. Comprehensive display of chunks from any KB containing the file
5. Detailed chunk information including IDs, content previews, and metadata
6. Handling of cases where multiple KBs contain the same file path

Example workflow:
1. Run: python chunking_analysis_test.py --test-file-path
2. Enter a file path to search for (e.g., "src/main.py")
3. View results from ALL knowledge bases that contain the file
4. See detailed information about chunks from each KB
"""

import argparse
import time
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import sys

# Import knowledge base models for detailed analysis
try:
    from client_server.utils.models.knowledgebase import QdrantKnowledgeBase, QdrantKnowledgeBaseChunk
    DETAILED_ANALYSIS_AVAILABLE = True
except ImportError:
    DETAILED_ANALYSIS_AVAILABLE = False
    print("⚠️  Detailed chunk analysis not available - missing knowledge base models")


def get_kb_input() -> Tuple[str, str]:
    """Get knowledge base ID and optional name from user input."""
    print("\n" + "=" * 60)
    print("📋 KNOWLEDGE BASE SELECTION")
    print("=" * 60)

    while True:
        kb_id = input("Enter Knowledge Base ID: ").strip()
        if not kb_id:
            print("❌ Knowledge Base ID cannot be empty. Please try again.")
            continue

        # Validate KB ID exists and is accessible
        try:
            kb = QdrantKnowledgeBase.get(kb_id)
            print(f"✅ Knowledge Base found: {kb.name}")

            # Ask for optional display name
            kb_name = input(f"Enter display name (press Enter to use '{kb.name}'): ").strip()
            if not kb_name:
                kb_name = kb.name

            return kb_id, kb_name

        except Exception as e:
            print(f"❌ Error accessing Knowledge Base '{kb_id}': {e}")
            print("Please check the ID and try again.")
            continue


def validate_kb_access(kb_id: str) -> Optional[QdrantKnowledgeBase]:
    """Validate that the KB ID exists and is accessible."""
    if not DETAILED_ANALYSIS_AVAILABLE:
        print("❌ Cannot validate KB access - missing knowledge base models")
        return None

    try:
        kb = QdrantKnowledgeBase.get(kb_id)
        return kb
    except Exception as e:
        print(f"❌ Error accessing Knowledge Base '{kb_id}': {e}")
        return None


def analyze_single_kb_comprehensive(kb: QdrantKnowledgeBase, kb_name: str, chunk_size: int = None,
                                   overlap: int = None, chunking_method: str = None,
                                   max_chunks_per_file: int = 5) -> Dict[str, Any]:
    """Perform comprehensive analysis of a single knowledge base."""
    if not DETAILED_ANALYSIS_AVAILABLE:
        return {"error": "Detailed analysis not available"}

    try:
        print(f"\n🔍 COMPREHENSIVE ANALYSIS: {kb_name}")
        print(f"📋 Knowledge Base ID: {kb.id}")
        if chunk_size:
            print(f"📏 Target Chunk Size: {chunk_size} characters")
        if overlap:
            print(f"🔄 Target Overlap: {overlap} characters")
        if chunking_method:
            print(f"🔧 Chunking Method: {chunking_method}")
        print("=" * 80)

        # Get all chunks from the knowledge base
        start_time = time.time()
        all_chunks = kb.get_all_chunks()
        load_time = time.time() - start_time

        print(f"📊 Loaded {len(all_chunks)} chunks in {load_time:.3f}s")

        if not all_chunks:
            print("⚠️  No chunks found in knowledge base")
            return {"files": {}, "summary": {"total_files": 0, "total_chunks": 0}}

        # Group chunks by file
        files_data = defaultdict(list)
        for chunk in all_chunks:
            if chunk.metadata.file:
                files_data[chunk.metadata.file].append(chunk)

        print(f"📁 Found {len(files_data)} files with chunks")
        print()

        # Analyze each file in detail
        for file_path, chunks in files_data.items():
            analyze_file_chunks_detailed(file_path, chunks, chunk_size, overlap, max_chunks_per_file)

        # Overall statistics
        print_kb_statistics(all_chunks, files_data, chunk_size, overlap, chunking_method)

        return {
            "files": dict(files_data),
            "summary": {
                "total_files": len(files_data),
                "total_chunks": len(all_chunks),
                "load_time": load_time
            }
        }

    except Exception as e:
        print(f"❌ Error in comprehensive analysis: {e}")
        return {"error": str(e)}


def analyze_file_chunks_detailed(file_path: str, chunks: List[QdrantKnowledgeBaseChunk],
                                chunk_size: int = None, overlap: int = None,
                                max_chunks: int = 5) -> None:
    """Analyze chunks for a specific file with detailed output."""
    print(f"📁 FILE: {file_path}")
    print(f"   📊 Total Chunks: {len(chunks)}")

    # Sort chunks by line number if available
    sorted_chunks = sorted(chunks, key=lambda c:
        c.metadata.additional_metadata.get("line_start", 0)
        if c.metadata.additional_metadata else 0)

    # File-level statistics
    total_content = sum(len(chunk.metadata.content) for chunk in chunks)
    avg_chunk_size = total_content / len(chunks) if chunks else 0
    chunk_sizes = [len(chunk.metadata.content) for chunk in chunks]

    print(f"   📏 Total Content: {total_content:,} characters")
    print(f"   📊 Chunk Sizes: min={min(chunk_sizes)}, max={max(chunk_sizes)}, avg={avg_chunk_size:.0f}")

    if chunk_size:
        size_deviation = abs(avg_chunk_size - chunk_size)
        deviation_percent = (size_deviation / chunk_size) * 100
        status = "✅" if deviation_percent <= 20 else "⚠️"
        print(f"   🎯 Size Compliance: {deviation_percent:.1f}% deviation from target {status}")

    # Line coverage analysis
    line_ranges = []
    for chunk in sorted_chunks:
        if chunk.metadata.additional_metadata:
            line_start = chunk.metadata.additional_metadata.get("line_start")
            line_end = chunk.metadata.additional_metadata.get("line_end")
            if line_start and line_end:
                line_ranges.append((line_start, line_end))

    if line_ranges:
        first_line = line_ranges[0][0]
        last_line = line_ranges[-1][1]
        total_lines = last_line - first_line + 1
        print(f"   📏 Line Coverage: {first_line}-{last_line} ({total_lines} lines)")

        # Check for gaps and overlaps
        gaps, overlaps = analyze_line_coverage(line_ranges)
        if gaps:
            print(f"   ⚠️  Line Gaps: {len(gaps)} gaps found")
        if overlaps:
            avg_overlap = sum(overlaps) / len(overlaps)
            print(f"   🔄 Line Overlaps: {len(overlaps)} overlaps, avg {avg_overlap:.1f} lines")
            if overlap:
                overlap_deviation = abs(avg_overlap - overlap)
                overlap_status = "✅" if overlap_deviation <= 2 else "⚠️"
                print(f"   🎯 Overlap Compliance: {overlap_deviation:.1f} lines deviation {overlap_status}")

    print()

    # Display individual chunks
    print(f"   🧩 INDIVIDUAL CHUNKS (showing {min(len(chunks), max_chunks)} of {len(chunks)}):")
    for i, chunk in enumerate(sorted_chunks[:max_chunks]):
        print_chunk_details(chunk, i + 1, chunk_size)

    if len(chunks) > max_chunks:
        print(f"   ... and {len(chunks) - max_chunks} more chunks")

    print("-" * 80)


def analyze_line_coverage(line_ranges: List[Tuple[int, int]]) -> Tuple[List[Tuple[int, int]], List[int]]:
    """Analyze line coverage for gaps and overlaps."""
    gaps = []
    overlaps = []

    for i in range(len(line_ranges) - 1):
        current_end = line_ranges[i][1]
        next_start = line_ranges[i + 1][0]

        if next_start > current_end + 1:
            gaps.append((current_end + 1, next_start - 1))
        elif next_start <= current_end:
            overlap_size = current_end - next_start + 1
            overlaps.append(overlap_size)

    return gaps, overlaps


def print_chunk_details(chunk: QdrantKnowledgeBaseChunk, index: int, target_chunk_size: int = None) -> None:
    """Print detailed information about a single chunk."""
    content_length = len(chunk.metadata.content)

    print(f"      [{index}] Chunk ID: {chunk.metadata.id}")
    print(f"          📝 Name: {chunk.metadata.name}")
    print(f"          📏 Size: {content_length:,} characters")

    # Compare with target size
    if target_chunk_size:
        size_diff = content_length - target_chunk_size
        size_percent = (size_diff / target_chunk_size) * 100
        status = "✅" if abs(size_percent) <= 20 else "⚠️"
        print(f"          🎯 vs Target: {size_diff:+d} chars ({size_percent:+.1f}%) {status}")

    # Line range information
    if chunk.metadata.additional_metadata:
        line_start = chunk.metadata.additional_metadata.get("line_start")
        line_end = chunk.metadata.additional_metadata.get("line_end")
        if line_start and line_end:
            line_count = line_end - line_start + 1
            print(f"          📏 Lines: {line_start}-{line_end} ({line_count} lines)")

        # Show additional metadata
        metadata_items = []
        for key, value in chunk.metadata.additional_metadata.items():
            if key not in ["line_start", "line_end"]:
                metadata_items.append(f"{key}={value}")
        if metadata_items:
            print(f"          📋 Metadata: {', '.join(metadata_items)}")

    # Embeddings information
    if chunk.embeddings:
        print(f"          🧠 Embeddings: {len(chunk.embeddings)} dimensions")
        vector_magnitude = sum(x*x for x in chunk.embeddings) ** 0.5
        print(f"          📊 Vector Magnitude: {vector_magnitude:.3f}")

    # Content preview
    content_preview = chunk.metadata.content[:200].replace('\n', '\\n').replace('\r', '\\r')
    print(f"          📄 Preview: {repr(content_preview)}...")
    print()


def print_kb_statistics(all_chunks: List[QdrantKnowledgeBaseChunk], files_data: Dict[str, List],
                        chunk_size: int = None, overlap: int = None, chunking_method: str = None) -> None:
    """Print overall knowledge base statistics."""
    print("=" * 80)
    print("📊 KNOWLEDGE BASE STATISTICS")
    print("=" * 80)

    # Basic statistics
    total_files = len(files_data)
    total_chunks = len(all_chunks)
    total_content = sum(len(chunk.metadata.content) for chunk in all_chunks)
    avg_chunks_per_file = total_chunks / total_files if total_files > 0 else 0
    avg_chunk_size = total_content / total_chunks if total_chunks > 0 else 0

    print(f"📁 Files: {total_files}")
    print(f"🧩 Total Chunks: {total_chunks}")
    print(f"📏 Total Content: {total_content:,} characters")
    print(f"📊 Average Chunks per File: {avg_chunks_per_file:.1f}")
    print(f"📏 Average Chunk Size: {avg_chunk_size:.0f} characters")

    # Chunk size distribution
    chunk_sizes = [len(chunk.metadata.content) for chunk in all_chunks]
    if chunk_sizes:
        print(f"\n📈 CHUNK SIZE DISTRIBUTION:")
        print(f"   Min: {min(chunk_sizes):,} characters")
        print(f"   Max: {max(chunk_sizes):,} characters")
        print(f"   Median: {sorted(chunk_sizes)[len(chunk_sizes)//2]:,} characters")

        # Size buckets
        size_buckets = defaultdict(int)
        for size in chunk_sizes:
            bucket = (size // 500) * 500  # 500-char buckets
            size_buckets[bucket] += 1

        print(f"   Distribution:")
        for bucket in sorted(size_buckets.keys())[:10]:  # Show top 10 buckets
            count = size_buckets[bucket]
            percentage = (count / len(chunk_sizes)) * 100
            print(f"     {bucket:,}-{bucket+499:,} chars: {count} chunks ({percentage:.1f}%)")

    # Target compliance
    if chunk_size:
        compliant_chunks = sum(1 for size in chunk_sizes
                             if abs(size - chunk_size) / chunk_size <= 0.2)
        compliance_rate = (compliant_chunks / len(chunk_sizes)) * 100 if chunk_sizes else 0
        print(f"\n🎯 TARGET SIZE COMPLIANCE:")
        print(f"   Target: {chunk_size} characters (±20% tolerance)")
        print(f"   Compliant: {compliant_chunks}/{len(chunk_sizes)} chunks ({compliance_rate:.1f}%)")

    # File extension analysis
    extension_stats = defaultdict(lambda: {"files": 0, "chunks": 0})
    for file_path, chunks in files_data.items():
        ext = Path(file_path).suffix.lower() or "no_extension"
        extension_stats[ext]["files"] += 1
        extension_stats[ext]["chunks"] += len(chunks)

    if extension_stats:
        print(f"\n📄 FILE TYPE ANALYSIS:")
        for ext, stats in sorted(extension_stats.items(), key=lambda x: x[1]["chunks"], reverse=True):
            avg_chunks = stats["chunks"] / stats["files"] if stats["files"] > 0 else 0
            print(f"   {ext}: {stats['files']} files, {stats['chunks']} chunks (avg {avg_chunks:.1f} chunks/file)")

    # Embeddings analysis
    embedded_chunks = sum(1 for chunk in all_chunks if chunk.embeddings)
    if embedded_chunks > 0:
        print(f"\n🧠 EMBEDDINGS ANALYSIS:")
        print(f"   Embedded Chunks: {embedded_chunks}/{total_chunks} ({(embedded_chunks/total_chunks)*100:.1f}%)")

        # Sample embedding dimensions
        sample_chunk = next((chunk for chunk in all_chunks if chunk.embeddings), None)
        if sample_chunk:
            print(f"   Embedding Dimensions: {len(sample_chunk.embeddings)}")

    print("=" * 80)


def analyze_file_level_details(kb: QdrantKnowledgeBase, chunk_size: int = None, overlap: int = None) -> Dict[str, Any]:
    """Perform detailed file-level analysis of a knowledge base."""
    if not DETAILED_ANALYSIS_AVAILABLE:
        return {"error": "Detailed analysis not available"}

    try:
        print(f"\n🔍 DETAILED FILE ANALYSIS: {kb.name}")
        if chunk_size:
            print(f"📏 Target Chunk Size: {chunk_size} characters")
        if overlap:
            print(f"🔄 Target Overlap: {overlap} characters")
        print("-" * 60)

        # Get all chunks from the knowledge base
        start_time = time.time()
        all_chunks = kb.get_all_chunks()
        load_time = time.time() - start_time

        print(f"📊 Loaded {len(all_chunks)} chunks in {load_time:.3f}s")

        if not all_chunks:
            print("⚠️  No chunks found in knowledge base")
            return {"files": {}, "summary": {"total_files": 0, "total_chunks": 0}}

        # Group chunks by file
        files_analysis = defaultdict(lambda: {
            "chunks": [],
            "total_content_length": 0,
            "line_ranges": [],
            "chunk_sizes": [],
            "embeddings_info": {},
            "chunk_size_analysis": {},
            "overlap_analysis": {}
        })

        for chunk in all_chunks:
            file_path = chunk.metadata.file
            content_length = len(chunk.metadata.content)

            files_analysis[file_path]["chunks"].append(chunk)
            files_analysis[file_path]["total_content_length"] += content_length
            files_analysis[file_path]["chunk_sizes"].append(content_length)

            # Extract line range information
            if chunk.metadata.additional_metadata:
                line_start = chunk.metadata.additional_metadata.get("line_start")
                line_end = chunk.metadata.additional_metadata.get("line_end")
                if line_start and line_end:
                    files_analysis[file_path]["line_ranges"].append((line_start, line_end))

            # Analyze embeddings
            if chunk.embeddings:
                files_analysis[file_path]["embeddings_info"] = {
                    "dimensions": len(chunk.embeddings),
                    "sample_values": chunk.embeddings[:5],
                    "vector_magnitude": sum(x*x for x in chunk.embeddings) ** 0.5
                }

        # Display detailed analysis for each file
        for file_path, analysis in files_analysis.items():
            print(f"\n📁 FILE: {file_path}")
            print(f"   Chunks: {len(analysis['chunks'])}")
            print(f"   Total Content: {analysis['total_content_length']:,} characters")

            # Chunk size statistics
            chunk_sizes = analysis["chunk_sizes"]
            if chunk_sizes:
                min_size = min(chunk_sizes)
                max_size = max(chunk_sizes)
                avg_size = sum(chunk_sizes) // len(chunk_sizes)
                print(f"   Chunk Sizes: min={min_size}, max={max_size}, avg={avg_size}")
                
                # Compare with target chunk size if provided
                if chunk_size:
                    size_deviation = abs(avg_size - chunk_size)
                    deviation_percent = (size_deviation / chunk_size) * 100
                    print(f"   Size Deviation from Target: {size_deviation} chars ({deviation_percent:.1f}%)")

            # Line coverage analysis
            line_ranges = analysis["line_ranges"]
            if line_ranges:
                sorted_ranges = sorted(line_ranges)
                first_line = sorted_ranges[0][0]
                last_line = sorted_ranges[-1][1]
                total_lines = last_line - first_line + 1
                print(f"   Line Coverage: {first_line}-{last_line} ({total_lines} lines)")

                # Check for gaps in line coverage
                gaps = []
                overlaps = []
                for i in range(len(sorted_ranges) - 1):
                    current_end = sorted_ranges[i][1]
                    next_start = sorted_ranges[i + 1][0]
                    if next_start > current_end + 1:
                        gaps.append((current_end + 1, next_start - 1))
                    elif next_start <= current_end:
                        overlap_size = current_end - next_start + 1
                        overlaps.append(overlap_size)

                if gaps:
                    print(f"   ⚠️  Line Gaps: {len(gaps)} gaps found")
                    for gap in gaps[:3]:  # Show first 3 gaps
                        gap_size = gap[1] - gap[0] + 1
                        print(f"      Gap: lines {gap[0]}-{gap[1]} ({gap_size} lines)")
                
                if overlaps:
                    avg_overlap = sum(overlaps) / len(overlaps)
                    print(f"   🔄 Line Overlaps: {len(overlaps)} overlaps, avg {avg_overlap:.1f} lines")
                    if overlap:
                        overlap_deviation = abs(avg_overlap - overlap)
                        print(f"   Overlap Deviation from Target: {overlap_deviation:.1f} lines")

            # Embeddings info
            embeddings_info = analysis["embeddings_info"]
            if embeddings_info:
                print(f"   Embeddings: {embeddings_info['dimensions']} dimensions")
                print(f"   Vector Sample: {[f'{x:.3f}' for x in embeddings_info['sample_values']]}")
                print(f"   Vector Magnitude: {embeddings_info['vector_magnitude']:.3f}")

        return {
            "files": dict(files_analysis),
            "summary": {
                "total_files": len(files_analysis),
                "total_chunks": len(all_chunks),
                "load_time": load_time
            }
        }

    except Exception as e:
        print(f"❌ Error in file analysis: {e}")
        return {"error": str(e)}


def analyze_chunk_level_details(kb: QdrantKnowledgeBase, file_path: str, max_chunks: int = 5,
                               chunk_size: int = None, overlap: int = None) -> Dict[str, Any]:
    """Perform detailed chunk-level analysis for a specific file."""
    if not DETAILED_ANALYSIS_AVAILABLE:
        return {"error": "Detailed analysis not available"}

    try:
        print(f"\n🔬 CHUNK-LEVEL ANALYSIS: {os.path.basename(file_path)}")
        if chunk_size:
            print(f"📏 Target Chunk Size: {chunk_size} characters")
        if overlap:
            print(f"🔄 Target Overlap: {overlap} characters")
        print("-" * 60)

        # Get chunks for the specific file
        chunks = kb.get_chunks_by_file_path(file_path, sort_by_line=True)

        if not chunks:
            print(f"⚠️  No chunks found for file: {file_path}")
            return {"chunks": [], "analysis": {}}

        print(f"📊 Found {len(chunks)} chunks for this file")

        chunk_details = []
        for i, chunk in enumerate(chunks[:max_chunks]):
            print(f"\n🧩 CHUNK {i+1}/{min(len(chunks), max_chunks)}:")
            print(f"   ID: {chunk.metadata.id}")
            print(f"   Name: {chunk.metadata.name}")

            content_length = len(chunk.metadata.content)
            print(f"   Content Length: {content_length:,} characters")

            # Compare with target chunk size
            if chunk_size:
                size_diff = content_length - chunk_size
                size_percent = (size_diff / chunk_size) * 100
                status = "✅" if abs(size_percent) <= 10 else "⚠️"
                print(f"   Size vs Target: {size_diff:+d} chars ({size_percent:+.1f}%) {status}")

            # Line range information
            if chunk.metadata.additional_metadata:
                line_start = chunk.metadata.additional_metadata.get("line_start")
                line_end = chunk.metadata.additional_metadata.get("line_end")
                if line_start and line_end:
                    line_count = line_end - line_start + 1
                    print(f"   Line Range: {line_start}-{line_end} ({line_count} lines)")

                    # Check overlap with previous chunk
                    if i > 0:
                        prev_chunk = chunks[i-1]
                        if prev_chunk.metadata.additional_metadata:
                            prev_end = prev_chunk.metadata.additional_metadata.get("line_end")
                            if prev_end and line_start <= prev_end:
                                actual_overlap = prev_end - line_start + 1
                                print(f"   Overlap with Previous: {actual_overlap} lines")
                                if overlap:
                                    overlap_diff = actual_overlap - overlap
                                    overlap_status = "✅" if abs(overlap_diff) <= 2 else "⚠️"
                                    print(f"   Overlap vs Target: {overlap_diff:+d} lines {overlap_status}")

                # Show all additional metadata
                print(f"   Additional Metadata: {chunk.metadata.additional_metadata}")

            # Embeddings information
            if chunk.embeddings:
                print(f"   Embeddings: {len(chunk.embeddings)} dimensions")
                print(f"   Vector Sample: {[f'{x:.3f}' for x in chunk.embeddings[:5]]}")

                # Calculate vector statistics
                vector_sum = sum(chunk.embeddings)
                vector_magnitude = sum(x*x for x in chunk.embeddings) ** 0.5
                print(f"   Vector Stats: sum={vector_sum:.3f}, magnitude={vector_magnitude:.3f}")

            # Content preview
            content_preview = chunk.metadata.content[:200].replace('\n', '\\n')
            print(f"   Content Preview: {repr(content_preview)}...")

            chunk_details.append({
                "id": chunk.metadata.id,
                "content_length": content_length,
                "line_range": (
                    chunk.metadata.additional_metadata.get("line_start"),
                    chunk.metadata.additional_metadata.get("line_end")
                ) if chunk.metadata.additional_metadata else None,
                "embeddings_dimensions": len(chunk.embeddings) if chunk.embeddings else 0,
                "additional_metadata": chunk.metadata.additional_metadata
            })

        if len(chunks) > max_chunks:
            print(f"\n... and {len(chunks) - max_chunks} more chunks")

        return {
            "chunks": chunk_details,
            "analysis": {
                "total_chunks": len(chunks),
                "displayed_chunks": min(len(chunks), max_chunks),
                "file_path": file_path
            }
        }

    except Exception as e:
        print(f"❌ Error in chunk analysis: {e}")
        return {"error": str(e)}


def test_get_chunks_by_file_path():
    """
    Test function for the get_chunks_by_file_path method.

    This function demonstrates how to:
    1. Import and use the get_chunks_by_file_path method
    2. Take a file path as input (via user prompt)
    3. Search across ALL knowledge bases automatically
    4. Display comprehensive information about each chunk found from all KBs
    """
    if not DETAILED_ANALYSIS_AVAILABLE:
        print("❌ ERROR: Cannot run test - missing knowledge base models")
        print("   Please ensure the client_server module is available.")
        return False

    print("\n" + "=" * 80)
    print("🧪 CROSS-KB FILE PATH SEARCH TEST")
    print("=" * 80)
    print("This test searches for a file path across ALL knowledge bases")
    print("and displays chunks from any KB that contains the specified file.")
    print()

    try:
        # Step 1: Get file path from user (no KB ID needed!)
        print("📁 STEP 1: File Path Selection")
        print("-" * 40)
        print("Enter the file path you want to search for across all knowledge bases.")
        print("Examples:")
        print("  - src/main.py")
        print("  - docs/README.md")
        print("  - /full/path/to/file.txt")
        print()

        file_path = input("Enter file path: ").strip()
        if not file_path:
            print("❌ File path cannot be empty.")
            return False

        # Step 2: Perform cross-KB search using the new static method
        print(f"\n🔍 STEP 2: Performing cross-KB search for file: {file_path}")
        print("-" * 40)

        try:
            search_result = QdrantKnowledgeBase.find_file_across_all_kbs(
                file_path=file_path,
                sort_by_line=True,
                include_kb_metadata=True,
                max_retries_per_kb=3
            )
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return False

        print(f"📊 Searched {search_result.total_kbs_searched} knowledge bases")
        print(f"⏱️  Search completed in {search_result.search_time_seconds:.3f} seconds")
        print(f"🎯 Found file in {search_result.kbs_with_matches} knowledge base(s) with {search_result.total_chunks_found} total chunks")

        # Display any errors that occurred during search
        if search_result.errors:
            print(f"\n⚠️  {len(search_result.errors)} error(s) occurred during search:")
            for error in search_result.errors:
                print(f"   • {error}")

        if not search_result.has_matches:
            print("\n⚠️  FILE NOT FOUND in any knowledge base.")
            print("   This could mean:")
            print("   • The file path doesn't exist in any knowledge base")
            print("   • The file path format doesn't match the stored format")
            print("   • The file hasn't been chunked in any KB yet")
            print("\n💡 Try checking the exact file path format used in your knowledge bases.")
            return True

        # Step 3: Display comprehensive results from all KBs
        print(f"\n📋 STEP 3: Detailed Results from All Knowledge Bases")
        print("=" * 80)

        # Overall summary statistics
        matching_results = [result for result in search_result.kb_results if result.has_chunks]
        all_chunks = []
        for kb_result in matching_results:
            all_chunks.extend(kb_result.chunks)

        chunk_sizes = [len(chunk.metadata.content) for chunk in all_chunks] if all_chunks else []

        print(f"📊 OVERALL SUMMARY:")
        print(f"   📁 File Path: {search_result.file_path}")
        print(f"   🏢 Knowledge Bases Found: {search_result.kbs_with_matches}")
        print(f"   🧩 Total Chunks: {search_result.total_chunks_found}")
        print(f"   📏 Total Content: {search_result.total_content_length:,} characters")
        if chunk_sizes:
            avg_chunk_size = search_result.average_chunk_size_across_all_kbs
            print(f"   📊 Chunk Sizes: min={min(chunk_sizes)}, max={max(chunk_sizes)}, avg={avg_chunk_size:.0f}")
        print()

        # Display results from each KB
        for kb_index, kb_result in enumerate(matching_results):
            print(f"🏢 KNOWLEDGE BASE {kb_index + 1}/{len(matching_results)}: {kb_result.kb_name}")
            print(f"   🆔 KB ID: {kb_result.kb_id}")
            print(f"   📊 Chunks in this KB: {kb_result.total_chunks}")
            print(f"   ⏱️  Search time: {kb_result.search_time_seconds:.3f}s")
            if kb_result.kb_type:
                print(f"   🏷️  KB Type: {kb_result.kb_type}")
            print("-" * 60)

            # KB-specific statistics
            kb_chunk_sizes = [len(chunk.metadata.content) for chunk in kb_result.chunks]

            print(f"   📏 KB Content: {kb_result.total_content_length:,} characters")
            if kb_chunk_sizes:
                print(f"   📊 KB Chunk Sizes: min={min(kb_chunk_sizes)}, max={max(kb_chunk_sizes)}, avg={kb_result.average_chunk_size:.0f}")

            # Line coverage analysis for this KB
            line_ranges = []
            for chunk in kb_result.chunks:
                if chunk.metadata.additional_metadata:
                    line_start = chunk.metadata.additional_metadata.get("line_start")
                    line_end = chunk.metadata.additional_metadata.get("line_end")
                    if line_start and line_end:
                        line_ranges.append((line_start, line_end))

            if line_ranges:
                first_line = line_ranges[0][0]
                last_line = line_ranges[-1][1]
                total_lines = last_line - first_line + 1
                print(f"   📏 Line Coverage: {first_line}-{last_line} ({total_lines} lines)")

            print()

            # Display individual chunks for this KB
            print(f"   🧩 CHUNKS FROM THIS KB:")
            for i, chunk in enumerate(kb_result.chunks):
                print(f"\n   [{i+1}/{kb_result.total_chunks}] CHUNK DETAILS:")
                print(f"      🆔 Chunk ID: {chunk.metadata.id}")
                print(f"      📝 Name: {chunk.metadata.name}")
                print(f"      📁 File: {chunk.metadata.file}")

                content_length = len(chunk.metadata.content)
                print(f"      📏 Content Length: {content_length:,} characters")

                # Line range information
                if chunk.metadata.additional_metadata:
                    line_start = chunk.metadata.additional_metadata.get("line_start")
                    line_end = chunk.metadata.additional_metadata.get("line_end")
                    if line_start and line_end:
                        line_count = line_end - line_start + 1
                        print(f"      📏 Line Range: {line_start}-{line_end} ({line_count} lines)")

                    # Display key additional metadata
                    metadata_items = []
                    for key, value in chunk.metadata.additional_metadata.items():
                        if key not in ["line_start", "line_end"]:
                            metadata_items.append(f"{key}={value}")
                    if metadata_items:
                        print(f"      📋 Other Metadata: {', '.join(metadata_items)}")

                # Embeddings information
                if chunk.embeddings:
                    print(f"      🧠 Embeddings: {len(chunk.embeddings)} dimensions")
                    vector_magnitude = sum(x*x for x in chunk.embeddings) ** 0.5
                    print(f"      📊 Vector Magnitude: {vector_magnitude:.3f}")
                else:
                    print(f"      🧠 Embeddings: Not available")

                # Content preview
                content_preview = chunk.metadata.content[:200].replace('\n', '\\n').replace('\r', '\\r')
                if len(chunk.metadata.content) > 200:
                    content_preview += "..."
                print(f"      📄 Content Preview: {repr(content_preview)}")

            print("=" * 60)

        print(f"\n✅ Cross-KB search completed successfully!")
        print(f"🎯 Found file '{search_result.file_path}' in {search_result.kbs_with_matches} knowledge base(s)")
        print(f"📊 Total chunks retrieved: {search_result.total_chunks_found}")
        print(f"⏱️  Total search time: {search_result.search_time_seconds:.3f} seconds")

        return True

    except KeyboardInterrupt:
        print(f"\n❌ Test interrupted by user")
        return False
    except Exception as e:
        print(f"❌ ERROR during test: {e}")
        import traceback
        traceback.print_exc()
        return False


def perform_validation_checks(kb: QdrantKnowledgeBase, kb_name: str, chunk_size: int = None,
                             overlap: int = None, chunking_method: str = None) -> Dict[str, Any]:
    """Perform validation checks on the knowledge base."""
    validation_results = {
        'kb_name': kb_name,
        'kb_id': kb.id,
        'validation_errors': [],
        'validation_warnings': [],
        'data_quality_issues': [],
        'performance_concerns': []
    }

    try:
        all_chunks = kb.get_all_chunks()

        if not all_chunks:
            validation_results['validation_errors'].append("No chunks found in knowledge base")
            return validation_results

        # Check for missing metadata
        chunks_without_file = sum(1 for chunk in all_chunks if not chunk.metadata.file)
        if chunks_without_file > 0:
            validation_results['data_quality_issues'].append(
                f"{chunks_without_file} chunks missing file path information"
            )

        # Check for missing embeddings
        chunks_without_embeddings = sum(1 for chunk in all_chunks if not chunk.embeddings)
        if chunks_without_embeddings > 0:
            validation_results['validation_warnings'].append(
                f"{chunks_without_embeddings} chunks missing embeddings"
            )

        # Check chunk size consistency
        if chunk_size:
            chunk_sizes = [len(chunk.metadata.content) for chunk in all_chunks]
            non_compliant = sum(1 for size in chunk_sizes
                              if abs(size - chunk_size) / chunk_size > 0.2)
            if non_compliant > len(chunk_sizes) * 0.3:  # More than 30% non-compliant
                validation_results['data_quality_issues'].append(
                    f"High chunk size variance: {non_compliant}/{len(chunk_sizes)} chunks exceed ±20% tolerance"
                )

        # Check for very large or very small chunks
        chunk_sizes = [len(chunk.metadata.content) for chunk in all_chunks]
        very_large = sum(1 for size in chunk_sizes if size > 10000)
        very_small = sum(1 for size in chunk_sizes if size < 100)

        if very_large > 0:
            validation_results['performance_concerns'].append(
                f"{very_large} chunks are very large (>10,000 chars) - may impact performance"
            )

        if very_small > 0:
            validation_results['data_quality_issues'].append(
                f"{very_small} chunks are very small (<100 chars) - may lack context"
            )

        # Check for duplicate content
        content_hashes = set()
        duplicates = 0
        for chunk in all_chunks:
            content_hash = hash(chunk.metadata.content)
            if content_hash in content_hashes:
                duplicates += 1
            else:
                content_hashes.add(content_hash)

        if duplicates > 0:
            validation_results['data_quality_issues'].append(
                f"{duplicates} chunks appear to have duplicate content"
            )

    except Exception as e:
        validation_results['validation_errors'].append(f"Error during validation: {e}")

    return validation_results


def print_validation_report(validation_results: Dict[str, Any]) -> None:
    """Print detailed validation report."""
    print("=" * 80)
    print("� KNOWLEDGE BASE VALIDATION REPORT")
    print("=" * 80)

    print(f"📋 Knowledge Base: {validation_results['kb_name']}")
    print(f"🆔 KB ID: {validation_results['kb_id']}")
    print()

    print(f"📊 Validation Summary:")
    print(f"   ❌ Errors: {len(validation_results['validation_errors'])}")
    print(f"   ⚠️  Warnings: {len(validation_results['validation_warnings'])}")
    print(f"   📋 Data Quality Issues: {len(validation_results['data_quality_issues'])}")
    print(f"   ⚡ Performance Concerns: {len(validation_results['performance_concerns'])}")
    print()

    # Print errors
    if validation_results['validation_errors']:
        print("❌ VALIDATION ERRORS:")
        for error in validation_results['validation_errors']:
            print(f"   • {error}")
        print()

    # Print warnings
    if validation_results['validation_warnings']:
        print("⚠️  VALIDATION WARNINGS:")
        for warning in validation_results['validation_warnings']:
            print(f"   • {warning}")
        print()

    # Print data quality issues
    if validation_results['data_quality_issues']:
        print("� DATA QUALITY ISSUES:")
        for issue in validation_results['data_quality_issues']:
            print(f"   • {issue}")
        print()

    # Print performance concerns
    if validation_results['performance_concerns']:
        print("⚡ PERFORMANCE CONCERNS:")
        for concern in validation_results['performance_concerns']:
            print(f"   • {concern}")
        print()

    # Overall assessment
    total_issues = (len(validation_results['validation_errors']) +
                   len(validation_results['validation_warnings']) +
                   len(validation_results['data_quality_issues']) +
                   len(validation_results['performance_concerns']))

    if total_issues == 0:
        print("✅ All validation checks passed! Knowledge base is in good condition.")
    else:
        print(f"📋 Found {total_issues} total issues that may need attention.")

    print("=" * 80)





def parse_arguments():
    """Parse command-line arguments for chunking analysis configuration."""
    parser = argparse.ArgumentParser(
        description="Standalone Chunking Analysis Tool - Analyze a single knowledge base chunking strategy",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python chunking_analysis_test.py --chunk-size 1000 --overlap 100
  python chunking_analysis_test.py --chunk-size 2000 --chunking-method semantic
  python chunking_analysis_test.py --overlap 50 --max-chunks-per-file 10 --validate
  python chunking_analysis_test.py --test-file-path  # Test get_chunks_by_file_path function
        """
    )

    # Test mode
    parser.add_argument(
        "--test-file-path",
        action="store_true",
        help="Run the cross-KB file path search test (searches all knowledge bases for a file path)"
    )

    # Chunking strategy parameters
    parser.add_argument(
        "--chunk-size",
        type=int,
        help="Target chunk size in characters (e.g., 1000, 2000)"
    )

    parser.add_argument(
        "--overlap",
        type=int,
        help="Target overlap size in characters or lines (e.g., 100, 200)"
    )

    parser.add_argument(
        "--chunking-method",
        choices=["fixed", "semantic", "sentence", "paragraph", "token"],
        help="Chunking method/strategy to analyze against"
    )

    # Analysis configuration
    parser.add_argument(
        "--max-chunks-per-file",
        type=int,
        default=5,
        help="Maximum number of chunks to display per file (default: 5)"
    )

    # Validation and output options
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Enable comprehensive validation checks"
    )

    return parser.parse_args()





def main():
    """Main function to execute the standalone chunking analysis."""
    args = parse_arguments()

    print("=" * 80)
    print("🔬 STANDALONE CHUNKING ANALYSIS TOOL")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Display configuration
    print(f"\n📋 Analysis Configuration:")
    if args.chunk_size:
        print(f"   📏 Target Chunk Size: {args.chunk_size} characters")
    if args.overlap:
        print(f"   🔄 Target Overlap: {args.overlap} characters")
    if args.chunking_method:
        print(f"   🔧 Chunking Method: {args.chunking_method}")
    print(f"   🧩 Max Chunks per File: {args.max_chunks_per_file}")
    if args.validate:
        print(f"   🔍 Validation: Enabled")

    if not DETAILED_ANALYSIS_AVAILABLE:
        print("\n❌ ERROR: Detailed analysis not available")
        print("   Missing knowledge base models. Please ensure the client_server module is available.")
        return 1

    # Check if we're running the file path test
    if args.test_file_path:
        print("\n🧪 RUNNING GET_CHUNKS_BY_FILE_PATH TEST")
        print("=" * 80)
        success = test_get_chunks_by_file_path()
        return 0 if success else 1

    print("=" * 80)

    try:
        # Get KB ID and name from user input
        kb_id, kb_name = get_kb_input()

        # Validate KB access
        kb = validate_kb_access(kb_id)
        if not kb:
            return 1

        print(f"\n✅ Successfully connected to Knowledge Base: {kb_name}")
        print()

        # Perform comprehensive analysis of the single KB
        analysis_result = analyze_single_kb_comprehensive(
            kb,
            kb_name,
            chunk_size=args.chunk_size,
            overlap=args.overlap,
            chunking_method=args.chunking_method,
            max_chunks_per_file=args.max_chunks_per_file
        )

        if analysis_result.get("error"):
            print(f"❌ Analysis failed: {analysis_result['error']}")
            return 1

        # Perform validation checks if requested
        if args.validate:
            validation_results = perform_validation_checks(
                kb, kb_name,
                chunk_size=args.chunk_size,
                overlap=args.overlap,
                chunking_method=args.chunking_method
            )
            print_validation_report(validation_results)

        # Summary and recommendations
        print("=" * 80)
        print("💡 ANALYSIS SUMMARY & RECOMMENDATIONS")
        print("=" * 80)

        summary = analysis_result.get("summary", {})
        if summary:
            print(f"📊 Analysis completed successfully:")
            print(f"   � Files analyzed: {summary.get('total_files', 0)}")
            print(f"   🧩 Total chunks: {summary.get('total_chunks', 0)}")
            print(f"   ⏱️  Load time: {summary.get('load_time', 0):.3f}s")

        # Provide recommendations based on configuration
        print(f"\n📋 RECOMMENDATIONS:")
        if not args.chunk_size:
            print("• Consider specifying --chunk-size to analyze size compliance")
        if not args.overlap:
            print("• Consider specifying --overlap to analyze overlap patterns")
        if not args.chunking_method:
            print("• Consider specifying --chunking-method to validate chunking strategy")
        if not args.validate:
            print("• Use --validate flag for comprehensive validation checks")

        print(f"\n✅ Standalone chunking analysis complete!")

        return 0

    except KeyboardInterrupt:
        print(f"\n❌ Analysis interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
