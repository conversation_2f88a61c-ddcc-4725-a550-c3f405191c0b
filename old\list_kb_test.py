#!/usr/bin/env python3
"""
Knowledge Base Listing Test Script

This script focuses on listing and displaying knowledge bases with comprehensive
diagnostic capabilities including sync status analysis and path uniqueness validation.
"""

import requests
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict
import sys

# Configuration
SESSION_ID = "d4d38050-b68f-4ebd-83d5-09580b08afc9"
BASE_URL = "http://localhost:45213"
API_ENDPOINT = f"{BASE_URL}/list_kbs"


def format_timestamp(timestamp: int) -> str:
    """Convert timestamp to readable format."""
    if not timestamp or timestamp <= 0:
        return "Never"
    
    try:
        # Handle both seconds and milliseconds timestamps
        if timestamp > 1e12:  # Milliseconds
            timestamp = timestamp / 1000
        return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, OSError):
        return f"Invalid timestamp: {timestamp}"


def get_sync_status_icon(sync_status: str) -> str:
    """Get appropriate icon for sync status."""
    icons = {
        'upload_needed': '⬆️',
        'sync_available': '🔄', 
        'synced': '✅',
        'unknown': '❓'
    }
    return icons.get(sync_status, '❓')


def get_source_icon(source: str) -> str:
    """Get appropriate icon for KB source."""
    icons = {
        'LOCAL': '🏠',
        'REMOTE': '☁️'
    }
    return icons.get(source.upper(), '📁')


def determine_sync_status(kb: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine sync status based on simplified cloud sync workflow.
    This replicates the backend logic for demonstration.
    """
    cloud_id = kb.get('cloud_id')
    source = kb.get('source', '').upper()
    
    sync_info = {
        'sync_status': 'unknown',
        'cloud_sync_available': False,
        'can_upload': False,
        'can_sync': False,
        'status_reason': ''
    }
    
    if source == 'REMOTE':
        sync_info.update({
            'sync_status': 'synced',
            'cloud_sync_available': True,
            'can_upload': False,
            'can_sync': False,
            'status_reason': 'Cloud knowledge base - always synced'
        })
    elif source == 'LOCAL':
        if not cloud_id:
            sync_info.update({
                'sync_status': 'upload_needed',
                'cloud_sync_available': True,
                'can_upload': True,
                'can_sync': False,
                'status_reason': 'Local KB not yet uploaded to cloud'
            })
        else:
            sync_info.update({
                'sync_status': 'sync_available',
                'cloud_sync_available': True,
                'can_upload': False,
                'can_sync': True,
                'status_reason': 'KB can be synced with cloud'
            })
    
    return sync_info


def print_kb_details(kb: Dict[str, Any], index: int, section_type: str) -> None:
    """Print detailed information about a knowledge base."""
    # Determine sync status
    sync_info = determine_sync_status(kb)
    sync_icon = get_sync_status_icon(sync_info['sync_status'])
    source_icon = get_source_icon(kb.get('source', ''))
    
    print(f"  [{index}] {kb.get('name', 'Unknown Name')} {sync_icon}")
    print(f"      📋 Basic Info:")
    print(f"         ID: {kb.get('id', 'N/A')}")
    print(f"         Source: {source_icon} {kb.get('source', 'Unknown')} ({section_type})")
    print(f"         Scope: {kb.get('scope', 'Unknown')}")
    print(f"         Type: {kb.get('type', 'Unknown')}")
    print(f"         Status: {kb.get('status', 'Unknown')}")
    
    print(f"      🔄 Cloud Sync Status:")
    print(f"         Sync Status: {sync_info['sync_status']} {sync_icon}")
    print(f"         Can Upload: {'✅' if sync_info['can_upload'] else '❌'}")
    print(f"         Can Sync: {'✅' if sync_info['can_sync'] else '❌'}")
    print(f"         Cloud Sync Available: {'✅' if sync_info['cloud_sync_available'] else '❌'}")
    print(f"         Reason: {sync_info['status_reason']}")
    
    print(f"      ☁️  Cloud Information:")
    cloud_id = kb.get('cloud_id')
    print(f"         Cloud ID: {cloud_id if cloud_id else 'None'}")
    
    print(f"      📁 Path & Indexing:")
    print(f"         Auto-Indexed: {'✅' if kb.get('isAutoIndexed') else '❌'}")
    
    # Path information
    metadata = kb.get('metadata', {})
    if isinstance(metadata, dict) and 'path' in metadata:
        print(f"         Path: {metadata['path']}")
    else:
        print(f"         Path: Not available")
    
    print(f"      ⏰ Sync Configuration:")
    sync_config = kb.get('syncConfig', {})
    if isinstance(sync_config, dict):
        sync_enabled = sync_config.get('enabled', False)
        last_synced = sync_config.get('lastSynced', 0)
        print(f"         Sync Enabled: {'✅' if sync_enabled else '❌'}")
        print(f"         Last Synced: {format_timestamp(last_synced)}")
    else:
        print(f"         Sync Config: {sync_config}")
    
    if kb.get('description'):
        print(f"      📝 Description: {kb.get('description')}")
    
    print()  # Empty line for readability


def categorize_knowledge_bases(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Categorize knowledge bases by source."""
    local_kbs = []
    cloud_kbs = []
    
    for kb in knowledge_bases:
        source = kb.get('source', '').upper()
        if source == 'REMOTE':
            cloud_kbs.append(kb)
        else:
            local_kbs.append(kb)
    
    return {'local': local_kbs, 'cloud': cloud_kbs}


def analyze_sync_statuses(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, int]:
    """Analyze sync status distribution."""
    status_counts = {
        'upload_needed': 0,
        'sync_available': 0,
        'synced': 0,
        'unknown': 0
    }
    
    for kb in knowledge_bases:
        sync_info = determine_sync_status(kb)
        status = sync_info['sync_status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    return status_counts


def analyze_path_uniqueness(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze path-based uniqueness constraints."""
    paths_seen = {}
    auto_indexed_count = 0
    manually_uploaded_count = 0
    path_conflicts = []
    
    for kb in knowledge_bases:
        # Count indexing types
        if kb.get('isAutoIndexed'):
            auto_indexed_count += 1
        else:
            manually_uploaded_count += 1
        
        # Check path uniqueness
        metadata = kb.get('metadata', {})
        if isinstance(metadata, dict) and 'path' in metadata:
            path = metadata['path']
            if path in paths_seen:
                path_conflicts.append({
                    'path': path,
                    'kbs': [paths_seen[path], kb.get('name', 'Unknown')]
                })
            else:
                paths_seen[path] = kb.get('name', 'Unknown')
    
    return {
        'total_paths': len(paths_seen),
        'auto_indexed_count': auto_indexed_count,
        'manually_uploaded_count': manually_uploaded_count,
        'path_conflicts': path_conflicts
    }


def print_workflow_demonstration(knowledge_bases: List[Dict[str, Any]]) -> None:
    """Demonstrate the simplified cloud sync workflow."""
    print("=" * 80)
    print("🔄 SIMPLIFIED CLOUD SYNC WORKFLOW DEMONSTRATION")
    print("=" * 80)

    print("Workflow Logic:")
    print("1. Local KB without cloud_id → ⬆️  Upload Needed")
    print("2. Local KB with cloud_id → 🔄 Sync Available")
    print("3. Remote KB → ✅ Synced (always)")
    print()

    print("Current Knowledge Bases in Workflow:")
    for kb in knowledge_bases:
        sync_info = determine_sync_status(kb)
        icon = get_sync_status_icon(sync_info['sync_status'])
        source_icon = get_source_icon(kb.get('source', ''))

        print(f"  {icon} {kb.get('name', 'Unknown')}")
        print(f"     Source: {source_icon} {kb.get('source', 'Unknown')}")
        print(f"     Cloud ID: {'✅ Present' if kb.get('cloud_id') else '❌ None'}")
        print(f"     Status: {sync_info['sync_status']}")
        print(f"     Next Action: {get_next_action_suggestion(sync_info)}")
        print()

    print("=" * 80)


def get_next_action_suggestion(sync_info: Dict[str, Any]) -> str:
    """Get suggested next action based on sync status."""
    if sync_info['can_upload']:
        return "Upload to cloud to enable syncing"
    elif sync_info['can_sync']:
        return "Sync local changes with cloud"
    else:
        return "No action needed - already synced"


def print_summary_analysis(knowledge_bases: List[Dict[str, Any]]) -> None:
    """Print comprehensive summary analysis."""
    if not knowledge_bases:
        print("📊 No knowledge bases found for analysis")
        return

    categorized = categorize_knowledge_bases(knowledge_bases)
    sync_analysis = analyze_sync_statuses(knowledge_bases)
    path_analysis = analyze_path_uniqueness(knowledge_bases)

    print("=" * 80)
    print("📊 COMPREHENSIVE ANALYSIS SUMMARY")
    print("=" * 80)

    # Basic counts
    print(f"📈 Knowledge Base Counts:")
    print(f"   Total KBs: {len(knowledge_bases)}")
    print(f"   🏠 Local KBs: {len(categorized['local'])}")
    print(f"   ☁️  Cloud KBs: {len(categorized['cloud'])}")
    print()

    # Sync status distribution
    print(f"🔄 Sync Status Distribution:")
    print(f"   ⬆️  Upload Needed: {sync_analysis['upload_needed']}")
    print(f"   🔄 Sync Available: {sync_analysis['sync_available']}")
    print(f"   ✅ Synced: {sync_analysis['synced']}")
    print(f"   ❓ Unknown: {sync_analysis['unknown']}")
    print()

    # Path-based uniqueness analysis
    print(f"📁 Path-Based Uniqueness Analysis:")
    print(f"   Unique Paths: {path_analysis['total_paths']}")
    print(f"   Auto-Indexed KBs: {path_analysis['auto_indexed_count']}")
    print(f"   Manually Uploaded KBs: {path_analysis['manually_uploaded_count']}")

    if path_analysis['path_conflicts']:
        print(f"   ⚠️  Path Conflicts Found: {len(path_analysis['path_conflicts'])}")
        for conflict in path_analysis['path_conflicts']:
            print(f"      Path: {conflict['path']}")
            print(f"      KBs: {', '.join(conflict['kbs'])}")
    else:
        print(f"   ✅ No Path Conflicts: Path uniqueness constraint satisfied")
    print()

    # Cloud sync capabilities
    cloud_sync_capable = sum(1 for kb in knowledge_bases
                           if determine_sync_status(kb)['cloud_sync_available'])
    can_upload = sum(1 for kb in knowledge_bases
                    if determine_sync_status(kb)['can_upload'])
    can_sync = sum(1 for kb in knowledge_bases
                  if determine_sync_status(kb)['can_sync'])

    print(f"☁️  Cloud Sync Capabilities:")
    print(f"   Cloud Sync Available: {cloud_sync_capable}")
    print(f"   Can Upload to Cloud: {can_upload}")
    print(f"   Can Sync with Cloud: {can_sync}")

    print("=" * 80)


def print_path_uniqueness_validation() -> None:
    """Print path-based uniqueness constraint explanation."""
    print("=" * 80)
    print("📁 PATH-BASED UNIQUENESS CONSTRAINT")
    print("=" * 80)

    print("Core Principle:")
    print("• One knowledge base per unique path")
    print("• Either auto-indexed OR manually uploaded, not both")
    print()

    print("Enforcement Rules:")
    print("✅ Auto-indexed KB exists + user uploads same path → Upgrade (reuse ID)")
    print("❌ Uploaded KB exists + user uploads same path → Reject with error")
    print("❌ Uploaded KB exists + auto-indexing same path → Prevent auto-indexing")
    print("✅ Auto-indexed KB exists + auto-indexing same path → Reuse existing")
    print()

    print("Benefits:")
    print("• Eliminates confusion about which KB represents a folder")
    print("• Prevents duplicate processing of same content")
    print("• Clear upgrade path from auto-indexed to uploaded")
    print("• Simplified sync status determination")

    print("=" * 80)


def perform_validation_checks(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Perform comprehensive validation checks on knowledge bases."""
    validation_results = {
        'total_kbs': len(knowledge_bases),
        'validation_errors': [],
        'validation_warnings': [],
        'data_quality_issues': [],
        'performance_concerns': []
    }

    for kb in knowledge_bases:
        kb_name = kb.get('name', 'Unknown')
        kb_id = kb.get('id', 'N/A')

        # Check for required fields
        required_fields = ['id', 'name', 'source', 'status']
        for field in required_fields:
            if not kb.get(field):
                validation_results['validation_errors'].append(
                    f"KB '{kb_name}' ({kb_id}): Missing required field '{field}'"
                )

        # Check sync configuration consistency
        sync_config = kb.get('syncConfig', {})
        if isinstance(sync_config, dict):
            if sync_config.get('enabled') and not kb.get('cloud_id'):
                validation_results['validation_warnings'].append(
                    f"KB '{kb_name}': Sync enabled but no cloud_id present"
                )

        # Check metadata consistency
        metadata = kb.get('metadata', {})
        if kb.get('isAutoIndexed') and not isinstance(metadata, dict):
            validation_results['data_quality_issues'].append(
                f"KB '{kb_name}': Auto-indexed but metadata is not a dictionary"
            )

        # Check for potential performance issues
        if isinstance(metadata, dict) and 'path' in metadata:
            path = metadata['path']
            if len(path) > 260:  # Windows path length limit
                validation_results['performance_concerns'].append(
                    f"KB '{kb_name}': Very long path ({len(path)} chars) may cause issues"
                )

    return validation_results


def print_validation_report(validation_results: Dict[str, Any]) -> None:
    """Print detailed validation report."""
    print("=" * 80)
    print("🔍 KNOWLEDGE BASE VALIDATION REPORT")
    print("=" * 80)

    print(f"📊 Validation Summary:")
    print(f"   Total KBs Analyzed: {validation_results['total_kbs']}")
    print(f"   ❌ Errors: {len(validation_results['validation_errors'])}")
    print(f"   ⚠️  Warnings: {len(validation_results['validation_warnings'])}")
    print(f"   📋 Data Quality Issues: {len(validation_results['data_quality_issues'])}")
    print(f"   ⚡ Performance Concerns: {len(validation_results['performance_concerns'])}")
    print()

    # Print errors
    if validation_results['validation_errors']:
        print("❌ VALIDATION ERRORS:")
        for error in validation_results['validation_errors']:
            print(f"   • {error}")
        print()

    # Print warnings
    if validation_results['validation_warnings']:
        print("⚠️  VALIDATION WARNINGS:")
        for warning in validation_results['validation_warnings']:
            print(f"   • {warning}")
        print()

    # Print data quality issues
    if validation_results['data_quality_issues']:
        print("📋 DATA QUALITY ISSUES:")
        for issue in validation_results['data_quality_issues']:
            print(f"   • {issue}")
        print()

    # Print performance concerns
    if validation_results['performance_concerns']:
        print("⚡ PERFORMANCE CONCERNS:")
        for concern in validation_results['performance_concerns']:
            print(f"   • {concern}")
        print()

    # Overall assessment
    total_issues = (len(validation_results['validation_errors']) +
                   len(validation_results['validation_warnings']) +
                   len(validation_results['data_quality_issues']) +
                   len(validation_results['performance_concerns']))

    if total_issues == 0:
        print("✅ All validation checks passed! Knowledge bases are in good condition.")
    else:
        print(f"📋 Found {total_issues} total issues that may need attention.")

    print("=" * 80)


def main():
    """Main function to execute the knowledge base listing test."""
    print("=" * 80)
    print("🧪 KNOWLEDGE BASE LISTING & ANALYSIS")
    print("=" * 80)
    print(f"Session ID: {SESSION_ID[:8]}...{SESSION_ID[-8:]}")
    print(f"API Endpoint: {API_ENDPOINT}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Check for validation flag
    enable_validation = "--validate" in sys.argv or "-v" in sys.argv
    if enable_validation:
        print("🔍 Validation checks enabled (use --validate or -v flag)")
    else:
        print("ℹ️  Basic listing mode (add --validate or -v for comprehensive validation)")

    print("=" * 80)
    print()

    try:
        # Make API request
        headers = {"x-session": SESSION_ID}
        params = {"include_cloud": True}

        print("🔄 Making API request...")
        response = requests.get(API_ENDPOINT, headers=headers, params=params, timeout=30)

        if response.status_code != 200:
            print(f"❌ API Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return 1

        knowledge_bases = response.json()
        print(f"✅ Successfully retrieved {len(knowledge_bases)} knowledge bases")
        print()

        if not knowledge_bases:
            print("📭 No knowledge bases found")
            return 0

        # Categorize and display
        categorized = categorize_knowledge_bases(knowledge_bases)
        local_kbs = categorized['local']
        cloud_kbs = categorized['cloud']

        # Display local knowledge bases
        if local_kbs:
            print("🏠 LOCAL KNOWLEDGE BASES")
            print("-" * 50)
            for i, kb in enumerate(local_kbs, 1):
                print_kb_details(kb, i, "Local Storage")
        else:
            print("🏠 LOCAL KNOWLEDGE BASES: None found")
            print()

        # Display cloud knowledge bases
        if cloud_kbs:
            print("☁️  CLOUD KNOWLEDGE BASES")
            print("-" * 50)
            for i, kb in enumerate(cloud_kbs, 1):
                print_kb_details(kb, i, "Cloud API")
        else:
            print("☁️  CLOUD KNOWLEDGE BASES: None found")
            print()

        # Print comprehensive analysis
        print_summary_analysis(knowledge_bases)

        # Demonstrate workflow
        print_workflow_demonstration(knowledge_bases)

        # Explain path uniqueness constraint
        print_path_uniqueness_validation()

        # Perform validation checks if enabled
        if enable_validation:
            validation_results = perform_validation_checks(knowledge_bases)
            print_validation_report(validation_results)
        else:
            print("\n💡 TIP: Add --validate flag for comprehensive validation checks")
            print("   Example: python list_kb_test.py --validate")

        return 0

    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST FAILED: {e}")
        return 1
    except json.JSONDecodeError as e:
        print(f"❌ JSON DECODE ERROR: {e}")
        return 1
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
