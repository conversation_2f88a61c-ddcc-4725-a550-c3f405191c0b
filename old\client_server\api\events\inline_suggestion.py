import requests
import socketio
import time
from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel, <PERSON>

from client_server.core.constants import SSL_CERT_FILE
from client_server.core.state import G_BASE_URL
from client_server.services.inference.utils import InferenceBuilder
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_api_call_stats
from client_server.utils.model_selector import ModelSelector

# -----------------------------------------------------------------------------


class InlineSuggestionRequest(BaseModel):
    """Request schema for inline suggestion"""

    requestID: str = Field(..., description="Unique identifier for the request")
    language: str = Field(
        default="Unknown", description="Programming language of the code"
    )

    # The code context is divided into three parts: prefix, current, and suffix.
    prefix: list[str] = Field(
        ...,
        description="Lines of code that come before the current line",
    )
    current: str = Field(..., description="The current line being edited")
    suffix: list[str] = Field(
        ...,
        description="Lines of code that come after the current line",
    )
    filepath: str = Field(..., description="The path to the file")
    position: dict = Field(..., description="The position of the cursor")


# -----------------------------------------------------------------------------

INLINE_SYSTEM_PROMPT = """
You are a code completion assistant.

Here's the instruction set for you:
1. User will provide you with a code snippet and you need to complete the code from where it is left off based on the provided context and information.
2. You have to generate the content that would best fit the context and information provided.
3. You have to generate the content in the same language as the code provided.
4. You have to generate the content that would replace the [INFILL] tag.
5. Don't say literally anything else except the content that would replace the [INFILL] tag.
6. Keep in mind that the code should be syntactically correct and should not contain any errors.
7. Keep in mind that you must generate the code fills the [INFILL] and nothing else.

Follow the instructions strictly.

For example:

User:
def calculate_sum(numbers):
    total = 0
    for num in numbers:
        [INFILL]
    return total

A:
total += num
"""

INLINE_SUGGESTION_PROMPT = PromptTemplate.from_template(
    """

LANGUAGE: {language}
CURRENT POSITION: LINE {line}, CHARACTER {character}

CODE:
{code}
"""
)

# -----------------------------------------------------------------------------


def get_inline_suggestion_from_local(
    request: InlineSuggestionRequest, *, model: str
) -> str:
    """Get inline suggestion using local LLM"""
    start_time = time.time()
    LOGGER.info(
        f"Generating local inline suggestion - Language: {request.language}, "
        f"Request ID: {request.requestID}"
    )

    llm_backend = InferenceBuilder.create()

    # Format the code
    code = ""
    for line in request.prefix:
        code += line + "\n"
    code += request.current + " [INFILL]\n"
    for line in request.suffix:
        code += line + "\n"

    LOGGER.debug(
        f"Formatted code context - Prefix: {len(request.prefix)} lines, "
        f"Current: {len(request.current)} chars, Suffix: {len(request.suffix)} lines"
    )

    # Create the chat messages
    messages = [
        {"role": "system", "content": INLINE_SYSTEM_PROMPT},
        {
            "role": "user",
            "content": INLINE_SUGGESTION_PROMPT.format(
                code=code,
                language=request.language,
                line=request.position["line"],
                character=request.position["character"],
            ),
        },
    ]

    # Generate the response
    inference_start = time.time()
    response = llm_backend.stream(model=model, messages=messages)
    content = ""
    chunk_count = 0

    for chunk in response:
        if chunk.choices[0].finish_reason is not None:
            LOGGER.debug(
                f"Generation finished with reason: {chunk.choices[0].finish_reason}"
            )
            break
        if chunk.choices[0].delta.content is not None:
            chunk_count += 1
            content += chunk.choices[0].delta.content

    inference_time = time.time() - inference_start
    total_time = time.time() - start_time

    LOGGER.info(
        f"Local suggestion generated - Length: {len(content)} chars, "
        f"Chunks: {chunk_count}, Inference time: {inference_time:.2f}s, "
        f"Total time: {total_time:.2f}s"
    )
    return content


def get_inline_suggestion_from_cloud(
    request: InlineSuggestionRequest, *, model: str
) -> str:
    """Get inline suggestion from cloud service"""
    start_time = time.time()
    LOGGER.info(
        f"Requesting cloud inline suggestion - Language: {request.language}, "
        f"Request ID: {request.requestID}"
    )

    prefix = "\n".join(request.prefix)
    suffix = "\n".join(request.suffix)

    base_url = G_BASE_URL.get().autocomplete

    try:
        api_start = time.time()
        res = requests.post(
            f"{base_url}/generate",
            headers={"Content-Type": "application/json"},
            json={
                "prefix": prefix,
                "suffix": suffix,
                "current": request.current,
                "language": request.language,
                "filepath": request.filepath,
                "position": request.position,
            },
            verify=SSL_CERT_FILE,
        )
        api_time = time.time() - api_start

        log_api_call_stats(
            f"{base_url}/generate",
            "POST",
            res.status_code,
            api_time,
            len(res.content) if res.content else 0,
        )

        if not res.ok:
            LOGGER.error(
                f"Cloud suggestion failed - Status: {res.status_code}, "
                f"Response: {res.text[:200]}..."
            )
            return ""

        body: str = res.json()
        suggestion = (
            body.replace(prefix, "", 1)
            .replace(suffix, "", 1)
            .replace(request.current, "", 1)
        )

        total_time = time.time() - start_time
        LOGGER.info(
            f"Cloud suggestion received - Length: {len(suggestion)} chars, "
            f"API time: {api_time:.2f}s, Total time: {total_time:.2f}s"
        )
        return suggestion

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error getting cloud suggestion after {total_time:.2f}s: {e}")
        return ""


def get_inline_suggestion(request: InlineSuggestionRequest, *, model: str) -> str:
    """Get inline suggestion using the appropriate method"""
    # return get_inline_suggestion_from_local(request, model=model)
    return get_inline_suggestion_from_cloud(request, model=model)


# -----------------------------------------------------------------------------


async def handle_inline_suggestion_event(
    sio: socketio.AsyncServer, sid: str, data: dict
):
    """Handle inline suggestion event"""
    start_time = time.time()
    LOGGER.info(f"Processing inline suggestion request for session {sid}")

    try:
        validation_start = time.time()
        request = InlineSuggestionRequest.model_validate(data)
        validation_time = time.time() - validation_start
        LOGGER.debug(f"Request validation completed in {validation_time:.3f}s")

    except Exception as e:
        LOGGER.error(f"Error validating inline suggestion request: {e}")
        await sio.emit(
            event="inline_suggestion:response",
            to=sid,
            data={"status": "error", "data": str(e)},
        )
        return

    response = {"status": "pending", "requestID": request.requestID, "data": None}

    try:
        suggestion_start = time.time()
        response["status"] = "success"
        response["data"] = get_inline_suggestion(
            request,
            model=ModelSelector.inline_suggestion(),
        )
        suggestion_time = time.time() - suggestion_start

        total_time = time.time() - start_time
        LOGGER.info(
            f"Inline suggestion completed - Status: {response['status']}, "
            f"Length: {len(response['data'])} chars, "
            f"Suggestion time: {suggestion_time:.2f}s, "
            f"Total time: {total_time:.2f}s"
        )

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error generating inline suggestion after {total_time:.2f}s: {e}")
        response["status"] = "error"
        response["data"] = str(e)

    await sio.emit(event="inline_suggestion:response", to=sid, data=response)


# -----------------------------------------------------------------------------

if __name__ == "__main__":
    # Test case for inline suggestion
    test_request = InlineSuggestionRequest(
        requestID="test-123",
        prefix=["def calculate_sum(numbers):", "    total = 0"],
        current="    for i, num in enumerate(numbers):",
        suffix=["    return total"],
        language="python",
        filepath="test.py",
        position={"line": 2, "character": 4},
    )

    try:
        # Test the suggestion generation
        start_time = time.time()
        LOGGER.info("Starting inline suggestion test")

        suggestion = get_inline_suggestion(
            test_request,
            model=ModelSelector.inline_suggestion(),
        )

        test_time = time.time() - start_time
        LOGGER.info(
            f"Test completed - Suggestion length: {len(suggestion)} chars, "
            f"Time: {test_time:.2f}s"
        )
        print(suggestion)

    except Exception as e:
        LOGGER.error(f"Error during testing: {str(e)}")

# -----------------------------------------------------------------------------
