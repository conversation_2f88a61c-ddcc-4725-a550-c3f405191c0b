{"info": {"name": "File Actions API", "description": "Comprehensive test collection for automated file analysis and improvements API", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:45213", "type": "string"}, {"key": "sessionId", "value": "48ece17b-605c-4c68-bb4f-fa8b662467e7", "type": "string"}], "item": [{"name": "Code Review", "item": [{"name": "Basic Code Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/utils/calculator.py\",\n  \"file_content\": \"def calculate(a, b, operation):\\n    if operation == 'add':\\n        return a + b\\n    elif operation == 'subtract':\\n        return a - b\\n    elif operation == 'multiply':\\n        return a * b\\n    elif operation == 'divide':\\n        if b == 0:\\n            raise ValueError('Cannot divide by zero')\\n        return a / b\\n    else:\\n        raise ValueError('Invalid operation')\\n\\ndef process_numbers(numbers):\\n    total = 0\\n    for num in numbers:\\n        total += num\\n    return total\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"analysis_depth\": \"standard\",\n  \"focus_areas\": [\"performance\", \"security\", \"style\"],\n  \"include_suggestions\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/review", "host": ["{{baseUrl}}"], "path": ["file-actions", "review"]}}, "response": []}, {"name": "Comprehensive Code Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/api/user_controller.js\",\n  \"file_content\": \"const express = require('express');\\nconst bcrypt = require('bcrypt');\\nconst jwt = require('jsonwebtoken');\\nconst User = require('../models/User');\\n\\nconst router = express.Router();\\n\\n// User login endpoint\\nrouter.post('/login', async (req, res) => {\\n  const { email, password } = req.body;\\n  \\n  // Find user by email\\n  const user = await User.findOne({ email: email });\\n  if (!user) {\\n    return res.status(401).json({ error: 'Invalid credentials' });\\n  }\\n  \\n  // Check password\\n  const isValid = await bcrypt.compare(password, user.password);\\n  if (!isValid) {\\n    return res.status(401).json({ error: 'Invalid credentials' });\\n  }\\n  \\n  // Generate JWT token\\n  const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);\\n  \\n  res.json({ token, user: { id: user._id, email: user.email } });\\n});\\n\\n// Get user profile\\nrouter.get('/profile/:id', async (req, res) => {\\n  const userId = req.params.id;\\n  const user = await User.findById(userId);\\n  \\n  if (!user) {\\n    return res.status(404).json({ error: 'User not found' });\\n  }\\n  \\n  res.json(user);\\n});\\n\\nmodule.exports = router;\",\n  \"language\": \"javascript\",\n  \"model\": \"gpt-4o-mini\",\n  \"analysis_depth\": \"comprehensive\",\n  \"focus_areas\": [\"security\", \"error_handling\", \"validation\"],\n  \"include_suggestions\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/review", "host": ["{{baseUrl}}"], "path": ["file-actions", "review"]}}, "response": []}]}, {"name": "Documentation Generation", "item": [{"name": "Generate Inline Documentation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/services/data_processor.py\",\n  \"file_content\": \"import pandas as pd\\nimport numpy as np\\nfrom typing import List, Dict, Optional\\n\\nclass DataProcessor:\\n    def __init__(self, config: Dict):\\n        self.config = config\\n        self.data = None\\n    \\n    def load_data(self, file_path: str) -> pd.DataFrame:\\n        if file_path.endswith('.csv'):\\n            return pd.read_csv(file_path)\\n        elif file_path.endswith('.json'):\\n            return pd.read_json(file_path)\\n        else:\\n            raise ValueError('Unsupported file format')\\n    \\n    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:\\n        df = df.dropna()\\n        df = df.drop_duplicates()\\n        return df\\n    \\n    def transform_data(self, df: pd.DataFrame, transformations: List[str]) -> pd.DataFrame:\\n        for transform in transformations:\\n            if transform == 'normalize':\\n                numeric_cols = df.select_dtypes(include=[np.number]).columns\\n                df[numeric_cols] = (df[numeric_cols] - df[numeric_cols].mean()) / df[numeric_cols].std()\\n            elif transform == 'log_transform':\\n                numeric_cols = df.select_dtypes(include=[np.number]).columns\\n                df[numeric_cols] = np.log1p(df[numeric_cols])\\n        return df\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"doc_type\": \"inline\",\n  \"include_examples\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/document", "host": ["{{baseUrl}}"], "path": ["file-actions", "document"]}}, "response": []}, {"name": "Generate API Documentation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/api/products.py\",\n  \"file_content\": \"from fastapi import APIRouter, HTTPException, Depends\\nfrom typing import List, Optional\\nfrom pydantic import BaseModel\\n\\nrouter = APIRouter()\\n\\nclass Product(BaseModel):\\n    id: int\\n    name: str\\n    price: float\\n    category: str\\n    in_stock: bool\\n\\<EMAIL>('/products')\\nasync def get_products(category: Optional[str] = None, limit: int = 10):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products/{product_id}')\\nasync def get_product(product_id: int):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products')\\nasync def create_product(product: Product):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products/{product_id}')\\nasync def update_product(product_id: int, product: Product):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products/{product_id}')\\nasync def delete_product(product_id: int):\\n    # Implementation here\\n    pass\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"doc_type\": \"api\",\n  \"include_examples\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/document", "host": ["{{baseUrl}}"], "path": ["file-actions", "document"]}}, "response": []}, {"name": "Generate API Documentation with Schemas", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/api/auth_service.py\",\n  \"file_content\": \"from flask import Flask, request, jsonify\\nfrom functools import wraps\\nimport jwt\\nimport datetime\\n\\napp = Flask(__name__)\\napp.config['SECRET_KEY'] = 'your-secret-key'\\n\\ndef token_required(f):\\n    @wraps(f)\\n    def decorated(*args, **kwargs):\\n        token = request.headers.get('Authorization')\\n        if not token:\\n            return jsonify({'message': 'Token is missing'}), 401\\n        try:\\n            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])\\n        except:\\n            return jsonify({'message': 'Token is invalid'}), 401\\n        return f(*args, **kwargs)\\n    return decorated\\n\\<EMAIL>('/api/login', methods=['POST'])\\ndef login():\\n    auth = request.get_json()\\n    if auth and auth['username'] == 'admin' and auth['password'] == 'password':\\n        token = jwt.encode({\\n            'user': auth['username'],\\n            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)\\n        }, app.config['SECRET_KEY'])\\n        return jsonify({'token': token})\\n    return jsonify({'message': 'Invalid credentials'}), 401\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"doc_type\": \"api\",\n  \"include_examples\": true,\n  \"include_schemas\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/document", "host": ["{{baseUrl}}"], "path": ["file-actions", "document"]}}, "response": []}]}, {"name": "Security Scanning", "item": [{"name": "Basic Security Scan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/auth/login.php\",\n  \"file_content\": \"<?php\\n$username = $_POST['username'];\\n$password = $_POST['password'];\\n\\n$query = \\\"SELECT * FROM users WHERE username = '$username' AND password = '$password'\\\";\\n$result = mysql_query($query);\\n\\nif (mysql_num_rows($result) > 0) {\\n    echo \\\"Login successful\\\";\\n    $_SESSION['user'] = $username;\\n} else {\\n    echo \\\"Invalid credentials\\\";\\n}\\n?>\",\n  \"language\": \"php\",\n  \"model\": \"gpt-4o-mini\",\n  \"scan_depth\": \"standard\"\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/security-scan", "host": ["{{baseUrl}}"], "path": ["file-actions", "security-scan"]}}, "response": []}, {"name": "Comprehensive Security Scan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/api/file_upload.js\",\n  \"file_content\": \"const express = require('express');\\nconst multer = require('multer');\\nconst path = require('path');\\nconst fs = require('fs');\\n\\nconst app = express();\\n\\nconst storage = multer.diskStorage({\\n  destination: function (req, file, cb) {\\n    cb(null, 'uploads/')\\n  },\\n  filename: function (req, file, cb) {\\n    cb(null, file.originalname)\\n  }\\n});\\n\\nconst upload = multer({ storage: storage });\\n\\napp.post('/upload', upload.single('file'), (req, res) => {\\n  const file = req.file;\\n  if (!file) {\\n    return res.status(400).json({ error: 'No file uploaded' });\\n  }\\n  \\n  // Execute uploaded file\\n  const filePath = path.join(__dirname, 'uploads', file.filename);\\n  require(filePath);\\n  \\n  res.json({ message: 'File uploaded and executed successfully' });\\n});\\n\\napp.listen(3000);\",\n  \"language\": \"javascript\",\n  \"model\": \"gpt-4o-mini\",\n  \"scan_depth\": \"comprehensive\"\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/security-scan", "host": ["{{baseUrl}}"], "path": ["file-actions", "security-scan"]}}, "response": []}]}, {"name": "Fix Suggestions", "item": [{"name": "Generate Fix Suggestions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/utils/data_validator.py\",\n  \"file_content\": \"def validate_email(email):\\n    if '@' in email:\\n        return True\\n    return False\\n\\ndef validate_password(password):\\n    if len(password) > 6:\\n        return True\\n    return False\\n\\ndef process_user_data(data):\\n    users = []\\n    for item in data:\\n        if validate_email(item['email']) and validate_password(item['password']):\\n            users.append(item)\\n    return users\\n\\ndef calculate_average(numbers):\\n    total = 0\\n    for num in numbers:\\n        total += num\\n    return total / len(numbers)\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"issues\": [\\n    {\\n      \\\"severity\\\": \\\"HIGH\\\",\\n      \\\"category\\\": \\\"validation\\\",\\n      \\\"line_numbers\\\": [1, 2, 3, 4],\\n      \\\"title\\\": \\\"Weak email validation\\\",\\n      \\\"description\\\": \\\"Email validation only checks for @ symbol\\\",\\n      \\\"recommendation\\\": \\\"Use proper regex or email validation library\\\"\\n    },\\n    {\\n      \\\"severity\\\": \\\"MEDIUM\\\",\\n      \\\"category\\\": \\\"error_handling\\\",\\n      \\\"line_numbers\\\": [18, 19, 20, 21],\\n      \\\"title\\\": \\\"Division by zero risk\\\",\\n      \\\"description\\\": \\\"No check for empty list in average calculation\\\",\\n      \\\"recommendation\\\": \\\"Add check for empty list before division\\\"\\n    }\\n  ],\n  \"safety_level\": \"review_required\"\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/fix-suggestions", "host": ["{{baseUrl}}"], "path": ["file-actions", "fix-suggestions"]}}, "response": []}]}, {"name": "Auto Apply Fixes", "item": [{"name": "Apply Safe Fixes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"file_path\": \"src/utils/string_utils.py\",\n  \"file_content\": \"def format_name(first, last):\\n    return first + ' ' + last\\n\\ndef clean_string(text):\\n    return text.strip().lower()\\n\\ndef validate_input(value):\\n    if value == None:\\n        return False\\n    return True\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"fixes\": [\\n    {\\n      \\\"fix_id\\\": \\\"fix_1\\\",\\n      \\\"confidence\\\": 0.95,\\n      \\\"safety_rating\\\": \\\"SAFE\\\",\\n      \\\"title\\\": \\\"Use f-string for string formatting\\\",\\n      \\\"line_changes\\\": [\\n        {\\n          \\\"line_number\\\": 2,\\n          \\\"old_code\\\": \\\"    return first + ' ' + last\\\",\\n          \\\"new_code\\\": \\\"    return f'{first} {last}'\\\",\\n          \\\"change_type\\\": \\\"MODIFY\\\"\\n        }\\n      ]\\n    },\\n    {\\n      \\\"fix_id\\\": \\\"fix_2\\\",\\n      \\\"confidence\\\": 0.9,\\n      \\\"safety_rating\\\": \\\"SAFE\\\",\\n      \\\"title\\\": \\\"Use 'is None' instead of '== None'\\\",\\n      \\\"line_changes\\\": [\\n        {\\n          \\\"line_number\\\": 7,\\n          \\\"old_code\\\": \\\"    if value == None:\\\",\\n          \\\"new_code\\\": \\\"    if value is None:\\\",\\n          \\\"change_type\\\": \\\"MODIFY\\\"\\n        }\\n      ]\\n    }\\n  ],\n  \"safety_level\": \"safe_only\",\n  \"create_backup\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/apply-fixes", "host": ["{{baseUrl}}"], "path": ["file-actions", "apply-fixes"]}}, "response": []}]}, {"name": "Batch Operations", "item": [{"name": "Batch File Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"operations\": [\n    {\n      \"operation\": \"review\",\n      \"file_path\": \"src/models/user.py\",\n      \"file_content\": \"class User:\\n    def __init__(self, name, email):\\n        self.name = name\\n        self.email = email\\n    \\n    def get_info(self):\\n        return self.name + ' - ' + self.email\",\n      \"language\": \"python\",\n      \"model\": \"gpt-4o-mini\"\n    },\n    {\n      \"operation\": \"security-scan\",\n      \"file_path\": \"src/auth/validator.js\",\n      \"file_content\": \"function validateUser(input) {\\n  eval(input.code);\\n  return true;\\n}\",\n      \"language\": \"javascript\",\n      \"model\": \"gpt-4o-mini\"\n    }\n  ],\n  \"parallel_processing\": true,\n  \"stop_on_error\": false\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/batch", "host": ["{{baseUrl}}"], "path": ["file-actions", "batch"]}}, "response": []}]}, {"name": "Queue Management", "item": [{"name": "Add to Queue", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"review\",\n  \"file_path\": \"src/services/payment.py\",\n  \"file_content\": \"import requests\\n\\ndef process_payment(amount, card_number):\\n    # Send payment to external API\\n    response = requests.post('https://api.payment.com/charge', {\\n        'amount': amount,\\n        'card': card_number\\n    })\\n    return response.json()\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"priority\": \"HIGH\",\n  \"max_retries\": 3\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/queue", "host": ["{{baseUrl}}"], "path": ["file-actions", "queue"]}}, "response": []}, {"name": "Get Queue Item Status", "request": {"method": "GET", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/queue/status/{{queueItemId}}", "host": ["{{baseUrl}}"], "path": ["file-actions", "queue", "status", "{{queueItemId}}"]}}, "response": []}, {"name": "Get Queue Stats", "request": {"method": "GET", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/queue/stats", "host": ["{{baseUrl}}"], "path": ["file-actions", "queue", "stats"]}}, "response": []}]}, {"name": "Safety & Rollback", "item": [{"name": "Get Change History", "request": {"method": "GET", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/changes?limit=20", "host": ["{{baseUrl}}"], "path": ["file-actions", "changes"], "query": [{"key": "limit", "value": "20"}]}}, "response": []}, {"name": "Get Audit Log", "request": {"method": "GET", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/audit?limit=50", "host": ["{{baseUrl}}"], "path": ["file-actions", "audit"], "query": [{"key": "limit", "value": "50"}]}}, "response": []}, {"name": "Rollback Change", "request": {"method": "POST", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/rollback/change/{{changeId}}", "host": ["{{baseUrl}}"], "path": ["file-actions", "rollback", "change", "{{changeId}}"]}}, "response": []}, {"name": "Rollback Operation", "request": {"method": "POST", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/rollback/operation/{{operationId}}", "host": ["{{baseUrl}}"], "path": ["file-actions", "rollback", "operation", "{{operationId}}"]}}, "response": []}]}, {"name": "Analytics & Stats", "item": [{"name": "Get Analysis Statistics", "request": {"method": "GET", "header": [{"key": "x-session", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/file-actions/stats", "host": ["{{baseUrl}}"], "path": ["file-actions", "stats"]}}, "response": []}, {"name": "Cleanup Old Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-session", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"days_to_keep\": 30,\n  \"cleanup_backups\": true,\n  \"cleanup_analysis_records\": true\n}"}, "url": {"raw": "{{baseUrl}}/file-actions/cleanup", "host": ["{{baseUrl}}"], "path": ["file-actions", "cleanup"]}}, "response": []}]}]}