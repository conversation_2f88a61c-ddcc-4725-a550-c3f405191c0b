import os
import time

import psutil

from . import LOGGER


def log_memory_usage(stage: str):
    """Log current memory usage"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        LOGGER.debug(f"Memory usage at {stage}: {memory_mb:.1f} MB")
        return memory_mb
    except Exception as e:
        LOGGER.warning(f"Failed to get memory usage at {stage}: {e}")
        return None


def log_file_stats(file_path: str):
    """Log file statistics"""
    try:
        if os.path.isfile(file_path):
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / 1024 / 1024
            LOGGER.debug(f"File stats - Path: {file_path}, Size: {file_size_mb:.2f} MB")
            return file_size
        else:
            LOGGER.debug(f"File stats - Path: {file_path} is not a regular file")
            return 0
    except Exception as e:
        LOGGER.warning(f"Failed to get file stats for {file_path}: {e}")
        return 0


def log_execution_time(func_name: str, start_time: float):
    """Log execution time for a function"""
    end_time = time.time()
    execution_time = end_time - start_time
    LOGGER.info(f"Function '{func_name}' completed in {execution_time:.2f} seconds")
    return execution_time


def log_api_call_stats(
    url: str,
    method: str,
    status_code: int,
    response_time: float,
    response_size: int = 0,
):
    """Log API call statistics"""
    LOGGER.debug(
        f"API call - Method: {method}, URL: {url}, Status: {status_code}, "
        f"Time: {response_time:.2f}s, Size: {response_size} bytes"
    )


def log_system_info():
    """Log current system information"""
    try:
        cpu_count = os.cpu_count()
        memory_total = psutil.virtual_memory().total / 1024 / 1024 / 1024  # GB
        disk_usage = psutil.disk_usage("/").free / 1024 / 1024 / 1024  # GB
        LOGGER.info(
            f"System info - CPUs: {cpu_count}, RAM: {memory_total:.1f}GB, "
            f"Free disk: {disk_usage:.1f}GB"
        )
        return {
            "cpu_count": cpu_count,
            "memory_total_gb": memory_total,
            "disk_free_gb": disk_usage,
        }
    except Exception as e:
        LOGGER.warning(f"Failed to get system info: {e}")
        return None


def log_operation_stats(
    operation_name: str,
    start_time: float,
    item_count: int = 0,
    data_size: int = 0,
    success: bool = True,
):
    """Log comprehensive operation statistics"""
    end_time = time.time()
    duration = end_time - start_time

    status = "completed" if success else "failed"
    stats_msg = f"Operation '{operation_name}' {status} in {duration:.2f}s"

    if item_count > 0:
        rate = item_count / duration if duration > 0 else 0
        stats_msg += f" - Items: {item_count} (rate: {rate:.1f}/s)"

    if data_size > 0:
        data_mb = data_size / 1024 / 1024
        throughput = data_mb / duration if duration > 0 else 0
        stats_msg += f" - Data: {data_mb:.2f} MB (throughput: {throughput:.2f} MB/s)"

    if success:
        LOGGER.info(stats_msg)
    else:
        LOGGER.error(stats_msg)

    return {
        "duration": duration,
        "success": success,
        "item_count": item_count,
        "data_size": data_size,
        "rate": item_count / duration if duration > 0 and item_count > 0 else 0,
        "throughput": (
            (data_size / 1024 / 1024) / duration
            if duration > 0 and data_size > 0
            else 0
        ),
    }
