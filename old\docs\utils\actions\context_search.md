# Context Search (`context_search.py`)

This module handles the `context_search` action, which performs a semantic search for a given query within a specified knowledge base (KB). It's a core component for retrieving contextual information.

The module can perform the search in two ways:

1.  **Local Search:** Directly queries a local Qdrant vector database.
2.  **Remote Search:** Forwards the query to a cloud-based search service if the knowledge base is configured for cloud sync.

## Functions

### `process_context_search(*, query: str, tool_id: str, kbid: str, search_references: SearchReferences)`

This is the main entry point for the context search action.

- **Parameters:**
  - `query` (str): The search query.
  - `tool_id` (str): The unique identifier for this tool call.
  - `kbid` (str): The ID of the knowledge base to search in.
  - `search_references` (SearchReferences): An object to track the search results.
- **Returns:**
  - A tuple containing:
    - `list[dict]`: A list of formatted messages ready to be sent to the language model.
    - `SearchReferences`: The updated search references object.

It orchestrates the search by calling `_perform_local_search`, processes the results, adds them to `search_references`, and formats the output using `create_action_messages` from `actions.utils`.

### `_perform_local_search(...)`

This function determines whether to perform a local or remote search based on the knowledge base's configuration.

- It first retrieves the KB's metadata to check if it's synced with the cloud (`on_cloud`).
- If `on_cloud` is `True`, it calls `_perform_remote_search`.
- Otherwise, it calls `_perform_qdrant_search` to query the local database.

### `_perform_qdrant_search(...)`

This function executes a vector search against the local Qdrant database.

- It gets an embedding for the search query using the `EmbeddingInferenceBuilder`.
- It performs the search on the specified Qdrant collection (`index_name`).
- It formats the raw Qdrant results into a standardized dictionary structure.

### `_perform_remote_search(...)`

This function sends the search query to the remote CodeMate AI search service.

- It makes a POST request to the `/context_search` endpoint.
- It includes the session ID in the headers for authentication.
- It formats the JSON response from the service into the same standardized dictionary structure as the local search.
