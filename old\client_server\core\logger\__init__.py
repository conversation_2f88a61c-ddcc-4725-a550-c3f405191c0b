import httpx
from loguru import logger as LOGGER

from client_server.core import constants
from client_server.utils.path_selector import PathSelector


def _report_to_cloud(_):
    try:
        with httpx.Client() as client:
            response = client.post(f"http://localhost:{constants.PORT}/report_to_cloud")
            return response.json()
    except Exception as e:
        # Silently fail to avoid logging loops
        pass


# Configure logger to write to both console and file
LOGGER.remove()
# Add console output with debug level only
# LOGGER.add(sys.stderr, level="ERROR")
# Add file output with INFO level (excludes debug)
LOGGER.add(
    PathSelector.get_logs_path() / "logs.log",
    rotation="10 MB",
    retention="10 days",
    backtrace=True,
    diagnose=True,
    encoding="utf-8",
    level="INFO",  # File output will only show INFO and above
    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
)

LOGGER.add(
    lambda message: _report_to_cloud(message),
    level="ERROR",
)
