import os
import tarfile
import zipfile
from pathlib import Path
from typing import Generator, <PERSON><PERSON>, Any

import requests
from overrides import override

from client_server.core.logger import LOGGER
from client_server.utils.platform_detector import PlatformDetector
from . import IDependency, DependencyStatus
from .registry import DependencyRegistry

# -----------------------------------------------------------------------------


class OllamaDependency(IDependency):
    def __init__(
        self,
        target_dir: Path,
        *,
        id: str | None = None,
        name: str | None = None,
        version: str | None = None,
        description: str | None = None,
    ):
        super().__init__()
        self.target_dir = target_dir
        # Store properties
        self._id = id or "ollama"
        self._name = name or "Ollama (Language Model Server)"
        self._version = version or "0.6.7"
        self._description = (
            description
            or "Ollama is a lightweight, fast, and easy-to-use language model server."
        )

    @property
    @override
    def id(self) -> str:
        return self._id

    @property
    @override
    def name(self) -> str:
        return self._name

    @property
    @override
    def version(self) -> str:
        return self._version

    @property
    @override
    def description(self) -> str:
        return self._description

    def _get_download_url(self) -> str:
        """Get the appropriate Ollama download URL based on system architecture."""
        os = PlatformDetector.get_os_name()
        arch = "arm64" if PlatformDetector.is_arm() else "amd64"

        match os:
            case "windows":
                return f"https://github.com/ollama/ollama/releases/download/v{self.version}/ollama-windows-{arch}.zip"
            case "linux":
                return f"https://github.com/ollama/ollama/releases/download/v{self.version}/ollama-linux-{arch}.tgz"
            case "darwin":  # macOS
                return f"https://github.com/ollama/ollama/releases/download/v{self.version}/ollama-darwin.tgz"
            case _:
                raise NotImplementedError(f"Unsupported system: {os}")

    def _get_binary_name(self) -> str:
        """Get the appropriate binary name based on the system."""
        os = PlatformDetector.get_os_name()
        match os:
            case "windows":
                return "ollama.exe"
            case "darwin":  # macOS
                return "ollama"
            case "linux":
                return "ollama"
            case _:
                raise NotImplementedError(f"Unsupported system: {os}")

    def _setup_ollama(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Download and extract Ollama to the target directory."""
        self.target_dir.mkdir(parents=True, exist_ok=True)
        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Creating target directory"

        download_url = self._get_download_url()
        archive_path = self.target_dir / os.path.basename(download_url)

        # Download the file with progress bar
        status = DependencyStatus.INSTALLING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, f"Downloading Ollama from {download_url}"
        response = requests.get(download_url, stream=True)
        response.raise_for_status()

        total_size = int(response.headers.get("content-length", 0))
        block_size = 8192
        downloaded_size = 0

        with open(archive_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=block_size):
                size = f.write(chunk)
                downloaded_size += size
                progress = (downloaded_size / total_size) * 100
                yield status, progress

        # Extract the archive
        yield status, "Extracting Ollama archive"
        if download_url.endswith(".zip"):
            with zipfile.ZipFile(archive_path, "r") as zip_ref:
                file_list = zip_ref.namelist()
                for i, file in enumerate(file_list):
                    zip_ref.extract(file, self.target_dir)
                    progress = ((i + 1) / len(file_list)) * 100
                    yield status, progress
        else:  # .tgz
            with tarfile.open(archive_path, "r:gz") as tar_ref:
                members = tar_ref.getmembers()
                for i, member in enumerate(members):
                    tar_ref.extract(member, self.target_dir)
                    progress = ((i + 1) / len(members)) * 100
                    yield status, progress

        # Make binary executable on Unix-like systems
        if PlatformDetector.is_linux() or PlatformDetector.is_darwin():
            binary_path = self.target_dir / self._get_binary_name()
            if PlatformDetector.is_linux():
                binary_path.chmod(0o755)
                yield status, "Setting executable permissions"

        # Clean up the archive
        archive_path.unlink()
        status = DependencyStatus.INSTALLED
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Ollama installation complete"

    @override
    def get_status(self) -> DependencyStatus:
        """Get the current status of Ollama."""
        # Use DependencyRegistry to get the stored status
        status_str = DependencyRegistry.get_status(self._id)

        # If status is None, determine it and update
        if status_str is None:
            binary_path = self.target_dir / self._get_binary_name()
            if not binary_path.exists():
                status = DependencyStatus.MISSING
            else:
                status = DependencyStatus.READY

            # Update the status in the registry
            DependencyRegistry.update_status(self._id, status.value)
            return status
        else:
            # Convert string status to enum
            return DependencyStatus(status_str)

    @override
    def install(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Install Ollama."""
        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Starting Ollama installation"

        try:
            yield from self._setup_ollama()
            status = DependencyStatus.INSTALLED
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama installation complete"

            status = DependencyStatus.READY
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama is ready to use"
        except Exception as e:
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to install Ollama: {e}"

    @override
    def uninstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Uninstall Ollama."""
        if not self.target_dir.exists():
            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama is not installed"
            return

        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Removing Ollama installation"

        try:
            import shutil

            shutil.rmtree(self.target_dir)
            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "Ollama has been uninstalled"
        except Exception as e:
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to uninstall Ollama: {e}"

    @override
    def reinstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """Reinstall Ollama by first uninstalling and then installing."""
        yield from self.uninstall()
        yield from self.install()

    @override
    def ensure(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Ensure Ollama is installed and ready.
        First reports the current status, then handles installation if needed.
        Yields status updates during the process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """

        match self.get_status():
            case DependencyStatus.INSTALLED:
                yield DependencyStatus.INSTALLED, "Ollama is already installed and ready"
                return
            case DependencyStatus.MISSING | DependencyStatus.ERROR:
                yield DependencyStatus.MISSING, "Ollama is not installed"
                yield from self.install()
            case DependencyStatus.CORRUPTED:
                yield DependencyStatus.CORRUPTED, "Ollama installation is corrupted"
                yield from self.reinstall()


# -----------------------------------------------------------------------------
