# Model Selector (`model_selector.py`)

The `model_selector.py` module provides a mechanism to dynamically select the appropriate AI models for different tasks based on the underlying hardware platform. This allows the application to optimize performance by using different models on different architectures (e.g., standard x86 vs. Snapdragon ARM).

## Classes

### `ModelSelector`

A class containing static methods for model selection.

#### `chat() -> str`

This static method returns the identifier for the chat model that should be used.

- **Returns:**
  - `str`: The model identifier string.
    - Returns `"openai/chat"` if the platform is Snapdragon ARM.
    - Returns `"qwen3:4b"` for other platforms.

#### `inline_suggestion() -> str`

This static method returns the identifier for the inline code suggestion (autocomplete) model.

- **Returns:**
  - `str`: The model identifier string.
    - Returns `"openai/autocomplete"` if the platform is Snapdragon ARM.
    - Returns `"qwen3:4b"` for other platforms.
