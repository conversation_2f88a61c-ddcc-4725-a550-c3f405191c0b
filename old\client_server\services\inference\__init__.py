from abc import ABC, abstractmethod
from typing import Any

from litellm.types.utils import ModelResponse
from litellm.litellm_core_utils.streaming_handler import CustomStreamWrapper


class IInferenceBackend(ABC):
    """
    Interface defining the contract for inference backend implementations.
    All inference backends (Ollama, etc.) should implement this interface.
    """

    @abstractmethod
    def generate(
        self, model: str, messages: list[dict[str, str]], **model_params
    ) -> ModelResponse | None:
        """
        Generate completion using specified model and chat messages.

        Args:
            model: The name of the model to use
            messages: List of message dictionaries with 'role' and 'content' keys
            **model_params: Additional parameters to pass to the model (temperature, top_p, etc.)

        Returns:
            str: The generated completion text
        """
        pass

    @abstractmethod
    def stream(
        self, model: str, messages: list[dict[str, str]], **model_params
    ) -> CustomStreamWrapper:
        """
        Stream completion using specified model and chat messages.
        Yields chunks of the response as they become available.

        Args:
            model: The name of the model to use
            messages: List of message dictionaries with 'role' and 'content' keys
            **model_params: Additional parameters to pass to the model (temperature, top_p, etc.)

        Yields:
            str: Chunks of the generated completion text
        """
        pass

    @abstractmethod
    def ensure_model(self, model: str) -> Any:
        """
        Ensure that model is loaded and ready for inference.
        """
        pass

    @abstractmethod
    def dispose(self) -> None:
        """
        Dispose of all loaded models and cleanup resources.
        """
        pass

    @abstractmethod
    def __enter__(self):
        """Context manager entry"""
        pass

    @abstractmethod
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        pass
