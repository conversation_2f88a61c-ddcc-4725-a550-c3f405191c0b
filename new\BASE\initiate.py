# from ipc import IPC
# from BASE.utils.platform_detector import PlatformDetector
# from BASE.vdb.qdrant import get_qdrant_client
# from BASE.vdb.download import main as download_qdrant

# """
# We create an IPC instance for shared memory communication.
# Rather than using events (for now), we use a shared pool to manage states.
# We also initialize the global Qdrant client for vector database operations.
# """
# ipc_ = IPC.connect()
# ipc_.set("platform", PlatformDetector.get_os_name())
# ipc_.set("ollama_running", False)
# ipc_.set("internet_connected", False)
# ipc_.set("cuda_available", False)



# # Set ollama-state
# import socket
# def ollama_running(port: int = 11434) -> bool:
#     """Check if the specified port is available."""
#     try:
#         with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
#             s.bind(("localhost", port))
#             return ipc_.set("ollama_running", True)
#     except socket.error:
#         ipc_.set("ollama_running", False)

# ollama_running()



# # qdrant_downloaded
# ipc_.set("qdrant_downloaded", False)
# qdrant_download_stat = download_qdrant()
# if qdrant_download_stat == True:
#     ipc_.set("qdrant_downloaded", True)
# else:
#     ipc_.set("qdrant_downloaded", False)



# # Initialize Qdrant client
# try:
#     from BASE.vdb.qdrant import start_qdrant_server
#     qdrant_running = start_qdrant_server()
#     if qdrant_running == True:
#         ipc_.set("qdrant_ready", True)
#     else:
#         ipc_.set("qdrant_ready", False)
#     from BASE.vdb.qdrant import get_qdrant_client
#     qdrant_client = get_qdrant_client()
#     ipc_.set("qdrant_connected", True)
# except Exception as e:
#     ipc_.set("qdrant_connected", False)



# # Check if CUDA is available
# import BASE.utils.check_cuda # checks if CUDA is available



from ipc import IPC
from BASE.utils.platform_detector import PlatformDetector
from BASE.vdb.qdrant import get_qdrant_client
from BASE.vdb.download import main as download_qdrant
import socket
import subprocess

"""
We create an IPC instance for shared memory communication.
Rather than using events (for now), we use a shared pool to manage states.
We also initialize the global Qdrant client for vector database operations.
"""
ipc_ = IPC.connect()
ipc_.set("platform", PlatformDetector.get_os_name())
ipc_.set("ollama_running", False)
ipc_.set("internet_connected", False)
ipc_.set("cuda_available", False)

# Set ollama-state
def ollama_running(port: int = 11434) -> bool:
    """Check if the specified port is available."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(("localhost", port))
            return ipc_.set("ollama_running", True)
    except socket.error:
        ipc_.set("ollama_running", False)

ollama_running()

# qdrant_downloaded
ipc_.set("qdrant_downloaded", False)
qdrant_download_stat = download_qdrant()
if qdrant_download_stat == True:
    ipc_.set("qdrant_downloaded", True)
else:
    ipc_.set("qdrant_downloaded", False)

# Initialize Qdrant client
try:
    import requests
    from BASE.vdb.qdrant import start_qdrant_server
    if requests.get("http://localhost:45215").status_code != 200:
        qdrant_running = start_qdrant_server()
        if qdrant_running == True:
            ipc_.set("qdrant_ready", True)
        else:
            ipc_.set("qdrant_ready", False)
    else:
        ipc_.set("qdrant_ready", True)
        
    from BASE.vdb.qdrant import get_qdrant_client
    qdrant_client = get_qdrant_client()
    ipc_.set("qdrant_connected", True)
except Exception as e:
    ipc_.set("qdrant_connected", False)

# Check if CUDA is available
import BASE.utils.check_cuda # checks if CUDA is available

# Start and detach subprocesses for http_server.py and websocket_server.py
def start_detached_process(script_name):
    import sys
    subprocess.Popen([sys.executable, script_name], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, stdin=subprocess.DEVNULL, close_fds=True)

start_detached_process('http_server.py')
start_detached_process('websocket_server.py')
