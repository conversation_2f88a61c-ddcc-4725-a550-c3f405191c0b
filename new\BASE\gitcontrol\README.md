# GitControl

A comprehensive Git repository management system for Python applications. GitControl provides a clean API for managing multiple Git repositories, creating pull requests, and handling authentication across different Git providers.

## Features

- **Repository Discovery**: Automatically find and list all Git repositories in specified directories
- **Clone Management**: Clone repositories with branch selection and authentication support
- **Pull Request Creation**: Create pull requests on GitHub and GitLab programmatically
- **Authentication**: Secure token management for GitHub, GitLab, and Bitbucket
- **Repository Status**: Get detailed status information for any Git repository
- **Sync Operations**: Automatically sync repositories with remotes (fetch, pull, push)
- **Configuration Management**: Easy configuration through `.gitcontrol-config` file

## Installation

Add the gitcontrol directory to your Python path or install it:

```bash
# Option 1: Add to your project
cp -r BASE/gitcontrol /path/to/your/project/

# Option 2: Install as package
cd BASE/gitcontrol
pip install -e .
```

### Dependencies

- Python 3.7+
- Git
- requests library

## Quick Start

### Basic Usage

```python
from gitcontrol import GitControl

# Create GitControl instance
gc = GitControl()

# List all repositories in your configured directories
repos = gc.list_git_repositories()
for repo in repos:
    print(f"Found: {repo['name']} at {repo['path']}")

# Clone a repository
success = gc.clone_repository(
    "https://github.com/user/repo.git",
    destination="./my-project",
    branch="main"
)

# Get repository status
status = gc.get_repository_status("./my-project")
print(f"Current branch: {status['current_branch']}")
print(f"Has changes: {status['has_changes']}")

# Create a pull request
pr_success = gc.create_pull_request(
    repo_path="./my-project",
    title="Fix critical bug",
    body="This PR fixes the authentication issue found in production",
    base_branch="main",
    head_branch="bugfix"
)
```

### Configuration Setup

```python
from gitcontrol import GitControlConfig

# Set up configuration
config = GitControlConfig()

# Add authentication tokens
config.set_token("github", "ghp_your_github_token")
config.set_token("gitlab", "glpat_your_gitlab_token")

# Configure search directories
config.add_git_directory("/home/<USER>/projects")
config.add_git_directory("/home/<USER>/work")

# Set default branch
config.set_default_branch("main")
```

## Configuration

GitControl uses a configuration file `~/.gitcontrol-config` to store settings and tokens.

### Configuration Options

- `tokens.github`: GitHub personal access token
- `tokens.gitlab`: GitLab personal access token
- `tokens.bitbucket`: Bitbucket app password
- `default_branch`: Default branch name (default: "main")
- `git_directories`: List of directories to search for repositories
- `users.github.username`: GitHub username
- `users.gitlab.username`: GitLab username

### Example Configuration

```json
{
  "tokens": {
    "github": "ghp_xxxxxxxxxxxxxxxxxxxx",
    "gitlab": "glpat-xxxxxxxxxxxxxxxxxxxx"
  },
  "default_branch": "main",
  "git_directories": [
    "/home/<USER>/projects",
    "/home/<USER>/work"
  ],
  "users": {
    "github": {
      "username": "myusername",
      "email": "<EMAIL>"
    }
  }
}
```

## Advanced Usage Examples

### Repository Management

```python
from gitcontrol import GitControl, GitControlError

gc = GitControl()

# Search for repositories in specific directories
repos = gc.list_git_repositories(["/home/<USER>/projects", "/home/<USER>/work"])

# Filter repositories by status
dirty_repos = [repo for repo in repos if repo['status'] == 'dirty']
print(f"Found {len(dirty_repos)} repositories with uncommitted changes")

# Clone multiple repositories
repo_urls = [
    "https://github.com/user/repo1.git",
    "https://github.com/user/repo2.git"
]

for url in repo_urls:
    try:
        success = gc.clone_repository(url, branch="main")
        if success:
            print(f"✅ Cloned {url}")
        else:
            print(f"❌ Failed to clone {url}")
    except GitControlError as e:
        print(f"Error: {e}")
```

### Batch Operations

```python
# Sync multiple repositories
def sync_all_repos():
    gc = GitControl()
    repos = gc.list_git_repositories()
    
    for repo in repos:
        print(f"Syncing {repo['name']}...")
        try:
            gc.sync_repository(repo['path'], auto_pull=True)
        except GitControlError as e:
            print(f"Failed to sync {repo['name']}: {e}")

# Create PRs for feature branches
def create_feature_prs():
    gc = GitControl()
    repos = gc.list_git_repositories()
    
    for repo in repos:
        status = gc.get_repository_status(repo['path'])
        if status['current_branch'].startswith('feature/'):
            success = gc.create_pull_request(
                repo_path=repo['path'],
                title=f"Feature: {status['current_branch'].replace('feature/', '')}",
                body="Auto-generated PR for feature branch"
            )
            if success:
                print(f"✅ Created PR for {repo['name']}")
```

## Supported Git Providers

- **GitHub**: Full support for repository operations and pull requests
- **GitLab**: Full support for repository operations and merge requests
- **Bitbucket**: Basic repository operations (pull request support planned)

## Authentication

### GitHub

1. Go to GitHub Settings > Developer settings > Personal access tokens
2. Generate a new token with appropriate permissions:
   - `repo` - for private repositories
   - `public_repo` - for public repositories
   - `workflow` - if you need to trigger actions
3. Set the token: `gitcontrol config token github YOUR_TOKEN`

### GitLab

1. Go to GitLab Settings > Access Tokens
2. Create a personal access token with scopes:
   - `api` - for full API access
   - `read_repository` - for reading repositories
   - `write_repository` - for writing to repositories
3. Set the token: `gitcontrol config token gitlab YOUR_TOKEN`

## Command Reference

### `gitcontrol list [paths...]`
List all Git repositories in specified paths or configured directories.

### `gitcontrol clone <url> [options]`
Clone a Git repository.
- `--destination, -d`: Destination directory
- `--branch, -b`: Branch to clone
- `--token, -t`: Authentication token

### `gitcontrol pr <title> [options]`
Create a pull request.
- `--body, -b`: Pull request description
- `--path, -p`: Repository path
- `--base`: Base branch
- `--head`: Head branch

### `gitcontrol status [options]`
Get repository status.
- `--path, -p`: Repository path

### `gitcontrol sync [options]`
Sync repository with remote.
- `--path, -p`: Repository path
- `--no-pull`: Skip automatic pull
- `--push`: Automatically push commits

### `gitcontrol config <action> [options]`
Manage configuration.
- `set <key> <value>`: Set configuration value
- `get <key>`: Get configuration value
- `token <provider> <token>`: Set authentication token

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## Roadmap

- [ ] Bitbucket pull request support
- [ ] Git hooks management
- [ ] Repository templates
- [ ] Bulk operations across multiple repositories
- [ ] Integration with CI/CD systems
- [ ] Web interface
- [ ] Repository analytics and reporting
