# Connection Events (`connection.py`)

The `connection.py` module manages the lifecycle of WebSocket connections between the client and the server. It handles initial connections, disconnections, and reconnections, ensuring a stable and resilient communication channel.

## Overview

This module is central to session management. It maintains a state for each connected client, allowing for features like session recovery after a temporary disconnection.

## Event Handlers

- `handle_connect_event(sio, sid, environ)`:

  - Triggered when a new client establishes a WebSocket connection.
  - It initializes a session state for the client, storing information like the connection time.
  - It checks if the connection is a reconnection attempt and, if so, triggers the recovery logic.
  - Emits a `connection_confirmed` event back to the client.

- `handle_disconnect_event(sio, sid, _)`:
  - Triggered when a client disconnects.
  - It preserves the session state for a configurable period, allowing the client to reconnect and resume their session.
  - It cancels any pending background tasks associated with the disconnected session to free up resources.

## Session Management

- `_session_state`: A dictionary that stores the state of all active and recently disconnected sessions. This is key to the reconnection logic.

- `handle_reconnection_recovery(sio, sid)`:

  - If a client reconnects within the timeout period, this function is called.
  - It restores the previous session state and notifies the client that the session has been successfully recovered by emitting a `reconnection_status` event.

- `cleanup_old_sessions()`:

  - A background task that runs periodically.
  - It iterates through the `_session_state` and removes stale sessions that have been disconnected for too long, preventing memory leaks.

- `update_session_activity(sid)`:

  - A utility function called by other event handlers (like `chat`) to update the `last_activity` timestamp for a session. This helps in determining which sessions are active and which can be cleaned up.

- `get_session_stats()`:
  - Provides statistics about the current state of connections, such as the number of active and total sessions.

## Event Flow

This diagram shows the lifecycle of a client connection, including disconnection and reconnection.

<details>
<summary>View Connection Lifecycle Flow</summary>

```mermaid
sequenceDiagram
    participant E as User's Editor
    participant S as Our Server

    E->>S: Editor connects
    S->>S: Acknowledge connection
    S->>S: Create a new session for the user
    S-->>E: Send "Connection successful"

    E-)S: Editor disconnects (e.g., internet problem)
    S->>S: Notice disconnection
    S->>S: Keep the user's session active for a short time

    E->>S: Editor reconnects
    S->>S: Recognize the user
    S->>S: Restore the previous session
    S-->>E: Send "Welcome back!"
```

</details>
