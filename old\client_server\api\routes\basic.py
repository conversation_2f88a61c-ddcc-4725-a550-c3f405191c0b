import time
from fastapi import Response, Request

from client_server.core.logger import LOGGER
from . import route


@route("GET", "/")
async def health(request: Request = None):
    """Health check endpoint"""
    start_time = time.time()
    LOGGER.info(
        f"Health check request received from {request.client.host if request else 'unknown'}"
    )

    response = Response(content="OK", media_type="text/plain")

    total_time = time.time() - start_time
    LOGGER.info(f"Health check completed in {total_time:.3f}s")

    return response
