from .platform_detector import PlatformDetector


class ModelSelector:
    @staticmethod
    def chat() -> str:
        if PlatformDetector.is_snapdragon_arm():
            return "openai/chat"
        else:
            return "qwen3:4b"

    @staticmethod
    def inline_suggestion() -> str:
        if PlatformDetector.is_snapdragon_arm():
            return "openai/autocomplete"
        else:
            return "qwen3:4b"
