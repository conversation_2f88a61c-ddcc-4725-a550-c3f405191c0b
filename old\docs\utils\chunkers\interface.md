# The IChunker Interface (`__init__.py`)

The `__init__.py` file in the `chunkers` package defines the abstract base class (ABC) that serves as the contract for all chunker implementations. This ensures that every chunker, regardless of the data source it handles, provides a consistent interface to the rest of the application.

## Classes

### `IChunker(ABC)`

This is the abstract base class for all chunkers.

#### `process(self, progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None) -> list[QdrantKnowledgeBaseChunk]`

This is the core abstract method that all concrete chunker classes must implement. It defines the main entry point for the chunking logic.

- **Parameters:**
  - `progress_callback` (Optional): An optional asynchronous function that can be called by the chunker to report its progress as a float between 0 and 100. This is used to provide real-time feedback to the user during a long-running chunking operation.
- **Returns:**
  - `list[QdrantKnowledgeBaseChunk]`: A list of `QdrantKnowledgeBaseChunk` objects. Each object represents a single chunk of text and its associated metadata.
