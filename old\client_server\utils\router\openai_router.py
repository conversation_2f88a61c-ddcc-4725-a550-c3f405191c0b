"""
OpenAI Client Utility for CodeLens API

This module provides function-based utilities for creating OpenAI clients
with BYOK (Bring Your Own Key) configuration and session-based authentication.
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.utils.security import validate_and_log_api_keys, APIKeyValidator

# OpenAI client import
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    LOGGER.warning("OpenAI package not available. OpenAI integration will be disabled.")

# Initialize environment
load_dotenv()


def _load_settings() -> dict:
    """Load settings from ~/settings.json"""
    return PathSelector._load_settings()


def _get_byok_openai_config() -> Optional[Dict[str, Any]]:
    """Extract OpenAI BYOK configuration from settings"""
    settings = _load_settings()
    byok_config = settings.get('byok', {})
    
    if not byok_config or not byok_config.get('enabled', False):
        LOGGER.debug("BYOK not enabled or configured")
        return None
        
    # Check if OpenAI API key is present
    api_keys = byok_config.get('api_keys', {})
    if not api_keys.get('openai'):
        LOGGER.debug("No OpenAI API key found in BYOK configuration")
        return None
        
    LOGGER.info("OpenAI BYOK configuration found and enabled")
    return {
        'api_key': api_keys['openai'],
        'base_url': byok_config.get('openai_base_url')  # Optional custom base URL
    }


def create_openai_client(session: str, base_url: Optional[str] = None) -> Optional[OpenAI]:
    """
    Create OpenAI client instance with session as API key.

    Args:
        session: Session ID to use as API key
        base_url: Custom base URL for OpenAI API

    Returns:
        OpenAI client instance or None if OpenAI package not available
    """
    if not OPENAI_AVAILABLE:
        LOGGER.error("OpenAI package not available. Cannot create OpenAI client.")
        return None

    if not session:
        LOGGER.error("Session ID is required for OpenAI client")
        return None

    try:
        # Use custom base_url if provided, otherwise use default
        client_kwargs = {
            "api_key": session
        }

        if base_url:
            client_kwargs["base_url"] = base_url
            LOGGER.info(f"Using custom base URL: {base_url}")

        LOGGER.info(f"Creating OpenAI client with kwargs: {list(client_kwargs.keys())}")
        client = OpenAI(**client_kwargs)

        # Log with masked session for security
        # masked_session = APIKeyValidator.mask_api_key(session)
        LOGGER.info(f"Successfully created OpenAI client with session authentication: {session}, base_url: {base_url}")
        return client

    except Exception as e:
        LOGGER.error(f"Error creating OpenAI client: {e}")
        LOGGER.error(f"Client kwargs were: {list(client_kwargs.keys()) if 'client_kwargs' in locals() else 'Not set'}")
        return None


def create_byok_openai_client() -> Optional[OpenAI]:
    """
    Create OpenAI client using BYOK configuration.

    Returns:
        OpenAI client instance or None if BYOK not configured or OpenAI package not available
    """
    if not OPENAI_AVAILABLE:
        LOGGER.error("OpenAI package not available. Cannot create OpenAI client.")
        return None

    try:
        byok_config = _get_byok_openai_config()
        if not byok_config:
            LOGGER.debug("No BYOK OpenAI configuration available")
            return None

        api_key = byok_config['api_key']
        base_url = byok_config.get('base_url')

        # Validate API key using security utilities
        validation_result = APIKeyValidator.validate_api_key_format('openai', api_key)
        if not validation_result:
            LOGGER.error("Invalid OpenAI API key format in BYOK configuration")
            return None

        client_kwargs = {
            "api_key": api_key
        }

        if base_url:
            client_kwargs["base_url"] = base_url

        client = OpenAI(**client_kwargs)
        
        # Log with masked API key for security
        masked_key = APIKeyValidator.mask_api_key(api_key)
        LOGGER.info(f"Created BYOK OpenAI client with key: {masked_key}, base_url: {base_url}")
        return client

    except Exception as e:
        LOGGER.error(f"Error creating BYOK OpenAI client: {e}")
        return None


def get_openai_client_with_custom_endpoint(session: str) -> Optional[OpenAI]:
    """
    Get OpenAI client configured with custom endpoint from environment variable.

    Args:
        session: Session ID to use as API key

    Returns:
        OpenAI client instance with custom endpoint
    """
    base_url = os.getenv('MY_MODEL_OPENAI_API_ENDPOINT')
    if base_url:
        LOGGER.info(f"Using custom OpenAI endpoint: {base_url}")
    else:
        LOGGER.debug("No custom OpenAI endpoint configured, using default")
    
    return create_openai_client(session, base_url)


def get_openai_client_with_session_or_byok(session: Optional[str] = None) -> Optional[OpenAI]:
    """
    Get OpenAI client using either session-based auth or BYOK configuration.
    Prioritizes BYOK if available, falls back to session-based auth.

    Args:
        session: Session ID to use as API key (if BYOK not available)

    Returns:
        OpenAI client instance or None if neither method is available
    """
    LOGGER.info(f"Attempting to create OpenAI client with session: {session}")

    # Try BYOK first
    byok_client = create_byok_openai_client()
    if byok_client:
        LOGGER.info("Using BYOK OpenAI client")
        return byok_client

    # Fall back to session-based authentication
    if session:
        LOGGER.info("BYOK not available, using session-based OpenAI client")
        client = get_openai_client_with_custom_endpoint(session)
        if client:
            LOGGER.info("Successfully created session-based OpenAI client")
            return client
        else:
            LOGGER.error("Failed to create session-based OpenAI client")
    else:
        LOGGER.error("No session ID provided for session-based authentication")

    LOGGER.warning("Neither BYOK nor session authentication available for OpenAI client")
    return None


def validate_openai_configuration() -> Dict[str, Any]:
    """
    Validate OpenAI configuration and return status information.

    Returns:
        Dictionary containing validation results and configuration status
    """
    status = {
        'openai_available': OPENAI_AVAILABLE,
        'byok_configured': False,
        'custom_endpoint_configured': False,
        'warnings': []
    }

    if not OPENAI_AVAILABLE:
        status['warnings'].append("OpenAI package not available")
        return status

    # Check BYOK configuration
    byok_config = _get_byok_openai_config()
    if byok_config:
        api_key = byok_config['api_key']
        if APIKeyValidator.validate_api_key_format('openai', api_key):
            status['byok_configured'] = True
            LOGGER.info("Valid BYOK OpenAI configuration found")
        else:
            status['warnings'].append("Invalid OpenAI API key format in BYOK configuration")

    # Check custom endpoint configuration
    custom_endpoint = os.getenv('MY_MODEL_OPENAI_API_ENDPOINT')
    if custom_endpoint:
        status['custom_endpoint_configured'] = True
        status['custom_endpoint'] = custom_endpoint
        LOGGER.info(f"Custom OpenAI endpoint configured: {custom_endpoint}")

    # Validate environment API keys if present
    env_openai_key = os.getenv('OPENAI_API_KEY')
    if env_openai_key:
        if APIKeyValidator.validate_api_key_format('openai', env_openai_key):
            status['env_key_valid'] = True
            LOGGER.info("Valid OpenAI API key found in environment")
        else:
            status['env_key_valid'] = False
            status['warnings'].append("Invalid OpenAI API key format in environment")

    return status


# Convenience functions
def get_openai_session_router(session: str) -> Optional[OpenAI]:
    """
    Create OpenAI client with default configuration (custom endpoint if available).
    
    Args:
        session: Session ID to use as API key
        
    Returns:
        OpenAI client instance
    """
    return get_openai_client_with_session_or_byok(session)
