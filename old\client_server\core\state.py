import threading
from typing import Generic, TypeVar

from .state_types import BaseURL
from client_server.core.logger import LOGGER

# -------------------------------------------------------------------------------------------------


T = TypeVar("T")


class ThreadSafeState(Generic[T]):
    name: str
    _value: T | None
    _lock: threading.Lock

    def __init__(self, name: str, initial_value: T | None = None):
        self.name = name
        self._value: T | None = initial_value
        self._lock = threading.Lock()

    def set(self, value: T | None) -> None:
        with self._lock:
            LOGGER.info(f"Setting {self.name} to {value}")
            self._value = value

    def get(self) -> T | None:
        with self._lock:
            return self._value


# -------------------------------------------------------------------------------------------------


# Session tracking states

G_SESSION_ID = ThreadSafeState[str]("SESSION_ID")

# Version tracking states

G_EXTENSION_VERSION = ThreadSafeState[str]("EXTENSION_VERSION")
G_CLIENT_SERVER_VERSION = ThreadSafeState[str]("CLIENT_SERVER_VERSION")

# BASE_URL

G_BASE_URL = ThreadSafeState[BaseURL]("BASE_URL", BaseURL())

# Cancelled request IDs

G_CANCELLED_REQUEST_IDS = ThreadSafeState[set[str]]("CANCELLED_REQUEST_IDS", set())


# -------------------------------------------------------------------------------------------------
