from overrides import override
from typing import List, Optional
import ollama
import socket
import subprocess
import time
import os
from pathlib import Path
from subprocess import Pope<PERSON>

from client_server.core.logger import LOGGER
from client_server.utils.platform_detector import PlatformDetector
from client_server.utils.path_selector import PathSelector
from . import ITokenizationBackend


class OllamaTokenizationBackend(ITokenizationBackend):
    """
    A tokenization service using Ollama's tokenizer.
    """

    _server_process: Optional[Popen[str]] = None
    _api_base: str = "http://localhost:11434"
    _default_model: str = "llama3"

    def __init__(self):
        self.target_dir = PathSelector.get_cache_path() / "ollama"
        self._ollama_binary_path = self._get_binary_path(str(self.target_dir))
        # Ensure the server is running on initialization
        self._start_server()

    def _get_binary_path(self, ollama_base_path: str) -> str:
        """Get the platform-specific path to the Ollama binary."""
        binary_paths = {
            "windows": f"{ollama_base_path}/ollama.exe",
            "linux": f"{ollama_base_path}/bin/ollama",
            "darwin": f"{ollama_base_path}/ollama",
        }

        os_name = PlatformDetector.get_os_name()
        if os_name not in binary_paths:
            raise ValueError(f"Unsupported platform: {os_name}")
        return binary_paths[os_name]

    def _is_port_available(self, port: int = 11434) -> bool:
        """Check if the specified port is available."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return True
        except socket.error:
            return False

    def _start_server(self):
        """Start the Ollama server if not already running"""
        if self._server_process is not None:
            return

        # Check if port is available
        if not self._is_port_available():
            LOGGER.info("Ollama server is already running, using existing instance")
            return

        # Create .logs directory if it doesn't exist
        log_path = PathSelector.get_logs_path() / "ollama.log"
        LOGGER.info(f"Starting ollama server at {self._ollama_binary_path}")
        try:
            with open(log_path, "w") as log_file:
                env = os.environ.copy()
                env["OLLAMA_MODELS"] = str(self.target_dir)
                self._server_process = subprocess.Popen(
                    [self._ollama_binary_path, "serve"],
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    text=True,
                    env=env,
                    shell=False,
                )
                time.sleep(1)
                retry_count = 10
                while retry_count > 0:
                    try:
                        ollama.ps()
                        break
                    except Exception as e:
                        LOGGER.warning(
                            f"Failed to start ollama server, retrying... ({retry_count}/10): {e}"
                        )
                        retry_count -= 1
                        time.sleep(1)
                LOGGER.info("ollama server started successfully")
        except Exception as e:
            LOGGER.error(f"Failed to start ollama server: {e}")
            raise RuntimeError(f"Failed to start ollama server: {e}")

    @override
    def count_tokens(self, text: str, model: str = None) -> int:
        """Count the number of tokens in a text string using Ollama's tokenizer."""
        if not text:
            return 0

        try:
            # Ensure server is running
            self._start_server()

            # Use the provided model or default
            model_to_use = model or self._default_model

            # Use Ollama client to tokenize and count
            client = ollama.Client()
            tokens = client.tokenize(model=model_to_use, text=text)
            return len(tokens)
        except Exception as e:
            LOGGER.warning(f"Error counting tokens with Ollama: {e}")
            # Fallback to a rough character-based estimate
            return len(text) // 4  # Rough approximation

    @override
    def tokenize(self, text: str, model: str = None) -> List[int]:
        """Convert a string to tokens using Ollama's tokenizer."""
        if not text:
            return []

        try:
            # Ensure server is running
            self._start_server()

            # Use the provided model or default
            model_to_use = model or self._default_model

            # Get tokens
            client = ollama.Client()
            return client.tokenize(model=model_to_use, text=text)
        except Exception as e:
            LOGGER.warning(f"Error tokenizing with Ollama: {e}")
            return []

    @override
    def detokenize(self, tokens: List[int], model: str = None) -> str:
        """Convert tokens back to a string using Ollama's detokenizer."""
        if not tokens:
            return ""

        try:
            # Ensure server is running
            self._start_server()

            # Use the provided model or default
            model_to_use = model or self._default_model

            # Use Ollama client to detokenize
            client = ollama.Client()
            return client.detokenize(model=model_to_use, tokens=tokens)
        except Exception as e:
            LOGGER.warning(f"Error detokenizing with Ollama: {e}")
            return ""

    @override
    def truncate_to_token_limit(
        self, text: str, max_tokens: int, model: str = None
    ) -> str:
        """Truncate text to stay within a maximum token limit using Ollama's tokenizer."""
        if not text or max_tokens <= 0:
            return ""

        try:
            # Ensure server is running
            self._start_server()

            # Use the provided model or default
            model_to_use = model or self._default_model

            # Get tokens
            client = ollama.Client()
            tokens = client.tokenize(model=model_to_use, text=text)

            if len(tokens) <= max_tokens:
                return text

            # Get the truncated tokens
            truncated_tokens = tokens[:max_tokens]

            # Convert tokens back to text
            # Since we can't easily map tokens directly back to specific character positions,
            # we'll use the following approach

            truncated_text = ""
            for token_id in truncated_tokens:
                token_text = client.detokenize(model=model_to_use, tokens=[token_id])
                truncated_text += token_text

            return truncated_text
        except Exception as e:
            LOGGER.warning(f"Error truncating text with Ollama: {e}")
            # Fallback to character-based truncation (rough estimate)
            char_limit = max_tokens * 4  # Rough approximation
            return text[:char_limit]
