"""
CodeLens API Routes

This module provides REST API endpoints for CodeLens operations including
inline editing, debugging, test generation, optimization, and code explanation.
"""

import time
import traceback
from typing import Any, Dict, Optional
from fastapi import HTTPException, Request
from pydantic import BaseModel, Field

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_operation_stats
from client_server.api.events.connection import update_session_activity
from client_server.utils.router.litellm_router import get_litellm_session_router
from client_server.utils.prompts.codelens_prompts import get_codelens_messages
from client_server.utils.services.llm_model import get_llm_model_name
from . import route
from client_server.core.state import G_SESSION_ID

class CodeLensRequest(BaseModel):
    """Base request model for CodeLens operations"""
    
    language: str = Field(..., description="Programming language of the code")
    context_code: str = Field(default="", description="Surrounding code context")
    target_code: str = Field(..., description="Target code for the operation")
    instructions: str = Field(..., description="User instructions or requirements")
    model: str = Field(default="gpt-4o-mini", description="LLM model to use")
    session_id: Optional[str] = Field(default=None, description="Session identifier")


class CodeLensResponse(BaseModel):
    """Response model for CodeLens operations"""
    
    success: bool = Field(..., description="Whether the operation was successful")
    result: str = Field(default="", description="Generated code or explanation")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")
    processing_time: float = Field(..., description="Time taken to process the request in seconds")
    model_used: str = Field(..., description="Model that was used for the operation")


async def _process_codelens_request(
    operation: str, 
    request_data: CodeLensRequest, 
    session_id: Optional[str] = None
) -> CodeLensResponse:
    """
    Common processing logic for CodeLens operations
    
    Args:
        operation: Type of operation ('edit', 'debug', 'test', 'optimize', 'explain')
        request_data: Request data containing code and instructions
        session_id: Optional session identifier
    
    Returns:
        CodeLensResponse with the result or error
    """
    start_time = time.time()
    
    try:
        # Update session activity if session ID is provided
        if session_id:
            update_session_activity(session_id)
        
        LOGGER.info(f"Processing CodeLens {operation} request - Language: {request_data.language}, Model: {request_data.model}")
        
        # Generate messages for the operation
        messages = get_codelens_messages(
            operation=operation,
            language=request_data.language,
            context_code=request_data.context_code,
            target_code=request_data.target_code,
            instructions=request_data.instructions
        )
        
        # Get LiteLLM client and make completion request
        client = get_litellm_session_router(session_id)()

        model_name = get_llm_model_name(operation)
        
        completion_start = time.time()
        response = await client.acompletion(
            messages=messages,
            model=model_name,
            temperature=0.1,  # Low temperature for more consistent code generation
            max_tokens=4000
        )
        completion_time = time.time() - completion_start
        
        # Extract content from response
        result = response.choices[0].message.content if response.choices else ""
        
        processing_time = time.time() - start_time
        
        LOGGER.info(f"CodeLens {operation} completed - Processing time: {processing_time:.3f}s, Completion time: {completion_time:.3f}s")
        
        return CodeLensResponse(
            success=True,
            result=result,
            processing_time=processing_time,
            model_used=request_data.model
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Error in CodeLens {operation}: {str(e)}"
        
        LOGGER.error(error_msg)
        LOGGER.debug(f"CodeLens {operation} error traceback: {traceback.format_exc()}")
        
        return CodeLensResponse(
            success=False,
            result="",
            error=error_msg,
            processing_time=processing_time,
            model_used=request_data.model
        )


@route("POST", "/codelens/inline/edit")
async def codelens_inline_edit(request: Request):
    """
    CodeLens inline code editing endpoint
    
    Accepts code context and instructions, returns edited code.
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        import json
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session") or G_SESSION_ID.get()
        
        # Validate request data
        request_data = CodeLensRequest.model_validate(data)
        
        # Process the request
        response = await _process_codelens_request("edit", request_data, session_id)
        
        # Log operation stats
        log_operation_stats("codelens_edit", time.time() - start_time, response.success)
        
        return response.model_dump()
        
    except Exception as e:
        LOGGER.error(f"Error in codelens inline edit endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@route("POST", "/codelens/inline/debug")
async def codelens_inline_debug(request: Request):
    """
    CodeLens debugging assistance endpoint
    
    Accepts code and returns debugging insights and suggestions.
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        import json
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session")
        
        # Validate request data
        request_data = CodeLensRequest.model_validate(data)
        
        # Process the request
        response = await _process_codelens_request("debug", request_data, session_id)
        
        # Log operation stats
        log_operation_stats("codelens_debug", time.time() - start_time, response.success)
        
        return response.model_dump()
        
    except Exception as e:
        LOGGER.error(f"Error in codelens debug endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@route("POST", "/codelens/inline/test")
async def codelens_inline_test(request: Request):
    """
    CodeLens test generation endpoint
    
    Accepts code and returns comprehensive test cases.
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        import json
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session")
        
        # Validate request data
        request_data = CodeLensRequest.model_validate(data)
        
        # Process the request
        response = await _process_codelens_request("test", request_data, session_id)
        
        # Log operation stats
        log_operation_stats("codelens_test", time.time() - start_time, response.success)
        
        return response.model_dump()
        
    except Exception as e:
        LOGGER.error(f"Error in codelens test endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@route("POST", "/codelens/inline/optimize")
async def codelens_inline_optimize(request: Request):
    """
    CodeLens code optimization endpoint
    
    Accepts code and returns optimized version with performance improvements.
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        import json
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session")
        
        # Validate request data
        request_data = CodeLensRequest.model_validate(data)
        
        # Process the request
        response = await _process_codelens_request("optimize", request_data, session_id)
        
        # Log operation stats
        log_operation_stats("codelens_optimize", time.time() - start_time, response.success)
        
        return response.model_dump()
        
    except Exception as e:
        LOGGER.error(f"Error in codelens optimize endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@route("POST", "/codelens/inline/explain")
async def codelens_inline_explain(request: Request):
    """
    CodeLens code explanation endpoint
    
    Accepts code and returns detailed explanation of functionality.
    """
    start_time = time.time()
    
    try:
        # Parse request body
        body = await request.body()
        import json
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session")
        
        # Validate request data
        request_data = CodeLensRequest.model_validate(data)
        
        # Process the request
        response = await _process_codelens_request("explain", request_data, session_id)
        
        # Log operation stats
        log_operation_stats("codelens_explain", time.time() - start_time, response.success)
        
        return response.model_dump()
        
    except Exception as e:
        LOGGER.error(f"Error in codelens explain endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))
