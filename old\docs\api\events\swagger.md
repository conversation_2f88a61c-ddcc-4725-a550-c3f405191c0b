# Swagger Generation Events (`swagger.py`)

The `swagger.py` module provides powerful functionality for generating API client code and multi-step API call sequences from Swagger/OpenAPI specifications. It operates in two distinct modes: generating code from a full specification, or generating a plan from a natural language prompt against an indexed knowledge base.

## Overview

The `handle_swagger_gen_event` is the main entry point. It checks the payload for a `swagger_knowledgebase_id`.

- If the ID is **not** present, it triggers the **Specification Generation** flow.
- If the ID **is** present, it triggers the **Prompt Generation** flow.

---

## 1. Specification-Based Code Generation

This flow is used when the user provides a complete Swagger specification (as a file, URL, or raw text content) and wants to generate client-side code for all of its endpoints.

### Event Flow

1.  **Parse Specification**: The handler first calls `prepare_spec_file` to load and parse the Swagger spec, extracting a list of all defined endpoints.
2.  **Begin Event**: It emits a `swagger_gen:begin` event to the client, sending the list of all endpoints that will be processed.
3.  **Process Endpoints**: It iterates through each endpoint and calls `process_endpoint`. This function sends the endpoint's specification to a backend service (`/swagger/gen`) along with the desired client language and any custom instructions. The backend generates the corresponding function/method.
4.  **Stream Progress**: For each endpoint, it emits events to the client to show progress:
    - `swagger_gen:endpoint_start`
    - `swagger_gen:endpoint_success` (with the generated code)
    - `swagger_gen:endpoint_error`
5.  **Combine Imports**: After all endpoints are processed, `combine_imports` gathers all the unique import statements required by the generated code.
6.  **Success Events**: Finally, it emits `swagger_gen:imports` with the combined imports and `swagger_gen:success` to signal completion.

---

## 2. Prompt-Based Generation

This flow is used when the user has already indexed a Swagger file into a knowledge base and wants to perform a task described in natural language (e.g., "create a new user and then add them to a project").

### Event Flow

1.  **Parse Prompt**: It parses the user's `custom_instructions` to separate the natural language prompt from any structuring instructions.
2.  **Generate Queries**: It sends the user's prompt to a backend service (`/swagger/generate_queries`) which returns a list of specific, searchable queries relevant to the prompt.
3.  **Search Knowledge Base**: It uses `_perform_swagger_search` to run these queries against the specified `swagger_knowledgebase_id`, finding the most relevant API endpoints.
4.  **Emit References**: It emits `swagger_gen:references` to show the client which API endpoints were found.
5.  **Generate Sequence**: It sends the user's prompt and the search results to another backend service (`/swagger/sequence_endpoints`). This service analyzes the prompt's intent and determines the logical sequence in which the API calls should be made.
6.  **Structure Output**: Optionally, if the user provided structuring instructions, the generated sequence is passed to one last service (`/swagger/structure_output`) to format the final result.
7.  **Return Response**: The final, structured plan or code is sent to the client via the `swagger_gen:kb_response` event.
8.  **End Event**: The process concludes with a `swagger_gen:end` event.
