from abc import ABC, abstractmethod
from enum import Enum
from typing import Generator, <PERSON><PERSON>, Any

from .utils import register_all_dependencies
from client_server.services.dependencies.utils import DependencyRegistry
from client_server.utils.platform_detector import PlatformDetector
from client_server.utils.resource import SystemResource


class DependencyStatus(Enum):
    """Status of a dependency"""

    # Missing dependencies
    MISSING = "missing"

    # Corrupted dependencies
    CORRUPTED = "corrupted"

    # Pending dependencies
    PENDING = "pending"

    # Working dependencies
    IDLE = "idle"
    STARTED = "started"

    # -- Preparing dependencies
    PREPARING = "preparing"
    PREPARED = "prepared"

    # -- Installing dependencies
    INSTALLING = "installing"
    INSTALLED = "installed"

    # -- Loading dependencies
    LOADING = "loading"
    LOADED = "loaded"

    # -- Ready dependencies
    READY = "ready"

    # -- Error dependencies
    ERROR = "error"


class IDependency(ABC):
    @abstractmethod
    def get_status(self) -> DependencyStatus:
        """Get the current status of the dependency."""
        pass

    @property
    @abstractmethod
    def id(self) -> str:
        """Get the id of the dependency."""
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """Get the name of the dependency."""
        pass

    @property
    @abstractmethod
    def version(self) -> str:
        """Get the version of the dependency."""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """Get the description of the dependency."""
        pass

    @abstractmethod
    def install(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Install the dependency.
        Yields status updates during the installation process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """
        pass

    @abstractmethod
    def uninstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Uninstall the dependency.
        Yields status updates during the uninstallation process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """
        pass

    @abstractmethod
    def reinstall(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Reinstall the dependency by first uninstalling and then installing.
        Yields status updates during the process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """
        pass

    @abstractmethod
    def ensure(self) -> Generator[Tuple[DependencyStatus, Any], None, None]:
        """
        Ensure the dependency is installed and ready.
        First reports the current status, then handles installation if needed.
        Yields status updates during the process.

        Yields:
            Tuple[DependencyStatus, Any]: Status updates with associated data
        """
        pass


# -----------------------------------------------------------------------------
# Initialize dependencies after all modules are imported
# -----------------------------------------------------------------------------

from client_server.services.dependencies.utils import register_all_dependencies

register_all_dependencies()

# -----------------------------------------------------------------------------


def prepare_common_dependencies():
    deps = []
    if PlatformDetector.is_snapdragon_arm():
        pass
    else:
        deps.append(DependencyRegistry.get_dependency_by_id("embedding_model"))
        # # Chat model for eco mode:
        # # ---- RAM: 3 GB, VRAM: 3 GB
        # ram_size = SystemResource.get_ram_size_in_gb()
        # vram_size = SystemResource.get_vram_size_in_gb()
        # if ram_size >= 3 or vram_size >= 3:
        #     deps.append(DependencyRegistry.get_dependency_by_id("ollama_model_qwen_coder"))
    return deps


def prepare_eco_mode_dependencies():
    deps = []
    if PlatformDetector.is_snapdragon_arm():
        deps = [
            DependencyRegistry.get_dependency_by_id("inferx"),
            DependencyRegistry.get_dependency_by_id("inferx_all_models"),
        ]
    else:
        deps = [
            DependencyRegistry.get_dependency_by_id("ollama"),
            DependencyRegistry.get_dependency_by_id("ollama_model_embedding"),
        ]
        # Chat model for eco mode:
        # ---- RAM: 3 GB, VRAM: 3 GB
        ram_size = SystemResource.get_ram_size_in_gb()
        vram_size = SystemResource.get_vram_size_in_gb()
        if ram_size >= 3 or vram_size >= 3:
            deps.append(DependencyRegistry.get_dependency_by_id("ollama_model_qwen"))

    return deps


def get_dependencies_status():
    """Get the status of all dependencies"""
    result = [
        {
            "id": "common",
            "name": "Common dependencies",
            "dependencies": prepare_common_dependencies(),
        },
        {
            "id": "eco_mode",
            "name": "Eco mode dependencies",
            "dependencies": prepare_eco_mode_dependencies(),
        },
        {
            "id": "normal_mode",
            "name": "Normal mode dependencies",
            "dependencies": [],
        },
    ]

    for group in result:
        for i, dependency in enumerate(group["dependencies"]):
            group["dependencies"][i] = {
                "id": dependency.id,
                "name": dependency.name,
                "status": dependency.get_status().value,
                "version": dependency.version,
                "description": dependency.description,
                "can_install": hasattr(dependency, "install"),
            }

    return result
