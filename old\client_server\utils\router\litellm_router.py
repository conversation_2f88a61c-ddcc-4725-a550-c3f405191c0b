"""
LiteLLM Router Utility for CodeLens API

This module provides function-based utilities for creating LiteLLM routers
with BYOK (Bring Your Own Key) configuration and session-based authentication.
"""

import os
from typing import Dict, Any, Optional
from litellm import Router
from dotenv import load_dotenv

from client_server.core.logger import LOGGER
from client_server.core.state import G_SESSION_ID
from client_server.utils.path_selector import PathSelector
from client_server.utils.security import validate_and_log_api_keys, APIKeyValidator

# Initialize environment
load_dotenv()


def _load_settings() -> dict:
    """Load settings from ~/settings.json"""
    return PathSelector._load_settings()


def _get_env_api_keys() -> Dict[str, str]:
    """Get API keys from environment variables"""
    api_keys = {}
    
    # Check for OpenAI API key
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        api_keys['openai'] = openai_key
    
    # Check for Anthropic API key
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    if anthropic_key:
        api_keys['anthropic'] = anthropic_key
    
    # Check for Google API key
    google_key = os.getenv('GOOGLE_API_KEY')
    if google_key:
        api_keys['google'] = google_key
    
    return api_keys


def _get_azure_config() -> Dict[str, str]:
    """Get Azure OpenAI configuration from environment variables"""
    return {
        'api_key': os.getenv('AZURE_OPENAI_API_KEY', ''),
        'api_base': os.getenv('AZURE_OPENAI_ENDPOINT', ''),
        'api_version': os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview')
    }


def _get_my_model_config() -> Dict[str, str]:
    """Get custom model configuration from environment variables"""
    return {
        'api_base': os.getenv('MY_MODEL_LITELLM_API_ENDPOINT', ''),
        'api_key': os.getenv('MY_API_KEY', '')
    }


def _get_session_id(session_id: Optional[str] = None) -> Optional[str]:
    """
    Get session ID from parameter or global state.

    Args:
        session_id: Optional session ID parameter

    Returns:
        Session ID from parameter or global state, or None if not available
    """
    if session_id:
        return session_id

    try:
        global_session_id = G_SESSION_ID.get()
        if global_session_id:
            LOGGER.debug(f"Using session ID from global state: {global_session_id}")
            return global_session_id
    except Exception as e:
        LOGGER.debug(f"Could not get session ID from global state: {e}")

    return None


def _get_byok_config() -> Optional[Dict[str, Any]]:
    """Extract BYOK configuration from settings"""
    settings = _load_settings()
    byok_config = settings.get('byok', {})
    
    if not byok_config or not byok_config.get('enabled', False):
        LOGGER.debug("BYOK not enabled or configured")
        return None
        
    # Check if required API keys are present
    api_keys = byok_config.get('api_keys', {})
    if not api_keys:
        LOGGER.warning("BYOK enabled but no API keys configured")
        return None
        
    LOGGER.info("BYOK configuration found and enabled")
    return byok_config


def create_byok_router(byok_config: Dict[str, Any]) -> Router:
    """Create LiteLLM router with BYOK configuration"""
    api_keys = byok_config.get('api_keys', {})
    
    # Validate and log API keys using security utilities
    validated_keys, warnings = validate_and_log_api_keys(api_keys)
    
    if warnings:
        LOGGER.warning(f"BYOK configuration warnings: {warnings}")
    
    model_list = []
    
    # Configure OpenAI models if API key is present and valid
    if 'openai' in validated_keys:
        model_list.extend([
            {
                "model_name": "gpt-4o",
                "litellm_params": {
                    "model": "openai/gpt-4o",
                    "api_key": validated_keys['openai']
                }
            },
            {
                "model_name": "gpt-4o-mini",
                "litellm_params": {
                    "model": "openai/gpt-4o-mini",
                    "api_key": validated_keys['openai']
                }
            },
            {
                "model_name": "gpt-3.5-turbo",
                "litellm_params": {
                    "model": "openai/gpt-3.5-turbo",
                    "api_key": validated_keys['openai']
                }
            }
        ])
        LOGGER.info(f"Added OpenAI models with key: {APIKeyValidator.mask_api_key(validated_keys['openai'])}")
    
    # Configure Anthropic models if API key is present and valid
    if 'anthropic' in validated_keys:
        model_list.extend([
            {
                "model_name": "claude-3-5-sonnet-20241022",
                "litellm_params": {
                    "model": "anthropic/claude-3-5-sonnet-20241022",
                    "api_key": validated_keys['anthropic']
                }
            },
            {
                "model_name": "claude-3-haiku-20240307",
                "litellm_params": {
                    "model": "anthropic/claude-3-haiku-20240307",
                    "api_key": validated_keys['anthropic']
                }
            }
        ])
        LOGGER.info(f"Added Anthropic models with key: {APIKeyValidator.mask_api_key(validated_keys['anthropic'])}")
    
    # Configure Google models if API key is present and valid
    if 'google' in validated_keys:
        model_list.extend([
            {
                "model_name": "gemini-1.5-pro",
                "litellm_params": {
                    "model": "google/gemini-1.5-pro",
                    "api_key": validated_keys['google']
                }
            },
            {
                "model_name": "gemini-1.5-flash",
                "litellm_params": {
                    "model": "google/gemini-1.5-flash",
                    "api_key": validated_keys['google']
                }
            }
        ])
        LOGGER.info(f"Added Google models with key: {APIKeyValidator.mask_api_key(validated_keys['google'])}")
    
    if not model_list:
        raise ValueError("No valid API keys found in BYOK configuration")
    
    LOGGER.info(f"Creating BYOK router with {len(model_list)} models")
    return Router(model_list=model_list)


def get_litellm_client_with_session_or_byok(session_id: Optional[str] = None) -> Router:
    """Create LiteLLM router with session-based authentication"""
    effective_session_id = _get_session_id(session_id)
    
    try:
        byok_config = _get_byok_config()
        
        if byok_config:
            # For BYOK, still use API keys
            router = create_byok_router(byok_config)
            LOGGER.info(f"Created BYOK router for session: {effective_session_id}")
        else:
            # For internal router, use session-based authentication
            my_model_config = _get_my_model_config()
            LOGGER.info(f"Using session ID: {effective_session_id}")
            model_list = [
                {
                    "model_name": "gpt-4.1-mini",
                    "litellm_params": {
                        "model": "gpt-4.1-mini",
                        "api_base": my_model_config.get('api_base', ''),
                        "api_key": my_model_config.get('api_key', ''),
                    }
                },
                {
                    "model_name": "gpt-4.1",
                    "litellm_params": {
                        "model": "gpt-4.1",
                        "api_base": my_model_config.get('api_base', ''),
                        "api_key": my_model_config.get('api_key', ''),
                    }
                },
                {
                    "model_name": "gpt-4o",
                    "litellm_params": {
                        "model": "gpt-4o",
                        "api_base": my_model_config.get('api_base', ''),
                        "api_key": my_model_config.get('api_key', ''),
                    }
                },
                {
                    "model_name": "o4-mini",
                    "litellm_params": {
                        "model": "o4-mini",
                        "api_base": my_model_config.get('api_base', ''),
                        "api_key": my_model_config.get('api_key', ''),
                    }
                },
            ]

            
            router = Router(model_list=model_list)
            LOGGER.info(f"Created session-based internal router for session: {effective_session_id}")
        
        return router
        
    except Exception as e:
        LOGGER.error(f"Error creating session router for {effective_session_id}: {e}")
        # Fallback to basic internal router with session
        my_model_config = _get_my_model_config()
        model_list = [
            {
                "model_name": "gpt-4.1-mini",
                "litellm_params": {
                    "model": "gpt-4.1-mini",
                    "api_base": my_model_config.get('api_base', ''),
                    "api_key": my_model_config.get('api_key', ''),
                }
            },
            {
                "model_name": "gpt-4.1",
                "litellm_params": {
                    "model": "gpt-4.1",
                    "api_base": my_model_config.get('api_base', ''),
                    "api_key": my_model_config.get('api_key', ''),
                }
            },
            {
                "model_name": "gpt-4o",
                "litellm_params": {
                    "model": "gpt-4o",
                    "api_base": my_model_config.get('api_base', ''),
                    "api_key": my_model_config.get('api_key', ''),
                }
            },
            {
                "model_name": "o4-mini",
                "litellm_params": {
                    "model": "o4-mini",
                    "api_base": my_model_config.get('api_base', ''),
                    "api_key": my_model_config.get('api_key', ''),
                }
            },
        ]
        router = Router(model_list=model_list)
        LOGGER.warning(f"Falling back to internal router for session: {effective_session_id}")
        return router


# Convenience function for router creation
def get_litellm_session_router(session_id: Optional[str] = None) -> Router:
    """
    Convenience function to get a LiteLLM router configured for session-based authentication.

    Args:
        session_id: The session ID to use for authentication. If None, will try to get from global state.

    Returns:
        Router instance configured with session-based authentication
    """
    return get_litellm_client_with_session_or_byok(session_id)
