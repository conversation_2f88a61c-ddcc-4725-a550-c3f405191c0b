"""
Message processing utilities for dual message system.

This module provides the core functionality for maintaining two distinct message formats:
1. Internal payload format for frontend display and history storage
2. LLM sequence format for model consumption

The processor handles context transformation, placeholder replacement, and message structure
conversion while preserving the original payload structure.
"""

import asyncio
import copy
import json
import time
import traceback
from typing import Any, Dict, List, Tuple, Union, Optional

from client_server.core.logger import LOGGER
from client_server.api.routes.chat.utils.context_processors import FileContextProcessor, SwaggerContextProcessor
from client_server.api.routes.chat.streaming import HTTPStreamEventEmitter


class ContextTransformer:
    """
    Handles context processing and placeholder replacement for LLM consumption.
    """

    @staticmethod
    async def transform_context(
        context: Union[Dict[str, Any], Any],
        message_content: str,
        event_emitter: HTTPStreamEventEmitter,
        request_id: str
    ) -> Tuple[str, str]:
        """
        Transform a context object into formatted content for LLM consumption.

        Args:
            context: Context object (dict or object with attributes)
            message_content: Original message content for search query generation
            event_emitter: Event emitter for streaming responses
            request_id: Request ID for tracking

        Returns:
            Tuple of (formatted_context_for_llm, additional_content)
        """
        context_type = context.get("type") if isinstance(context, dict) else getattr(context, "type", None)
        context_name = context.get("name") if isinstance(context, dict) else getattr(context, "name", "")
        context_path = context.get("path") if isinstance(context, dict) else getattr(context, "path", None)
        context_kbid = context.get("kbid") if isinstance(context, dict) else getattr(context, "kbid", "")
        context_id = context.get("id") if isinstance(context, dict) else getattr(context, "id", "")

        LOGGER.debug(f"Transforming context - Type: {context_type}, Name: {context_name}, ID: {context_id}")

        formatted_context = ""
        additional_content = ""

        try:
            if context_type == "file":
                formatted_context, additional_content = await ContextTransformer._transform_file_context(
                    context, context_name, context_path
                )

            elif context_type in ["docs", "codebase", "git"]:
                formatted_context, additional_content = ContextTransformer._transform_knowledge_base_context(
                    context_type, context_name, context_kbid
                )

            elif context_type == "swagger":
                formatted_context, additional_content = await ContextTransformer._transform_swagger_context(
                    context, message_content, event_emitter, request_id
                )

            elif context_type == "folder":
                formatted_context, additional_content = ContextTransformer._transform_folder_context(
                    context_path, context_kbid
                )

            elif context_type in ["terminal", "errors", "warnings"]:
                formatted_context, additional_content = ContextTransformer._transform_diagnostic_context(
                    context_type, context_name, context
                )

            elif context_type == "commit":
                formatted_context, additional_content = ContextTransformer._transform_commit_context(
                    context_name, context
                )

            else:
                # Unknown context type - use basic formatting
                formatted_context, additional_content = ContextTransformer._transform_generic_context(
                    context_type, context_name, context
                )

        except Exception as e:
            LOGGER.error(f"Error transforming context {context_type}: {e}")
            LOGGER.error(f"Context transformation error traceback: {traceback.format_exc()}")
            formatted_context = f"Error processing {context_type} context: {context_name}"
            additional_content = ""

        LOGGER.debug(f"Context transformation complete - Formatted length: {len(formatted_context)}")
        return formatted_context, additional_content

    @staticmethod
    async def _transform_file_context(
        context: Union[Dict[str, Any], Any],
        context_name: str,
        context_path: str
    ) -> Tuple[str, str]:
        """Transform file context by loading and formatting file content."""
        # Process file context - load file content
        FileContextProcessor.process_file_context(context, context_name)
        file_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")

        # Format file content for LLM consumption
        formatted_context = f"File: {context_path or context_name}\n"
        if context_name and context_path != context_name:
            formatted_context += f"Name: {context_name}\n"
        formatted_context += f"Content:\n{file_content}"

        return formatted_context, ""

    @staticmethod
    def _transform_knowledge_base_context(
        context_type: str,
        context_name: str,
        context_kbid: str
    ) -> Tuple[str, str]:
        """Transform knowledge base contexts (docs, codebase, git)."""
        formatted_context = f"{context_type.title()}: {context_name}"
        additional_content = (
            f"\n\n[ Attached {context_type.title()} Knowledge Base:\n"
            f"Name: {context_name}    |    KNOWLEDGEBASE ID: {context_kbid}]\n\n"
        )
        return formatted_context, additional_content

    @staticmethod
    async def _transform_swagger_context(
        context: Union[Dict[str, Any], Any],
        message_content: str,
        event_emitter: HTTPStreamEventEmitter,
        request_id: str
    ) -> Tuple[str, str]:
        """Transform swagger context with search processing."""
        # Process swagger context with search
        await SwaggerContextProcessor.process_swagger_context(
            context, {"content": message_content}, event_emitter, request_id
        )

        swagger_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
        formatted_context = f"Swagger API Documentation:\n{swagger_content}"

        return formatted_context, ""

    @staticmethod
    def _transform_folder_context(context_path: str, context_kbid: str) -> Tuple[str, str]:
        """Transform folder context."""
        formatted_context = f"Folder: {context_path}"
        additional_content = (
            f"\n\n[ Attached Folder:\n"
            f"Path: {context_path}    |    KNOWLEDGEBASE ID: {context_kbid}]\n\n"
        )
        return formatted_context, additional_content

    @staticmethod
    def _transform_diagnostic_context(
        context_type: str,
        context_name: str,
        context: Union[Dict[str, Any], Any]
    ) -> Tuple[str, str]:
        """Transform diagnostic contexts (terminal, errors, warnings)."""
        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
        formatted_context = f"{context_type.title()}: {context_name}\nContent:\n{context_content}"
        return formatted_context, ""

    @staticmethod
    def _transform_commit_context(
        context_name: str,
        context: Union[Dict[str, Any], Any]
    ) -> Tuple[str, str]:
        """Transform git commit context."""
        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
        formatted_context = f"Git Commit: {context_name}\nDetails:\n{context_content}"
        return formatted_context, ""

    @staticmethod
    def _transform_generic_context(
        context_type: str,
        context_name: str,
        context: Union[Dict[str, Any], Any]
    ) -> Tuple[str, str]:
        """Transform unknown context types with basic formatting."""
        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
        formatted_context = f"{context_type or 'Unknown'}: {context_name}"
        if context_content:
            formatted_context += f"\nContent:\n{context_content}"
        return formatted_context, ""


class MessageSequenceProcessor:
    """
    Converts internal message format to LLM-readable format while preserving original payload.
    """
    
    @staticmethod
    async def process_messages_for_llm(
        payload_messages: List[Dict[str, Any]], 
        event_emitter: HTTPStreamEventEmitter,
        request_id: str
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Process payload messages to create LLM-consumable sequence while preserving original.
        
        Args:
            payload_messages: Original payload messages (internal format)
            event_emitter: Event emitter for streaming responses
            request_id: Request ID for tracking
            
        Returns:
            Tuple of (original_payload_messages, llm_sequence_messages)
        """
        # Deep copy original payload to preserve it
        original_payload = copy.deepcopy(payload_messages)
        llm_sequence = []
        
        LOGGER.info(f"Processing {len(payload_messages)} messages for LLM sequence")
        
        for message in payload_messages:
            message_role = message.get("role", "")

            if message_role == "user":
                # Process user message with context transformation
                processed_message = await MessageSequenceProcessor._process_user_message(
                    message, event_emitter, request_id
                )
                llm_sequence.append(processed_message)
                
            elif message_role == "assistant":
                # Assistant messages go through preserving tool_calls for follow-up context
                assistant_content = message.get("content", "")
                assistant_message = {
                    "role": "assistant",
                    "content": assistant_content
                }

                # Preserve tool_calls if present for follow-up question context
                if "tool_calls" in message:
                    assistant_message["tool_calls"] = message["tool_calls"]
                    LOGGER.debug(f"Preserved {len(message['tool_calls'])} tool_calls in assistant message for LLM")

                llm_sequence.append(assistant_message)
                
            elif message_role == "action":
                # Action messages are converted to tool role for LLM
                # This handles tool call results from previous interactions
                action_content = message.get("content", "")
                action_id = message.get("action_id", "")

                llm_sequence.append({
                    "role": "tool",
                    "tool_call_id": action_id,
                    "content": action_content
                })
                
            else:
                LOGGER.warning(f"Unknown message role: {message_role}")
                
        LOGGER.info(f"Generated LLM sequence with {len(llm_sequence)} messages")
        return original_payload, llm_sequence
    
    @staticmethod
    async def _process_user_message(
        message: Dict[str, Any],
        event_emitter: HTTPStreamEventEmitter,
        request_id: str
    ) -> Dict[str, Any]:
        """
        Process a user message, handling context transformation and placeholder replacement.
        
        Args:
            message: User message with potential context attachments
            event_emitter: Event emitter for streaming responses
            request_id: Request ID for tracking
            
        Returns:
            Processed message for LLM consumption
        """
        message_content = message.get("content", "")
        message_contexts = message.get("context", [])
        
        # Start with original content
        processed_content = message_content
        
        # Process each context and replace placeholders
        for context in message_contexts:
            context_id = context.get("id") if isinstance(context, dict) else getattr(context, "id", "")
            placeholder = f"<__$__{context_id}__$__>"
            
            if placeholder in processed_content:
                # Transform context for LLM consumption
                formatted_context, additional_content = await ContextTransformer.transform_context(
                    context, message_content, event_emitter, request_id
                )
                
                # Replace placeholder with formatted context
                processed_content = processed_content.replace(placeholder, formatted_context)
                
                # Add any additional content
                if additional_content:
                    processed_content += additional_content
        
        return {
            "role": "user",
            "content": processed_content
        }


class DualMessageManager:
    """
    Manages both message formats throughout the conversation lifecycle.
    """

    def __init__(self, original_payload: List[Dict[str, Any]]):
        """
        Initialize with original payload messages.

        Args:
            original_payload: Original payload messages (internal format)
        """
        self.original_payload = copy.deepcopy(original_payload)  # Immutable original
        self.payload_messages = copy.deepcopy(original_payload)  # Working copy for updates
        self.llm_messages = []  # For LLM consumption
        self.conversation_history = []  # Tracks full conversation including tool calls
        self.tool_call_counter = 0  # For generating unique tool call IDs

    async def initialize_llm_sequence(
        self,
        event_emitter: HTTPStreamEventEmitter,
        request_id: str
    ) -> List[Dict[str, Any]]:
        """
        Initialize the LLM message sequence from payload messages.

        Args:
            event_emitter: Event emitter for streaming responses
            request_id: Request ID for tracking

        Returns:
            LLM sequence messages ready for model consumption
        """
        _, self.llm_messages = await MessageSequenceProcessor.process_messages_for_llm(
            self.payload_messages, event_emitter, request_id
        )

        # Initialize conversation history with processed messages
        self.conversation_history = copy.deepcopy(self.llm_messages)

        return self.llm_messages

    def add_assistant_message_with_tool_calls(self, content: str, tool_calls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Add assistant message with tool calls to both formats.

        Args:
            content: Assistant response content
            tool_calls: List of tool calls made by the assistant

        Returns:
            Assistant message for LLM conversation
        """
        # Create assistant message for LLM format
        assistant_message = {
            "role": "assistant",
            "content": content if content.strip() else None,
            "tool_calls": tool_calls
        }

        # Add to LLM conversation history
        self.conversation_history.append(assistant_message)

        # For payload format, add assistant message WITH tool_calls to match LLM format
        # This enhances cloud storage to include tool calling information
        payload_assistant_message = {
            "role": "assistant",
            "content": content,
            "tool_calls": tool_calls  # Include tool_calls in cloud storage format
        }
        self.payload_messages.append(payload_assistant_message)

        # Add tool call actions to payload format for frontend display
        for tool_call in tool_calls:
            tool_action_message = {
                "role": "action",
                "action": {
                    "id": tool_call["id"],
                    "name": tool_call["function"]["name"],
                    "arguments": tool_call["function"]["arguments"]
                },
                "action_id": tool_call["id"],
                "content": f"Executing {tool_call['function']['name']}..."
            }
            self.payload_messages.append(tool_action_message)

        return assistant_message

    def add_tool_call_results(self, tool_results: List[Dict[str, Any]]):
        """
        Add tool call results to both message formats appropriately.

        Args:
            tool_results: List of tool call results
        """
        # Add to LLM conversation history as tool messages
        for tool_result in tool_results:
            tool_message = {
                "role": "tool",
                "tool_call_id": tool_result["tool_call_id"],
                "content": tool_result["content"]
            }
            self.conversation_history.append(tool_message)

        # Update corresponding action messages in payload format
        for tool_result in tool_results:
            # Find and update the corresponding action message
            for message in reversed(self.payload_messages):
                if (message.get("role") == "action" and
                    message.get("action_id") == tool_result["tool_call_id"]):
                    message["content"] = tool_result["content"]
                    break

    def add_final_assistant_response(self, content: str):
        """
        Add final assistant response after tool call processing.

        Args:
            content: Final assistant response content
        """
        if content.strip():
            # Add to LLM conversation history
            assistant_message = {
                "role": "assistant",
                "content": content
            }
            self.conversation_history.append(assistant_message)

            # Add to payload messages
            self.payload_messages.append(assistant_message)

    def get_original_payload(self) -> List[Dict[str, Any]]:
        """Get immutable original payload messages."""
        return self.original_payload

    def get_payload_messages(self) -> List[Dict[str, Any]]:
        """Get current payload messages for frontend/storage."""
        return self.payload_messages

    def get_llm_messages(self) -> List[Dict[str, Any]]:
        """Get current LLM conversation history."""
        return self.conversation_history

    def get_messages_for_cloud_sync(self) -> List[Dict[str, Any]]:
        """
        Get messages formatted for cloud history sync.
        Uses the original payload format with tool call updates.
        """
        return self.payload_messages

    def generate_tool_call_id(self) -> str:
        """Generate a unique tool call ID."""
        self.tool_call_counter += 1
        return f"call_{self.tool_call_counter}_{int(time.time())}"


class FrontendEventHandler:
    """
    Handles frontend event emissions using the dual message system.
    """

    def __init__(self, event_emitter: HTTPStreamEventEmitter, request_id: str):
        """
        Initialize the frontend event handler.

        Args:
            event_emitter: Event emitter for streaming responses
            request_id: Request ID for tracking
        """
        self.event_emitter = event_emitter
        self.request_id = request_id

    async def emit_content_chunk(self, content: str, chunk_index: int, conversation_id: str):
        """
        Emit content chunk to frontend.

        Args:
            content: Content chunk to emit
            chunk_index: Index of the chunk
            conversation_id: Conversation ID
        """
        response_data = {
            "type": "content",
            "request_id": self.request_id,
            "conversation_id": conversation_id,
            "data": {
                "type": "content",
                "memory_id": conversation_id,
                "chunk_index": chunk_index,
                "content": content,
            },
        }

        await self.event_emitter.emit("chat_response", data=response_data)
        return self.event_emitter.format_as_sse({
            "event": "chat_response",
            "data": response_data
        })

    async def emit_tool_call_start(self, tool_call_id: str, function_name: str = ""):
        """
        Emit tool call start event to frontend.

        Args:
            tool_call_id: ID of the tool call
            function_name: Name of the function being called (optional)
        """
        search_data = {"request_id": self.request_id, "action_id": tool_call_id}
        if function_name:
            search_data["function_name"] = function_name

        await self.event_emitter.emit(
            "chat_response_searching",
            data=search_data
        )

    async def emit_search_references(self, search_references):
        """
        Emit search references to frontend.

        Args:
            search_references: Search references object
        """
        if search_references and search_references.get_search_result().get("results"):
            await self.event_emitter.emit(
                "chat_response_references",
                data=search_references.get_search_result()
            )
            return self.event_emitter.format_as_sse({
                "event": "chat_response_references",
                "data": search_references.get_search_result()
            })
        return ""

    async def emit_follow_ups(self, follow_ups: List[str]):
        """
        Emit follow-up suggestions to frontend.

        Args:
            follow_ups: List of follow-up suggestions
        """
        await self.event_emitter.emit(
            "chat_response_follow_ups",
            data={
                "request_id": self.request_id,
                "follow_ups": follow_ups,
            }
        )
        return self.event_emitter.format_as_sse({
            "event": "chat_response_follow_ups",
            "data": {
                "request_id": self.request_id,
                "follow_ups": follow_ups,
            }
        })

    async def emit_chat_end(self):
        """Emit chat end event to frontend."""
        await self.event_emitter.emit(
            "chat_response_end", data={"request_id": self.request_id}
        )
        return self.event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": self.request_id}
        })

    async def emit_chat_error(self, error_message: str):
        """
        Emit chat error event to frontend.

        Args:
            error_message: Error message to display
        """
        await self.event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": self.request_id,
                "error": error_message,
            }
        )
        return self.event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": self.request_id,
                "error": error_message,
            }
        })
