# API Key Configuration Guide

This guide explains how to securely configure API keys for the LiteLLM client in CodeMate.AI.

## Overview

The LiteLLM client supports multiple AI providers and can be configured in two ways:
1. **BYOK (Bring Your Own Key)** - User-provided API keys via settings
2. **Environment Variables** - API keys loaded from environment variables (recommended for security)

## Security-First Approach

### Why Environment Variables?

- **Security**: API keys are not stored in configuration files
- **Flexibility**: Easy to change keys without modifying code
- **Best Practice**: Industry standard for sensitive configuration
- **Version Control Safe**: No risk of accidentally committing API keys

## Setup Instructions

### 1. Create Environment File

Copy the example environment file:

```bash
cp .env.example .env
```

### 2. Configure Your API Keys

Edit the `.env` file and add your API keys for the providers you want to use:

```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-your-actual-openai-api-key-here

# Anthropic Configuration  
ANTHROPIC_API_KEY=sk-ant-your-actual-anthropic-api-key-here

# Google AI Configuration
GOOGLE_API_KEY=your-actual-google-ai-api-key-here

# Azure OpenAI Configuration (if using Azure)
AZURE_OPENAI_API_KEY=your-actual-azure-openai-api-key-here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name
```

### 3. Obtain API Keys

#### OpenAI
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in to your account
3. Click "Create new secret key"
4. Copy the key (starts with `sk-`)

#### Anthropic
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Sign in to your account
3. Go to API Keys section
4. Create a new key (starts with `sk-ant-`)

#### Google AI
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the generated key

#### Azure OpenAI
1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to your Azure OpenAI resource
3. Go to "Keys and Endpoint" section
4. Copy the key and endpoint URL

## Security Best Practices

### ✅ Do's

- **Use environment variables** for API keys
- **Keep `.env` file local** - never commit to version control
- **Use different keys** for development and production
- **Rotate keys regularly** (every 90 days recommended)
- **Monitor API usage** and set up billing alerts
- **Use least privilege** - restrict API key permissions where possible
- **Enable IP restrictions** if your provider supports it

### ❌ Don'ts

- **Never hardcode** API keys in source code
- **Never commit** `.env` files to git
- **Don't share** API keys in chat/email
- **Don't use production keys** in development
- **Don't ignore** security warnings from the system

## Validation and Troubleshooting

### API Key Format Validation

The system automatically validates API key formats:

- **OpenAI**: `sk-` followed by 48+ characters
- **Anthropic**: `sk-ant-` followed by 95+ characters  
- **Google**: 39 characters (alphanumeric with dashes/underscores)
- **Azure**: 32 hexadecimal characters

### Common Issues

#### "Invalid API key format"
- Check that your API key matches the expected format
- Ensure no extra spaces or characters
- Verify you copied the complete key

#### "No API keys found"
- Ensure `.env` file exists in the project root
- Check environment variable names match exactly
- Restart the application after adding keys

#### "API key appears to be placeholder"
- Replace example values with actual API keys
- Remove any placeholder text from the keys

### Debug Mode

Enable debug logging to see detailed API key validation:

```bash
# Set log level to debug
export LOG_LEVEL=DEBUG
```

## Production Deployment

### Azure Key Vault (Recommended)

For production deployments, consider using Azure Key Vault:

```python
# Example integration (not implemented yet)
from azure.keyvault.secrets import SecretClient

# Retrieve API keys from Key Vault
openai_key = secret_client.get_secret("openai-api-key").value
```

### Environment Variables in Production

Set environment variables in your production environment:

```bash
# Linux/macOS
export OPENAI_API_KEY="your-key-here"

# Windows
set OPENAI_API_KEY=your-key-here

# Docker
docker run -e OPENAI_API_KEY="your-key-here" your-app

# Kubernetes
kubectl create secret generic api-keys \
  --from-literal=OPENAI_API_KEY="your-key-here"
```

## Monitoring and Alerts

### API Usage Monitoring

Set up monitoring for:
- API call volume and costs
- Rate limit violations
- Authentication failures
- Unusual usage patterns

### Billing Alerts

Configure billing alerts with your providers:
- OpenAI: Set usage limits in platform settings
- Anthropic: Monitor usage in console
- Google: Set up Cloud Billing alerts
- Azure: Configure cost alerts

## Support

If you encounter issues with API key configuration:

1. Check the logs for specific error messages
2. Verify your API key format and permissions
3. Test with a simple API call outside the application
4. Consult the provider's documentation
5. Contact support with sanitized logs (never include actual API keys)

## Security Incident Response

If you suspect an API key has been compromised:

1. **Immediately revoke** the compromised key
2. **Generate a new key** with the provider
3. **Update your environment** configuration
4. **Monitor usage** for any unauthorized activity
5. **Review access logs** if available
6. **Report the incident** if required by your organization
