# Swagger Service

The Swagger service is a specialized utility for parsing, resolving, and deconstructing OpenAPI (formerly Swagger) specification files. Its primary purpose is to take a complete OpenAPI specification and break it down into individual, self-contained specifications for each API endpoint.

This is particularly useful for features that might need to understand or interact with single API endpoints in isolation, such as generating tool calls for an AI agent.

## Design

Unlike the other services that use a backend/builder pattern, this service is a more direct utility. It's composed of two main classes:

- **`SwaggerSpecRefsResolver`**: The core component responsible for handling `$ref` references within a specification.
- **`SwaggerSpecGenerator`**: The main entry point that orchestrates the process of loading, resolving, and splitting the specification.

This service does not have an `__init__.py` interface, as it's a concrete utility rather than an abstract service with multiple backends.

## Core Components

### `SwaggerSpecRefsResolver` (`resolver.py`)

- **Reference Resolution**: Its main method, `resolve_references`, takes a spec dictionary and a base URI. It uses the `jsonref` library to recursively replace all JSON references (`$ref`) with their corresponding definitions. This creates a single, fully-resolved specification document.
- **Version Detection**: It can detect the OpenAPI version (1, 2, or 3) from the specification file, which is important because the structure for definitions (`definitions` vs. `components/schemas`) changes between versions.
- **Schema Extraction**: The `_find_referenced_schemas` method is crucial. It traverses the definition for a single endpoint and collects all the schemas that it references. This allows the generator to create a minimal, self-contained spec for that endpoint.

### `SwaggerSpecGenerator` (`generator.py`)

This class provides a high-level API for the user.

- **Multiple Input Sources**: It can load a specification from various sources:
  - `generate_from_content(dict)`: Processes a spec that's already a Python dictionary.
  - `generate_from_file(path)`: Reads and parses a local JSON or YAML file.
  - `generate_from_url(url)`: Fetches and parses a spec from a remote URL.
- **Endpoint Splitting**: After resolving the full spec using the `SwaggerSpecRefsResolver`, its main goal is to iterate through every path and method in the API. For each one, it:
  1.  Identifies all the dependent schemas required by that specific endpoint.
  2.  Uses `_create_endpoint_definition` to build a new, minimal OpenAPI spec containing _only_ the information for that single endpoint and its required schemas.
  3.  Returns a list of these individual endpoint specifications.

## Workflow

1.  **Instantiation**: A user creates an instance of `SwaggerSpecGenerator`.
2.  **Loading**: The user calls one of the `generate_from_*` methods with a URL, file path, or dictionary.
3.  **Resolution**: The generator uses the `SwaggerSpecRefsResolver` to resolve all `$ref`s in the document.
4.  **Generation**: It then iterates through each API endpoint (`/path`, `method`) in the resolved spec.
5.  **Extraction & Creation**: For each endpoint, it finds all referenced schemas and creates a brand-new, minimal OpenAPI document that contains only that endpoint and its necessary schemas.
6.  **Output**: The result is a list of dictionaries, where each dictionary represents a single, isolated API endpoint with its own valid OpenAPI spec.

**Example Usage:**

```python
from client_server.services.swagger.generator import SwaggerSpecGenerator

generator = SwaggerSpecGenerator()
endpoint_specs = generator.generate_from_url("https://petstore.swagger.io/v2/swagger.json")

# endpoint_specs is now a list, where each item is a self-contained spec for one endpoint
print(endpoint_specs[0])
```
