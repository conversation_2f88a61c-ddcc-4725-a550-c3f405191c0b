# WebSocket Events

The event handlers in `client_server/api/events` manage real-time communication with the client using WebSockets. Each file in this directory typically corresponds to a specific feature or set of related events.

## Event Naming Convention

As a convention, events often follow a `[feature]:[action]` pattern. For example:

- `chat:start`: Initiates a new chat session.
- `upload:progress`: Reports the progress of a file upload.
- `dependency_install:status`: Provides a status update on a dependency installation.

This consistent naming scheme helps in identifying the purpose of each event.

## Common Payloads

While each event has its own specific data structures, some common patterns exist:

- Most events that involve a long-running process will accept a `request_id` from the client. This ID is then used in subsequent events to track the operation.
- Progress and status events often include fields like `status`, `progress`, and `message` to provide feedback to the user.

Detailed documentation for each event handler and its associated data models can be found in the respective markdown files in this directory.
