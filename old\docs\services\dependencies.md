# Dependencies Service

The Dependencies service is responsible for managing, installing, and checking the status of external dependencies required by the application. This is crucial for functionalities that rely on local models or external tools, such as "Eco Mode".

## Design

The service is built around a few core components:

- **`IDependency` Interface**: An abstract base class that defines the contract for all dependency types. It standardizes operations like `install`, `uninstall`, `reinstall`, `ensure`, and status checking.
- **`DependencyStatus` Enum**: A set of statuses to represent the state of a dependency (e.g., `MISSING`, `INSTALLING`, `READY`, `ERROR`).
- **Dependency Implementations**: Concrete classes that implement `IDependency` for a specific tool or model. Examples include `OllamaDependency`, `OllamaModelDependency`, `InferXDependency`, and `InferXModelDependency`.
- **`DependencyRegistry`**: A static class that acts as a central registry for all dependencies. It allows registering dependencies and querying them by ID. It also persists the status of dependencies in a `dependencies.json` file.
- **Registration (`utils.py`)**: The `register_all_dependencies` function initializes and registers all the concrete dependency implementations with the `DependencyRegistry`.
- **Dependency Groups (`__init__.py`)**: Functions like `prepare_common_dependencies` and `prepare_eco_mode_dependencies` group dependencies for different operational modes.

## Core Components

### `IDependency` Interface

Located in `client_server/services/dependencies/__init__.py`, this ABC defines the following methods and properties:

- `get_status()`: Returns the current `DependencyStatus`.
- `id`, `name`, `version`, `description`: Properties to identify and describe the dependency.
- `install()`: A generator that installs the dependency, yielding status updates.
- `uninstall()`: A generator that removes the dependency, yielding status updates.
- `reinstall()`: Reinstalls the dependency.
- `ensure()`: Checks if the dependency is ready, and installs it if not.

### `DependencyRegistry`

Found in `client_server/services/dependencies/registry.py`, this class manages all dependencies.

- `register_dependency()`: Adds a dependency to the registry.
- `get_dependency_by_id()`: Retrieves a dependency.
- `get_status()` / `update_status()`: Reads and writes the status of a dependency to `dependencies.json` in the user's base path, providing persistence across sessions.

### Dependency Implementations

The service includes implementations for two main sets of dependencies:

1.  **Ollama**:

    - `OllamaDependency` (`ollama.py`): Manages the Ollama server executable itself. It handles downloading the correct binary for the user's OS and architecture, installing it, and checking its status.
    - `OllamaModelDependency` (`ollama_model.py`): Manages individual models within Ollama. It can start the Ollama server, pull models, check if they exist, and uninstall them.

2.  **InferX** (for Snapdragon ARM platforms):
    - `InferXDependency` (`inferx.py`): Manages the InferX inference engine. It downloads the engine from Hugging Face, extracts it, and sets permissions.
    - `InferXModelDependency` (`inferx_model.py`): Manages models for the InferX engine, also downloaded from Hugging Face.

## Workflow

1.  **Registration**: When the application starts, `register_all_dependencies()` is called, populating the `DependencyRegistry` with all known dependencies (Ollama, InferX, and their respective models).
2.  **Status Check**: The application can then query for dependency statuses, for example, to show the user in the UI. `get_dependencies_status()` in `__init__.py` groups dependencies by mode (common, eco) and returns their details.
3.  **Installation/Ensuring**: When a feature requires a dependency, it can call `ensure()` on that dependency.
    - The `ensure()` method checks the current status (from `dependencies.json` or by checking the file system).
    - If the dependency is `MISSING` or `CORRUPTED`, it will trigger the `install()` or `reinstall()` method.
    - The `install()` method is a generator that yields `(DependencyStatus, progress)` tuples, which can be used to show real-time progress to the user. For example, it yields progress percentages during downloads and extraction.
4.  **Usage**: Once a dependency is in the `READY` state, other services (like `Inference` or `Embeddings`) can use it.
