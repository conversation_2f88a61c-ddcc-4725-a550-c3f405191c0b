"""
File Timestamp Manager Utility

This module provides utilities for managing file modification timestamps
for the knowledge base system to optimize incremental updates and avoid
unnecessary reprocessing.

Key Features:
- Get file modification timestamps in milliseconds
- Compare timestamps to detect changes
- Batch timestamp operations for efficiency
- Cross-platform path normalization
- Thread-safe operations
- Comprehensive error handling
"""

import os
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

from client_server.core.logger import LOGGER


# Thread-safe lock for file operations
_file_lock = threading.Lock()


def get_file_modification_time(file_path: str) -> int:
    """
    Get file modification timestamp in milliseconds.
    
    Args:
        file_path: Path to the file
        
    Returns:
        File modification timestamp in milliseconds, or 0 if file doesn't exist or error occurs
        
    Raises:
        None - errors are logged and 0 is returned for graceful handling
    """
    try:
        with _file_lock:
            path = Path(file_path)
            if not path.exists():
                LOGGER.debug(f"File does not exist: {file_path}")
                return 0
                
            if not path.is_file():
                LOGGER.debug(f"Path is not a file: {file_path}")
                return 0
                
            # Get modification time in seconds and convert to milliseconds
            mtime_seconds = path.stat().st_mtime
            mtime_milliseconds = int(mtime_seconds * 1000)
            
            LOGGER.debug(f"File modification time for '{file_path}': {mtime_milliseconds}ms")
            return mtime_milliseconds
            
    except PermissionError:
        LOGGER.warning(f"Permission denied accessing file: {file_path}")
        return 0
    except OSError as e:
        LOGGER.warning(f"OS error accessing file '{file_path}': {e}")
        return 0
    except Exception as e:
        LOGGER.error(f"Unexpected error getting modification time for '{file_path}': {e}")
        return 0


def compare_timestamps(stored_timestamp: int, current_timestamp: int) -> bool:
    """
    Compare timestamps to detect changes.
    
    Args:
        stored_timestamp: Previously stored timestamp in milliseconds
        current_timestamp: Current file timestamp in milliseconds
        
    Returns:
        True if current timestamp is newer than stored timestamp, False otherwise
    """
    if stored_timestamp == 0 or current_timestamp == 0:
        # If either timestamp is 0 (error/missing), consider it modified
        return True
        
    return current_timestamp > stored_timestamp


def batch_update_timestamps(file_paths: List[str]) -> Dict[str, int]:
    """
    Get timestamps for multiple files efficiently using parallel processing.
    
    Args:
        file_paths: List of file paths to process
        
    Returns:
        Dictionary mapping normalized file paths to their modification timestamps
    """
    if not file_paths:
        return {}
        
    LOGGER.debug(f"Starting batch timestamp update for {len(file_paths)} files")
    start_time = time.time()
    
    timestamps = {}
    
    # Use ThreadPoolExecutor for parallel file access
    max_workers = min(len(file_paths), os.cpu_count() or 4)
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_path = {
            executor.submit(get_file_modification_time, file_path): file_path
            for file_path in file_paths
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_path):
            file_path = future_to_path[future]
            try:
                timestamp = future.result()
                normalized_path = normalize_path_format(file_path)
                timestamps[normalized_path] = timestamp
                
                if timestamp > 0:
                    LOGGER.debug(f"Batch update: '{normalized_path}' -> {timestamp}ms")
                else:
                    LOGGER.debug(f"Batch update: '{normalized_path}' -> file not accessible")
                    
            except Exception as e:
                LOGGER.error(f"Error processing file '{file_path}' in batch update: {e}")
                normalized_path = normalize_path_format(file_path)
                timestamps[normalized_path] = 0
    
    processing_time = time.time() - start_time
    successful_files = sum(1 for ts in timestamps.values() if ts > 0)
    
    LOGGER.info(
        f"Batch timestamp update completed: {successful_files}/{len(file_paths)} files "
        f"processed successfully in {processing_time:.2f}s"
    )
    
    return timestamps


def normalize_path_format(file_path: str) -> str:
    """
    Convert paths to Unix format for consistent storage across platforms.
    
    Args:
        file_path: File path in any format
        
    Returns:
        Normalized path in Unix format (forward slashes)
    """
    if not file_path:
        return ""
        
    try:
        # Convert to Path object and then to string with forward slashes
        path = Path(file_path)
        
        # Convert to POSIX format (Unix-style with forward slashes)
        normalized = path.as_posix()
        
        LOGGER.debug(f"Path normalization: '{file_path}' -> '{normalized}'")
        return normalized
        
    except Exception as e:
        LOGGER.error(f"Error normalizing path '{file_path}': {e}")
        # Return original path if normalization fails
        return file_path


def is_file_accessible(file_path: str) -> bool:
    """
    Check if a file is accessible for reading.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if file exists and is readable, False otherwise
    """
    try:
        path = Path(file_path)
        return path.exists() and path.is_file() and os.access(path, os.R_OK)
    except Exception as e:
        LOGGER.debug(f"File accessibility check failed for '{file_path}': {e}")
        return False


def get_files_needing_update(
    file_paths: List[str], 
    stored_timestamps: Dict[str, int]
) -> List[str]:
    """
    Determine which files need to be updated based on timestamp comparison.
    
    Args:
        file_paths: List of file paths to check
        stored_timestamps: Dictionary of stored timestamps (normalized paths -> timestamps)
        
    Returns:
        List of file paths that need to be updated
    """
    if not file_paths:
        return []
        
    LOGGER.debug(f"Checking {len(file_paths)} files for updates")
    start_time = time.time()
    
    files_needing_update = []
    
    # Get current timestamps for all files
    current_timestamps = batch_update_timestamps(file_paths)
    
    for file_path in file_paths:
        normalized_path = normalize_path_format(file_path)
        current_timestamp = current_timestamps.get(normalized_path, 0)
        stored_timestamp = stored_timestamps.get(normalized_path, 0)
        
        if compare_timestamps(stored_timestamp, current_timestamp):
            files_needing_update.append(file_path)
            LOGGER.debug(
                f"File needs update: '{file_path}' "
                f"(stored: {stored_timestamp}ms, current: {current_timestamp}ms)"
            )
        else:
            LOGGER.debug(
                f"File up to date: '{file_path}' "
                f"(stored: {stored_timestamp}ms, current: {current_timestamp}ms)"
            )
    
    processing_time = time.time() - start_time
    LOGGER.info(
        f"Update check completed: {len(files_needing_update)}/{len(file_paths)} files "
        f"need updates (checked in {processing_time:.2f}s)"
    )
    
    return files_needing_update


def cleanup_stale_timestamps(
    stored_timestamps: Dict[str, int], 
    current_files: List[str]
) -> Dict[str, int]:
    """
    Remove timestamps for files that no longer exist in the current file list.
    
    Args:
        stored_timestamps: Dictionary of stored timestamps
        current_files: List of current file paths
        
    Returns:
        Cleaned dictionary with only timestamps for existing files
    """
    if not stored_timestamps:
        return {}
        
    # Normalize current file paths for comparison
    normalized_current_files = {normalize_path_format(fp) for fp in current_files}
    
    # Keep only timestamps for files that still exist
    cleaned_timestamps = {
        path: timestamp 
        for path, timestamp in stored_timestamps.items()
        if path in normalized_current_files
    }
    
    removed_count = len(stored_timestamps) - len(cleaned_timestamps)
    if removed_count > 0:
        LOGGER.info(f"Cleaned up {removed_count} stale timestamp entries")
        
    return cleaned_timestamps
