# import os
# from pathlib import Path
# import shutil
# import subprocess
# import sys
# from dotenv import load_dotenv

# from client_server.utils.platform_detector import PlatformDetector

# # Load environment variables for macOS signing
# load_dotenv()

# HERE = Path(__file__).parent.absolute()
# ROOT = HERE.parent.parent
# PATH_TO_MAIN = str(HERE / "server.py")

# # Constants for macOS signing
# APP_NAME = "server"


# class Prebuild:
#     """Class responsible for pre-building the application."""

#     def __init__(self):
#         """Initialize the pre-build class."""
#         pass

#     def run(self):
#         shutil.rmtree("build/", ignore_errors=True)
#         shutil.rmtree("dist/", ignore_errors=True)


# class Postbuild:
#     """Class responsible for post-building the application."""

#     def __init__(self):
#         """Initialize the pre-build class."""
#         pass

#     def run(self):
#         pass


# class Builder:
#     """Class responsible for building the application for different platforms."""

#     def __init__(self):
#         """Initialize the builder."""
#         self.platform = PlatformDetector.get_os_name()
#         self.arch = PlatformDetector.get_arch_name()
#         print(f"Detected platform: {self.platform}")
#         print(f"Architecture: {self.arch}")

#         # Create output directories
#         os.makedirs("dist", exist_ok=True)
#         os.makedirs("build", exist_ok=True)

#     def _find_tokenizer(self):
#         """Find the tokenizer file and return its path with the destination."""
#         venv_path = ROOT / ".venv"

#         if PlatformDetector.is_windows():
#             search_paths = [
#                 venv_path / "Lib" / "site-packages",
#                 ROOT / ".venv" / "Lib" / "site-packages",  # Try relative path
#                 Path(sys.prefix) / "Lib" / "site-packages",  # Try system Python path
#             ]
#         else:  # Linux/Unix/Darwin systems
#             search_paths = [
#                 venv_path / "lib" / "python3.11" / "site-packages",
#                 ROOT / ".venv" / "lib" / "python3.11" / "site-packages",
#                 Path(sys.prefix) / "lib" / "python3.11" / "site-packages",
#             ]

#         tokenizer_relative_path = Path(
#             "litellm/litellm_core_utils/tokenizers/anthropic_tokenizer.json"
#         )

#         for base_path in search_paths:
#             tokenizer_path = base_path / tokenizer_relative_path
#             if tokenizer_path.exists():
#                 print(f"Found tokenizer at: {tokenizer_path}")
#                 separator = ";" if PlatformDetector.is_windows() else ":"
#                 return (
#                     f"{tokenizer_path}{separator}litellm/litellm_core_utils/tokenizers"
#                 )

#         raise FileNotFoundError(
#             f"Could not find tokenizer file in any of these locations:\n"
#             + "\n".join(str(p / tokenizer_relative_path) for p in search_paths)
#         )

#     def _get_base_pyinstaller_args(
#         self, tokenizer_path: str, platform_name: str
#     ) -> list:
#         """Get the base PyInstaller arguments common to all platforms."""
#         return [
#             "pyinstaller",
#             PATH_TO_MAIN,
#             # "--onefile",
#             "--hidden-import=tiktoken_ext.openai_public",
#             "--hidden-import=tiktoken_expyinstaller",
#             "--hidden-import=tiktoken_ext",
#             f"--add-data={tokenizer_path}",
#             "--hidden-import=python-socketio",
#             "--hidden-import=socketio.asyncio_server",
#             f"--distpath=dist/{platform_name}",
#             f"--workpath=build/{platform_name}",
#             f"--specpath=build/{platform_name}",
#             f"--icon={ROOT}/assets/logo.ico",
#             "--noconsole",
#         ]

#     def _build_windows(self) -> bool:
#         """Build the executable for Windows."""
#         print("\nBuilding for Windows...")
#         platform_name = "windows"

#         try:
#             tokenizer_path = self._find_tokenizer()
#             pyinstaller_args = self._get_base_pyinstaller_args(
#                 tokenizer_path, platform_name
#             )

#             if PlatformDetector.is_amd64():
#                 pyinstaller_args.extend(["--target-arch", "x64"])
#             elif PlatformDetector.is_amd86():
#                 pyinstaller_args.extend(["--target-arch", "x86"])

#             subprocess.run(pyinstaller_args, check=True)
#             print("Build completed successfully for Windows!")
#             return True
#         except (subprocess.CalledProcessError, FileNotFoundError) as e:
#             print(f"Build failed for Windows with error: {e}", file=sys.stderr)
#             return False

#     def _build_linux(self) -> bool:
#         """Build the executable for Linux."""
#         print("\nBuilding for Linux...")
#         platform_name = "linux"

#         try:
#             tokenizer_path = self._find_tokenizer()
#             pyinstaller_args = self._get_base_pyinstaller_args(
#                 tokenizer_path, platform_name
#             )

#             if PlatformDetector.is_amd64():
#                 pyinstaller_args.extend(["--target-arch", "x86_64"])
#             elif PlatformDetector.is_aarch64():
#                 pyinstaller_args.extend(["--target-arch", "aarch64"])

#             subprocess.run(pyinstaller_args, check=True)
#             print("Build completed successfully for Linux!")
#             return True
#         except (subprocess.CalledProcessError, FileNotFoundError) as e:
#             print(f"Build failed for Linux with error: {e}", file=sys.stderr)
#             return False

#     def _build_darwin(self) -> bool:
#         """Build the executable for macOS."""
#         print("\nBuilding for macOS...")
#         platform_name = "darwin"

#         try:
#             tokenizer_path = self._find_tokenizer()
#             pyinstaller_args = self._get_base_pyinstaller_args(
#                 tokenizer_path, platform_name
#             )

#             if PlatformDetector.is_arm64():
#                 pyinstaller_args.extend(["--target-arch", "arm64"])
#             elif PlatformDetector.is_amd64():
#                 pyinstaller_args.extend(["--target-arch", "x86_64"])

#             subprocess.run(pyinstaller_args, check=True)
#             print("Build completed successfully for macOS!")
#             return True
#         except (subprocess.CalledProcessError, FileNotFoundError) as e:
#             print(f"Build failed for macOS with error: {e}", file=sys.stderr)
#             return False

#     def build(self) -> bool:
#         """Build the server executable for the current platform."""
#         platform_builders = {
#             "windows": self._build_windows,
#             "linux": self._build_linux,
#             "darwin": self._build_darwin,
#         }

#         builder = platform_builders.get(self.platform)
#         if not builder:
#             print(
#                 f"Error: Unsupported platform: {self.platform}",
#                 file=sys.stderr,
#             )
#             return False

#         return builder()


# class Signing:
#     """Class responsible for signing and notarizing binaries."""

#     def __init__(self):
#         """Initialize the signing class."""
#         self.team_id = os.getenv("APPLE_TEAM_ID")
#         self.developer_name = os.getenv("APPLE_DEVELOPER_NAME")
#         self.apple_id = os.getenv("APPLE_ID")
#         self.app_password = os.getenv("APPLE_APP_PASSWORD")

#     def _run_command(self, cmd: list[str], error_msg: str) -> None:
#         """Run a shell command and handle errors."""
#         try:
#             subprocess.run(cmd, check=True)
#         except subprocess.CalledProcessError as e:
#             print(f"Error: {error_msg}")
#             print(f"Command failed with error: {e}")
#             raise

#     def _sign_darwin_binary(self) -> bool:
#         """Sign the macOS binary and create a distributable zip."""

#         try:
#             if not all([self.team_id, self.developer_name]):
#                 print(
#                     "Warning: Missing Apple signing credentials in .env file. Skipping signing process."
#                 )
#                 return True

#             sign_id = (
#                 f"Developer ID Application: {self.developer_name} ({self.team_id})"
#             )
#             dist_path = ROOT / "dist" / "darwin"
#             binary_path = dist_path / "server"
#             entitlements_path = ROOT / "assets" / "runtime.entitlements"

#             print("🧼 Removing old code signatures...")
#             # Find and remove existing signatures from binaries and libraries
#             for root, _, files in os.walk(binary_path):
#                 for file in files:
#                     file_path = Path(root) / file
#                     if file_path.suffix in [".dylib", ".so"] or os.access(
#                         file_path, os.X_OK
#                     ):
#                         try:
#                             self._run_command(
#                                 ["codesign", "--remove-signature", str(file_path)],
#                                 f"Failed to remove signature from {file_path}",
#                             )
#                         except subprocess.CalledProcessError:
#                             # Ignore errors when removing signatures
#                             pass

#             print("🔏 Signing libraries...")
#             # Sign all dynamic libraries
#             for ext in [".dylib", ".so", ".framework"]:
#                 for root, _, files in os.walk(binary_path):
#                     for file in files:
#                         if file.endswith(ext):
#                             file_path = Path(root) / file
#                             self._run_command(
#                                 [
#                                     "codesign",
#                                     "--force",
#                                     "--timestamp",
#                                     "--options",
#                                     "runtime",
#                                     "--entitlements",
#                                     str(entitlements_path),
#                                     "--sign",
#                                     sign_id,
#                                     str(file_path),
#                                 ],
#                                 f"Failed to sign library {file_path}",
#                             )

#             print("🚀 Signing main executable/folder...")
#             self._run_command(
#                 [
#                     "codesign",
#                     "--force",
#                     "--timestamp",
#                     "--options",
#                     "runtime",
#                     "--entitlements",
#                     str(entitlements_path),
#                     "--deep",
#                     "--sign",
#                     sign_id,
#                     str(binary_path),
#                 ],
#                 "Failed to sign binary",
#             )

#             print("✅ Verifying local code signature...")
#             self._run_command(
#                 [
#                     "codesign",
#                     "--verify",
#                     "--deep",
#                     "--strict",
#                     "--verbose=2",
#                     str(binary_path),
#                 ],
#                 "Code signature verification failed",
#             )

#             # Create zip for distribution
#             print("🤐 Creating distributable zip archive...")
#             ZIP_NAME = ROOT / "dist" / "darwin" / f"{APP_NAME}.zip"
#             if ZIP_NAME.exists():
#                 ZIP_NAME.unlink()

#             # Create zip directly from the dist folder contents
#             self._run_command(
#                 ["ditto", "-c", "-k", str(binary_path) + "/", str(ZIP_NAME)],
#                 "Failed to create zip archive",
#             )

#             print("🔏 Signing the zip archive...")
#             self._run_command(
#                 [
#                     "codesign",
#                     "--timestamp",
#                     "--sign",
#                     sign_id,
#                     str(ZIP_NAME),
#                 ],
#                 "Failed to sign zip archive",
#             )

#             print("✅ Code signing and zip creation completed successfully!")
#             return True

#         except Exception as e:
#             print(f"❌ Code signing failed: {e}", file=sys.stderr)
#             return False

#     def sign(self):
#         """Sign the binary."""
#         if PlatformDetector.is_darwin():
#             return self._sign_darwin_binary()
#         else:
#             return True


# def main():
#     """Build the server executable."""

#     Prebuild().run()

#     builder_success = Builder().build()
#     if not builder_success:
#         print("❌ Build failed", file=sys.stderr)
#         sys.exit(1)

#     signing_success = Signing().sign()
#     if not signing_success:
#         print("❌ Signing failed", file=sys.stderr)
#         sys.exit(1)

#     Postbuild().run()


# if __name__ == "__main__":
#     main()


import os
from pathlib import Path
import shutil
import subprocess
import sys
from dotenv import load_dotenv

from client_server.utils.platform_detector import PlatformDetector

# Load environment variables (still useful if you ever re-enable signing)
load_dotenv()

HERE = Path(__file__).parent.absolute()
ROOT = HERE.parent.parent
PATH_TO_MAIN = str(HERE / "server.py")
APP_NAME = "server"


class Prebuild:
    def run(self):
        shutil.rmtree("build/", ignore_errors=True)
        shutil.rmtree("dist/", ignore_errors=True)


class Postbuild:
    def run(self):
        pass


class Builder:
    def __init__(self, python_version: str):
        self.platform = PlatformDetector.get_os_name()
        self.arch = PlatformDetector.get_arch_name()
        self.pyver = python_version
        print(f"Detected platform: {self.platform}")
        print(f"Architecture: {self.arch}")
        print(f"Using Python version: {self.pyver}")
        os.makedirs("dist", exist_ok=True)
        os.makedirs("build", exist_ok=True)

    def _find_tokenizer(self):
        venv = ROOT / ".venv"
        verpath = f"python{self.pyver}"
        if PlatformDetector.is_windows():
            search = [
                venv / "Lib" / "site-packages",
                ROOT / ".venv" / "Lib" / "site-packages",
                Path(sys.prefix) / "Lib" / "site-packages",
            ]
        else:
            search = [
                venv / "lib" / verpath / "site-packages",
                ROOT / ".venv" / "lib" / verpath / "site-packages",
                Path(sys.prefix) / "lib" / verpath / "site-packages",
            ]

        rel = Path("litellm/litellm_core_utils/tokenizers/anthropic_tokenizer.json")
        for base in search:
            tok = base / rel
            if tok.exists():
                sep = ";" if PlatformDetector.is_windows() else ":"
                print(f"Found tokenizer at: {tok}")
                return f"{tok}{sep}litellm/litellm_core_utils/tokenizers"

        raise FileNotFoundError(
            f"Tokenizer not found in:\n" + "\n".join(str(p / rel) for p in search)
        )

    def _get_common_args(self, tok_path, plat):
        return [
            "pyinstaller",
            PATH_TO_MAIN,
            "--hidden-import=tiktoken_ext.openai_public",
            "--hidden-import=tiktoken_expyinstaller",
            "--hidden-import=tiktoken_ext",
            "--hidden-import=uvicorn.logging",
            f"--add-data={tok_path}",
            "--hidden-import=python-socketio",
            "--hidden-import=socketio.asyncio_server",
            f"--distpath=dist/{plat}",
            f"--workpath=build/{plat}",
            f"--specpath=build/{plat}",
            f"--icon={ROOT}/assets/logo.ico",
            "--noconsole",
        ]

    def _build_windows(self):
        print("\nBuilding for Windows...")
        plat = "windows"
        args = self._get_common_args(self._find_tokenizer(), plat)
        if PlatformDetector.is_amd64():
            args += ["--target-arch", "x64"]
        elif PlatformDetector.is_amd86():
            args += ["--target-arch", "x86"]
        subprocess.run(args, check=True)
        print("Windows build OK")
        return True

    def _build_linux(self):
        print("\nBuilding for Linux...")
        plat = "linux"
        args = self._get_common_args(self._find_tokenizer(), plat)
        if PlatformDetector.is_amd64():
            args += ["--target-arch", "x86_64"]
        elif PlatformDetector.is_aarch64():
            args += ["--target-arch", "aarch64"]
        subprocess.run(args, check=True)
        print("Linux build OK")
        return True

    def _build_darwin(self):
        print("\nBuilding for macOS...")
        plat = "darwin"
        args = self._get_common_args(self._find_tokenizer(), plat)
        if PlatformDetector.is_arm64():
            args += ["--target-arch", "arm64"]
        elif PlatformDetector.is_amd64():
            args += ["--target-arch", "x86_64"]
        subprocess.run(args, check=True)
        print("macOS build OK")
        return True

    def build(self):
        builders = {
            "windows": self._build_windows,
            "linux": self._build_linux,
            "darwin": self._build_darwin,
        }
        fn = builders.get(self.platform)
        print(fn)
        if not fn:
            print(f"Unsupported platform: {self.platform}", file=sys.stderr)
            return False
        return fn()


class Signing:
    def sign(self):
        if PlatformDetector.is_darwin():
            # SKIP ALL CODESIGN STEPS on macOS
            print("🔒 Skipping code signing on macOS")
            # Just zip up the dist folder
            dist_dir = ROOT / "dist" / "darwin" / APP_NAME
            zip_path = ROOT / "dist" / "darwin" / f"{APP_NAME}.zip"
            if zip_path.exists():
                zip_path.unlink()
            subprocess.run(
                ["ditto", "-c", "-k", str(dist_dir) + "/", str(zip_path)], check=True
            )
            print(f"📦 Created zip at {zip_path}")
            return True
        # Non-macOS: no signing needed
        return True


def main():
    # 1) Ask for the python version once
    pyver = input("Enter Python version (e.g. 3.11): ").strip() or "3.11"

    # 2) Clean
    Prebuild().run()

    # 3) Build
    if not Builder(pyver).build():
        print("❌ Build failed", file=sys.stderr)
        sys.exit(1)

    # 4) Zip (no codesign on macOS)
    if not Signing().sign():
        print("❌ Packaging failed", file=sys.stderr)
        sys.exit(1)

    # 5) Post
    Postbuild().run()


if __name__ == "__main__":
    main()
