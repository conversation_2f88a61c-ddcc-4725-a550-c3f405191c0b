from pathlib import Path
from typing import Generator, Any
import zipfile
import requests
from overrides import override

from client_server.core.logger import LOGGER
from . import IDependency, DependencyStatus
from .registry import DependencyRegistry


# Registry of available models and their URLs
MODEL_REGISTRY: dict[str, str] = {
    "all": "https://huggingface.co/datasets/codemateai/subsystem/resolve/main/models.zip?download=true",
}


class InferXModelDependency(IDependency):
    def __init__(
        self,
        target_dir: Path,
        model_name: str,
        *,
        id: str | None = None,
        name: str | None = None,
        version: str | None = None,
        description: str | None = None,
    ):
        super().__init__()
        if model_name not in MODEL_REGISTRY:
            raise ValueError(
                f"Unknown model: {model_name}. Available models: {', '.join(MODEL_REGISTRY.keys())}"
            )

        self.model_name = model_name
        self.target_dir = target_dir
        self.models_dir = target_dir / "models"

        # Store properties
        self._id = id or f"inferx_model_{self.model_name.replace('/', '_')}"
        self._name = name or self.model_name
        self._version = version or "latest"
        self._description = (
            description or f"InferX model {model_name} for specialized inference tasks."
        )

    @classmethod
    def list_available_models(cls) -> dict[str, str]:
        """Return a dictionary of available models and their descriptions."""
        return cls.MODEL_REGISTRY.copy()

    def _get_download_url(self) -> str:
        """Get the download URL for the specific model."""
        return MODEL_REGISTRY[self.model_name]

    def _check_model_exists(self) -> bool:
        """Check if the model exists in the models directory."""
        model_path = self.models_dir / self.model_name
        return model_path.exists() and any(model_path.iterdir())

    def _download_and_extract_model(
        self,
    ) -> Generator[tuple[DependencyStatus, Any], None, None]:
        """Download and extract the model to the target directory."""
        self.models_dir.mkdir(parents=True, exist_ok=True)
        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, f"Creating models directory for {self.model_name}"

        download_url = self._get_download_url()
        archive_path = self.target_dir / f"{self.model_name}.zip"

        # Download the file with progress bar
        status = DependencyStatus.INSTALLING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, f"Downloading {self.model_name}"
        response = requests.get(download_url, stream=True)
        response.raise_for_status()

        total_size = int(response.headers.get("content-length", 0))
        block_size = 8192
        downloaded_size = 0

        with open(archive_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=block_size):
                size = f.write(chunk)
                downloaded_size += size
                progress = (downloaded_size / total_size) * 100
                yield status, progress

        # Extract the archive
        yield status, f"Extracting {self.model_name} archive"
        model_extract_path = self.target_dir
        model_extract_path.mkdir(parents=True, exist_ok=True)

        with zipfile.ZipFile(archive_path, "r") as zip_ref:
            file_list = zip_ref.namelist()
            for i, file in enumerate(file_list):
                zip_ref.extract(file, model_extract_path)
                progress = ((i + 1) / len(file_list)) * 100
                yield status, progress
        # Clean up the archive
        archive_path.unlink()
        status = DependencyStatus.INSTALLED
        DependencyRegistry.update_status(self._id, status.value)
        yield status, f"Model {self.model_name} installation complete"

    @property
    @override
    def id(self) -> str:
        return self._id

    @property
    @override
    def name(self) -> str:
        return self._name

    @property
    @override
    def version(self) -> str:
        return self._version

    @property
    @override
    def description(self) -> str:
        return self._description

    @override
    def get_status(self) -> DependencyStatus:
        """Get the current status of the InferX model."""
        # Use DependencyRegistry to get the stored status
        status_str = DependencyRegistry.get_status(self._id)

        # If status is None, determine it and update
        if status_str is None:
            try:
                if not self.models_dir.exists():
                    status = DependencyStatus.MISSING
                else:
                    status = DependencyStatus.READY

                # Update the status in the registry
                DependencyRegistry.update_status(self._id, status.value)
                return status
            except Exception as e:
                LOGGER.error(f"Error checking model status: {e}")
                status = DependencyStatus.ERROR
                DependencyRegistry.update_status(self._id, status.value)
                return status
        else:
            # Convert string status to enum
            return DependencyStatus(status_str)

    @override
    def install(self) -> Generator[tuple[DependencyStatus, Any], None, None]:
        """Install the InferX model."""
        status = DependencyStatus.INSTALLING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, f"Starting installation of {self.model_name}"

        try:
            yield from self._download_and_extract_model()
            status = DependencyStatus.INSTALLED
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"InferX model {self.model_name} installation complete"

            status = DependencyStatus.READY
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"InferX model {self.model_name} is ready to use"
        except Exception as e:
            LOGGER.error(f"Failed to install InferX model {self.model_name}: {e}")
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to install InferX model {self.model_name}: {e}"

    @override
    def uninstall(self) -> Generator[tuple[DependencyStatus, Any], None, None]:
        """Uninstall InferX while preserving the models folder."""
        if not self.target_dir.exists():
            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "InferX is not installed"
            return

        status = DependencyStatus.PREPARING
        DependencyRegistry.update_status(self._id, status.value)
        yield status, "Removing InferX installation while preserving models"

        try:
            import shutil
            import tempfile

            # Create a temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir_path = Path(temp_dir)
                models_dir = self.target_dir / "models"

                # If models directory exists, move it to temp location
                if models_dir.exists():
                    temp_models_dir = temp_dir_path / "models"
                    shutil.move(str(models_dir), str(temp_models_dir))
                    yield status, "Preserving models folder"

                # Delete the entire target directory
                shutil.rmtree(self.target_dir)

                # Recreate the target directory
                self.target_dir.mkdir(parents=True)

                # If we had models, move them back
                if models_dir.exists():
                    shutil.move(str(temp_models_dir), str(models_dir))
                    yield status, "Restoring models folder"

            status = DependencyStatus.MISSING
            DependencyRegistry.update_status(self._id, status.value)
            yield status, "InferX has been uninstalled (models preserved)"
        except Exception as e:
            status = DependencyStatus.ERROR
            DependencyRegistry.update_status(self._id, status.value)
            yield status, f"Failed to uninstall InferX: {e}"

    @override
    def reinstall(self) -> Generator[tuple[DependencyStatus, Any], None, None]:
        """Reinstall the InferX model by first uninstalling and then installing."""
        yield from self.uninstall()
        yield from self.install()

    @override
    def ensure(self) -> Generator[tuple[DependencyStatus, Any], None, None]:
        """
        Ensure the InferX model is installed and ready.
        First reports the current status, then handles installation if needed.
        Yields status updates during the process.

        Yields:
            tuple[DependencyStatus, Any]: Status updates with associated data
        """

        match self.get_status():
            case DependencyStatus.INSTALLED:
                yield DependencyStatus.INSTALLED, "InferX model is already installed and ready"
                return
            case DependencyStatus.MISSING | DependencyStatus.ERROR:
                yield DependencyStatus.MISSING, "InferX model is not installed"
                yield from self.install()
            case DependencyStatus.CORRUPTED:
                yield DependencyStatus.CORRUPTED, "InferX model is corrupted"
                yield from self.reinstall()
