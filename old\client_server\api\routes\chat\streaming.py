"""
HTTP streaming functionality for chat.

This module contains the HTTPStreamEventEmitter class and related
functionality for handling Server-Sent Events (SSE) streaming.
"""

import json
import time
from typing import Any

from .models import (
    ToolCallRequestEvent,
    ToolCallExecutionEvent,
    ToolCallResultEvent,
    ToolCallErrorEvent
)


class HTTPStreamEventEmitter:
    """
    Replaces socketio.emit functionality with Server-Sent Events (SSE) for HTTP streaming.
    Formats events as SSE data that can be streamed to the client.
    """

    def __init__(self, request_id: str):
        self.request_id = request_id
        self.events = []

    async def emit(self, event_name: str, data: dict[str, Any], to: str = None):
        """
        Emit an event by adding it to the events list.
        This replaces the socketio.emit functionality.
        """
        event_data = {
            "event": event_name,
            "data": data,
            "timestamp": time.time()
        }
        self.events.append(event_data)

    def format_as_sse(self, event_data: dict[str, Any]) -> str:
        """Format event data as Server-Sent Events (SSE) format"""
        event_name = event_data.get("event", "message")
        data = event_data.get("data", {})

        # Format as SSE
        sse_data = f"event: {event_name}\ndata: {json.dumps(data)}\n\n"
        return sse_data

    def get_events(self) -> list[dict[str, Any]]:
        """Get all accumulated events"""
        return self.events.copy()

    def clear_events(self):
        """Clear all accumulated events"""
        self.events.clear()

    # -----------------------------------------------------------------------------
    # Tool Call Event Helper Methods
    # -----------------------------------------------------------------------------

    async def emit_tool_call_request(self, tool_call_id: str, tool_name: str, tool_args: dict[str, Any]) -> str:
        """
        Emit a tool call request event and return SSE formatted string.

        Args:
            tool_call_id: Unique identifier for the tool call
            tool_name: Name of the tool being called
            tool_args: Arguments passed to the tool

        Returns:
            SSE formatted string for the event
        """
        event_data = ToolCallRequestEvent(
            request_id=self.request_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            tool_args=tool_args,
            timestamp=time.time()
        )

        await self.emit("tool_call_request", data=event_data.model_dump())
        return self.format_as_sse({
            "event": "tool_call_request",
            "data": event_data.model_dump()
        })

    async def emit_tool_call_execution(self, tool_call_id: str, tool_name: str) -> str:
        """
        Emit a tool call execution event and return SSE formatted string.

        Args:
            tool_call_id: Unique identifier for the tool call
            tool_name: Name of the tool being executed

        Returns:
            SSE formatted string for the event
        """
        event_data = ToolCallExecutionEvent(
            request_id=self.request_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            status="executing",
            timestamp=time.time()
        )

        await self.emit("tool_call_execution", data=event_data.model_dump())
        return self.format_as_sse({
            "event": "tool_call_execution",
            "data": event_data.model_dump()
        })

    async def emit_tool_call_result(self, tool_call_id: str, tool_name: str,
                                   success: bool, result_summary: str = None,
                                   error_message: str = None) -> str:
        """
        Emit a tool call result event and return SSE formatted string.

        Args:
            tool_call_id: Unique identifier for the tool call
            tool_name: Name of the tool that was executed
            success: Whether the tool call was successful
            result_summary: Optional summary of the results
            error_message: Optional error message if failed

        Returns:
            SSE formatted string for the event
        """
        event_data = ToolCallResultEvent(
            request_id=self.request_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            status="success" if success else "error",
            result_summary=result_summary,
            error_message=error_message,
            timestamp=time.time()
        )

        await self.emit("tool_call_result", data=event_data.model_dump())
        return self.format_as_sse({
            "event": "tool_call_result",
            "data": event_data.model_dump()
        })

    async def emit_tool_call_error(self, tool_call_id: str, tool_name: str, error_message: str) -> str:
        """
        Emit a tool call error event and return SSE formatted string.

        Args:
            tool_call_id: Unique identifier for the tool call
            tool_name: Name of the tool that failed
            error_message: Error message describing the failure

        Returns:
            SSE formatted string for the event
        """
        event_data = ToolCallErrorEvent(
            request_id=self.request_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            error_message=error_message,
            timestamp=time.time()
        )

        await self.emit("tool_call_error", data=event_data.model_dump())
        return self.format_as_sse({
            "event": "tool_call_error",
            "data": event_data.model_dump()
        })
