#!/usr/bin/env python3
"""
Test script for the new cloud upload endpoint.
This script tests the upload_to_cloud Socket.IO event for uploading a KB to cloud using KB ID.
"""

import asyncio
import json
import time
from typing import Dict, Any

import socketio


# Configuration
SESSION_ID = "d4d38050-b68f-4ebd-83d5-09580b08afc9"
BASE_URL = "http://localhost:45214"


async def test_cloud_upload_endpoint():
    """Test the upload_to_cloud Socket.IO endpoint."""
    print("=" * 80)
    print("Testing Cloud Upload Endpoint")
    print("=" * 80)
    
    # Get KB ID from user
    print("Enter the Knowledge Base ID to upload:")
    kb_id = input("> ").strip()
    
    if not kb_id:
        print("❌ KB ID is required!")
        return
    
    # Create Socket.IO client
    print("\n1. Connecting to Socket.IO server...")
    sio = socketio.AsyncClient()
    
    # Event handlers
    upload_events = []
    
    @sio.event
    async def connect():
        print("   ✅ Connected to Socket.IO server")
    
    @sio.event
    async def disconnect():
        print("   🔌 Disconnected from Socket.IO server")
    
    @sio.on('upload_to_cloud:progress')
    async def on_upload_progress(data):
        print(f"   📈 Progress: {data.get('progress', 0)}% - {data.get('message', '')}")
        upload_events.append(('progress', data))
    
    @sio.on('upload_to_cloud:success')
    async def on_upload_success(data):
        print(f"   ✅ Upload Success: {data.get('message', '')}")
        upload_events.append(('success', data))
    
    @sio.on('upload_to_cloud:error')
    async def on_upload_error(data):
        print(f"   ❌ Upload Error: {data.get('message', '')}")
        upload_events.append(('error', data))
    
    try:
        # Connect to server
        await sio.connect(BASE_URL)
        await asyncio.sleep(1)  # Give connection time to establish
        
        # Prepare upload data
        upload_data = {
            'kb_id': kb_id,
            'request_id': f'test_upload_{int(time.time())}',
            'session': SESSION_ID,
            'sync_config': {
                'enabled': True,
                'lastSynced': 0
            }
        }
        
        print(f"\n2. Sending upload_to_cloud event...")
        print(f"   Request ID: {upload_data['request_id']}")
        
        # Send the upload event
        await sio.emit('upload_to_cloud', upload_data)
        
        # Wait for response (with timeout)
        print("   ⏳ Waiting for upload to complete...")
        timeout = 300  # 5 minutes timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            await asyncio.sleep(1)
            
            # Check if we got a final result
            final_events = [e for e in upload_events if e[0] in ['success', 'error']]
            if final_events:
                break
        
        # Analyze results
        print(f"\n3. Upload Results:")
        print(f"   Total events received: {len(upload_events)}")
        
        for event_type, event_data in upload_events:
            if event_type == 'progress':
                continue  # Already printed during progress
            elif event_type == 'success':
                print(f"   ✅ UPLOAD SUCCESSFUL!")
                print(f"      Message: {event_data.get('message', '')}")
                if 'data' in event_data and event_data['data']:
                    kb_data = event_data['data']
                    print(f"      Updated KB Cloud ID: {kb_data.get('cloud_id', 'None')}")
                    print(f"      Sync Enabled: {kb_data.get('syncConfig', {}).get('enabled', False)}")
            elif event_type == 'error':
                print(f"   ❌ UPLOAD FAILED!")
                print(f"      Error: {event_data.get('message', '')}")
        
        if not upload_events:
            print("   ⚠️  No events received - check server logs")
        
    except Exception as e:
        print(f"   ❌ Test failed with exception: {e}")
    
    finally:
        await sio.disconnect()
    
    print("\n" + "=" * 80)
    print("Test completed")
    print("=" * 80)


def main():
    """Main function to run the test."""
    print("Cloud Upload Endpoint Test")
    print(f"Make sure the server is running on {BASE_URL}")
    print()
    
    try:
        asyncio.run(test_cloud_upload_endpoint())
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    main()
