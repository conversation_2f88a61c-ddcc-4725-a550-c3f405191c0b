from abc import ABC, abstractmethod
from typing import Coroutine


class ICrawlerBackend(ABC):
    @abstractmethod
    async def scrape(self, url: str) -> Coroutine[dict[str, str], None, None]:
        """
        Scrape a website starting from parent_url and save content to path.

        Args:
            url: The URL to start crawling from

        Returns:
            dict of {url: content}
        """
        pass
