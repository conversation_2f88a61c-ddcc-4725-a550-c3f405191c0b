import time
import asyncio
import httpx
import socketio
from socketio.exceptions import ConnectionRefusedError
from typing import Dict, Any

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_system_info
from client_server.core import constants
from client_server.core.state import G_BASE_URL, G_SESSION_ID

# Session state storage for reconnection recovery
_session_state: Dict[str, Dict[str, Any]] = {}


async def handle_connect_event(
    sio: socketio.AsyncServer, sid: str, environ: dict
) -> None:
    """Handle client connection event"""
    try:
        start_time = time.time()
        LOGGER.info(f"New client connection request - Session ID: {sid}")

        # Log system information on new connections
        log_system_info()

        # Log relevant environment information
        LOGGER.debug(f"Connection environment - Headers: {environ.get('headers', {})}")
        LOGGER.debug(
            f"Connection environment - Query String: {environ.get('QUERY_STRING', '')}"
        )

        # Check for reconnection from query parameters or headers
        query_string = environ.get("QUERY_STRING", "")
        is_reconnection = "reconnect=true" in query_string

        if is_reconnection:
            LOGGER.info(f"Detected reconnection attempt for session {sid}")
            await handle_reconnection_recovery(sio, sid)

        # Initialize session state
        _session_state[sid] = {
            "connected_at": time.time(),
            "last_activity": time.time(),
            "reconnect_count": _session_state.get(sid, {}).get("reconnect_count", 0)
            + (1 if is_reconnection else 0),
        }

        LOGGER.info(f"Client connection accepted - Session ID: {sid}")
        connection_time = time.time() - start_time
        LOGGER.debug(f"Connection established in {connection_time:.3f}s")

        # Emit connection confirmation with reconnection info
        await sio.emit(
            "connection_confirmed",
            {
                "session_id": sid,
                "is_reconnection": is_reconnection,
                "server_time": time.time(),
            },
            to=sid,
        )

    except Exception as e:
        LOGGER.error(f"Error establishing connection for session {sid}: {e}")
        raise ConnectionRefusedError(f"Connection error: {str(e)}")


async def handle_disconnect_event(sio: socketio.AsyncServer, sid: str, _: Any) -> None:
    """Handle client disconnection event"""
    try:
        LOGGER.info(f"Client disconnected - Session ID: {sid}")

        # Preserve session state for potential reconnection
        if sid in _session_state:
            _session_state[sid]["disconnected_at"] = time.time()
            _session_state[sid]["last_disconnect_reason"] = "client_disconnect"
            LOGGER.debug(f"Session state preserved for potential reconnection: {sid}")

        # Cancel any pending tasks for this session
        tasks = asyncio.all_tasks()
        cancelled_tasks = 0
        for task in tasks:
            if hasattr(task, "session_id") and task.session_id == sid:
                task.cancel()
                cancelled_tasks += 1

        LOGGER.debug(
            f"Cancelled {cancelled_tasks} tasks for session {sid} (reason: disconnect)"
        )
        LOGGER.info(f"Successfully cleaned up resources for session {sid}")

        # -------------------------------------------------------------
        # Report session duration to backend API
        # -------------------------------------------------------------
        try:
            # Get the session ID stored in thread-safe global state (set via /register_meta)
            session_id = G_SESSION_ID.get()
            if not session_id:
                LOGGER.warning(
                    "No session ID found in state. Skipping /session_duration reporting."
                )
            else:
                # Compute the session duration (in seconds) if we have a connection start time
                connected_at = _session_state.get(sid, {}).get("connected_at")
                if connected_at:
                    duration = int(time.time() - connected_at)
                else:
                    duration = None

                if duration is None:
                    LOGGER.warning(
                        f"Unable to determine session duration for sid {sid}. Skipping report."
                    )
                else:
                    base_url = G_BASE_URL.get().general
                    LOGGER.info(
                        f"Reporting session duration {duration}s for session {session_id} to {base_url}/session_duration"
                    )
                    async with httpx.AsyncClient(
                        verify=constants.SSL_CONTEXT
                    ) as client:
                        await client.post(
                            f"{base_url}/session_duration",
                            json={"duration": duration},
                            headers={"x-session": session_id},
                            timeout=10,
                        )
        except Exception as e:
            LOGGER.error(f"Error reporting session duration for session {sid}: {e}")

    except Exception as e:
        LOGGER.error(f"Error cleaning up resources for session {sid}: {e}")


async def handle_reconnection_recovery(sio: socketio.AsyncServer, sid: str) -> None:
    """Handle reconnection recovery logic"""
    try:
        if sid in _session_state:
            last_state = _session_state[sid]
            disconnect_duration = time.time() - last_state.get("disconnected_at", 0)

            LOGGER.info(
                f"Reconnection recovery for session {sid} after {disconnect_duration:.2f}s"
            )

            # Emit reconnection status to client
            await sio.emit(
                "reconnection_status",
                {
                    "recovered": True,
                    "disconnect_duration": disconnect_duration,
                    "reconnect_count": last_state.get("reconnect_count", 0),
                },
                to=sid,
            )
        else:
            LOGGER.debug(f"No previous session state found for {sid}")

    except Exception as e:
        LOGGER.error(f"Error during reconnection recovery for session {sid}: {e}")


# Cleanup old session states periodically
async def cleanup_old_sessions() -> None:
    """Clean up old session states to prevent memory leaks"""
    current_time = time.time()
    cleanup_threshold = constants.SOCKETIO_SESSION_TIMEOUT

    sessions_to_remove = []
    for sid, state in _session_state.items():
        last_activity = state.get("disconnected_at", state.get("last_activity", 0))
        if current_time - last_activity > cleanup_threshold:
            sessions_to_remove.append(sid)

    for sid in sessions_to_remove:
        del _session_state[sid]
        LOGGER.debug(f"Cleaned up old session state: {sid}")

    if sessions_to_remove:
        LOGGER.info(f"Cleaned up {len(sessions_to_remove)} old session states")


# Session activity tracking
def update_session_activity(sid: str) -> None:
    """Update last activity timestamp for a session"""
    if sid in _session_state:
        _session_state[sid]["last_activity"] = time.time()


# Get session statistics
def get_session_stats() -> dict:
    """Get current session statistics"""
    current_time = time.time()
    active_sessions = 0
    total_sessions = len(_session_state)

    for sid, state in _session_state.items():
        if "disconnected_at" not in state:
            active_sessions += 1

    return {
        "total_sessions": total_sessions,
        "active_sessions": active_sessions,
        "disconnected_sessions": total_sessions - active_sessions,
    }
