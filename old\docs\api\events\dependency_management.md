# Dependency Management Events (`dependency_install.py`, `dependency_uninstall.py`)

These modules handle the installation and uninstallation of dependencies required for certain features, particularly for running local models in `ECO` mode.

## Overview

The client can request to install or uninstall a list of dependencies. The server processes these requests and streams status updates back to the client. This allows the user to manage optional components of the application.

A central `DependencyRegistry` is used to look up the correct handler for each dependency ID.

---

## Dependency Installation (`dependency_install.py`)

This handler manages the installation of dependencies.

### Data Models

- `DependencyInstallPayload`:
  - `request_id: str`: A unique ID for the installation request.
  - `dependencies: list[str]`: A list of dependency IDs to install.

### Event Handler

- `handle_dependency_install_event(sio, sid, data)`:
  - Receives a list of dependencies to install.
  - For each dependency, it finds the corresponding manager in the `DependencyRegistry` and calls its `ensure()` method.
  - The `ensure()` method will check if the dependency is already installed and, if not, download and set it up.
  - It streams the following events to the client:
    - `dependency_install:start`: Signals the start of the process.
    - `dependency_install:status`: Provides real-time updates on the installation progress (e.g., downloading, installing, success, error).
    - `dependency_install:end`: Signals the end of the entire installation process.

### Event Flow

<details>
<summary>View Dependency Install Flow</summary>

```mermaid
sequenceDiagram
    participant E as User's Editor
    participant S as Our Server
    participant I as Installer

    E->>S: User requests to install a feature
    S->>S: Start the installation process
    S-->>E: Notify "Installation started..."
    loop For each required component
        S->>I: Tell installer to get the component
        I-->>S: Installer reports its progress
        S-->>E: Show progress to user (e.g., "Downloading...")
        I-->>S: Installer reports it's finished
        S-->>E: Show user "Component installed!"
    end
    S-->>E: Notify "Installation finished"
```

</details>

---

## Dependency Uninstallation (`dependency_uninstall.py`)

This handler manages the removal of dependencies.

### Data Models

- `DependencyUninstallPayload`:
  - `request_id: str`: A unique ID for the uninstallation request.
  - `dependencies: list[str]`: A list of dependency IDs to uninstall.

### Event Handler

- `handle_dependency_uninstall_event(sio, sid, data)`:
  - Receives a list of dependencies to uninstall.
  - For each dependency, it finds the corresponding manager and calls its `uninstall()` method.
  - It streams events (`dependency_uninstall:start`, `dependency_uninstall:status`, `dependency_uninstall:end`) to report the progress and result of the uninstallation.

### Event Flow

<details>
<summary>View Dependency Uninstall Flow</summary>

```mermaid
sequenceDiagram
    participant E as User's Editor
    participant S as Our Server
    participant U as Uninstaller

    E->>S: User requests to uninstall a feature
    S->>S: Start the uninstallation process
    S-->>E: Notify "Uninstallation started..."
    loop For each component to remove
        S->>U: Tell uninstaller to remove component
        U-->>S: Uninstaller reports its status
        S-->>E: Show status to user (e.g., "Deleting files...")
    end
    S-->>E: Notify "Uninstallation finished"
```

</details>
