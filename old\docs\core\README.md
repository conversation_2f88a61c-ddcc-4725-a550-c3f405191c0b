# Core Module

The `core` module contains components decoupled from the rest of the business logic.

## Key Components

The `core` module is organized into the following key areas:

- **Constants**: Defines application-wide constants, from server port configurations to feature flags.
- **Logger**: A centralized logging system built on `loguru` for event tracking and error reporting.
- **State Management**: A thread-safe mechanism for managing global application state for handling concurrent operations and maintaining session information.
- **State Types**: Defines Pydantic models for structured state data, ensuring type safety and data integrity.

Detailed documentation for each component can be found in their respective markdown files.
