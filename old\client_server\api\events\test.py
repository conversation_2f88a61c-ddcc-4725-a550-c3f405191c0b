import time
from typing import Any

import socketio

from client_server.core.logger import LOGGER


async def handle_test_event(sio: socketio.AsyncServer, sid: str, data: dict[str, Any]):
    """Handle test event"""
    start_time = time.time()
    LOGGER.info(f"Processing test event for session {sid}")

    try:
        # Log the test data
        LOGGER.debug(f"Test event data: {data}")

        # Simulate some processing time
        await sio.sleep(1)

        # Emit test response
        await sio.emit(
            "test:response",
            {"message": "Test successful", "data": data},
            to=sid,
        )

        total_time = time.time() - start_time
        LOGGER.info(f"Test event completed successfully in {total_time:.2f}s")

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error in test event handler after {total_time:.2f}s: {e}")
        await sio.emit(
            "test:error",
            {"error": str(e)},
            to=sid,
        )
