import time
from fastapi import <PERSON><PERSON><PERSON>Exception
from pydantic import BaseModel, Field

from client_server.utils.actions.folder_search import _perform_folder_search
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_operation_stats
from . import route


class SearchRequest(BaseModel):
    """Request model for folder search"""

    query: str = Field(..., description="Search query string")
    kbid: str = Field(..., description="Knowledge base ID to search in")
    folder_path: str = Field(..., description="Path to the folder to search in")


@route("POST", "/search")
async def search(data: SearchRequest):
    """Search for content in a specific folder within a knowledge base"""
    start_time = time.time()
    LOGGER.info(
        f"Processing search request - Query: '{data.query}', "
        f"KB: {data.kbid}, Folder: {data.folder_path}"
    )

    try:
        # Perform the search
        search_start = time.time()
        response = await _perform_folder_search(
            query=data.query, index_name=data.kbid, folder_path=data.folder_path
        )
        search_time = time.time() - search_start

        result_count = len(response) if response else 0
        LOGGER.debug(
            f"Search completed - Found {result_count} results in {search_time:.3f}s"
        )

        if result_count > 0:
            LOGGER.debug(f"First result: {str(response[0])[:200]}...")

        # Log operation statistics
        total_time = time.time() - start_time
        stats = log_operation_stats(
            "folder_search", start_time, result_count, success=True
        )

        LOGGER.info(
            f"Search request completed - Results: {result_count}, "
            f"Search time: {search_time:.3f}s, Total time: {total_time:.3f}s"
        )

        return {"result": str(response[0]) if response else ""}

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error performing search after {total_time:.3f}s: {e}")
        raise HTTPException(status_code=500, detail=str(e))
