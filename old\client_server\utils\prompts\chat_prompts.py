from .prompt_loader import load_prompt


class ChatPrompts:
    """Message templates for chat operations"""

    @staticmethod
    def fast_thinking_prompt() -> str:
        """Prompt for fast thinking mode"""
        return load_prompt("chat", "fast_thinking")

    @staticmethod
    def slow_thinking_prompt() -> str:
        """Prompt for slow thinking mode"""
        return load_prompt("chat", "slow_thinking")

    @staticmethod
    def calculative_thinking_prompt() -> str:
        """Prompt for calculative thinking mode"""
        return load_prompt("chat", "calculative_thinking")

        
    @staticmethod
    def get_initial_messages(tag: str, conversation_messages: list[dict[str, str]]) -> list[dict[str, str]]:
        """
        Generate initial system messages for the conversation based on thinking mode

        Args:
            tag: Thinking mode tag (<fast_thinking>, <slow_thinking>, <calculative_thinking>)
            conversation_messages: List of conversation messages to process

        Returns:
            List of message dictionaries starting with system prompt followed by conversation messages
        """
        # Get appropriate system prompt based on tag
        if tag == "<fast_thinking>":
            system_prompt = ChatPrompts.fast_thinking_prompt()
        elif tag == "<slow_thinking>":
            system_prompt = ChatPrompts.slow_thinking_prompt()
        elif tag == "<calculative_thinking>":
            system_prompt = ChatPrompts.calculative_thinking_prompt()
        else:
            # Fallback to slow_thinking for unknown tags
            from client_server.core.logger import LOGGER
            LOGGER.warning(f"Unknown thinking mode tag '{tag}', falling back to slow_thinking")
            system_prompt = ChatPrompts.slow_thinking_prompt()

        # Initialize messages list with system prompt
        messages = [{"role": "system", "content": system_prompt}]

        # Add conversation messages maintaining order
        # Include user, assistant, and tool messages for complete conversation context
        for message in conversation_messages:
            if message["role"] in ["user", "assistant", "tool"]:
                # Preserve the complete message structure for tool messages
                if message["role"] == "tool":
                    messages.append({
                        "role": message["role"],
                        "tool_call_id": message.get("tool_call_id", ""),
                        "content": message.get("content", "")
                    })
                elif message["role"] == "assistant" and "tool_calls" in message:
                    # Preserve tool_calls in assistant messages
                    messages.append({
                        "role": message["role"],
                        "content": message.get("content", ""),
                        "tool_calls": message["tool_calls"]
                    })
                else:
                    # Standard user and assistant messages
                    messages.append({
                        "role": message["role"],
                        "content": message.get("content", "")
                    })

        return messages

    @staticmethod
    def get_followup_messages(conversation_messages: list[dict[str, str]]) -> list[dict[str, str]]:
        """
        Generate messages for follow-up question generation

        Args:
            conversation_messages: List of conversation messages to generate follow-ups for
        """
        system_prompt = load_prompt("chat", "followup_questions")

        # Build the messages array with system prompt, conversation context, and final instruction
        messages = [{"role": "system", "content": system_prompt}]

        # Add the conversation messages for context
        messages.extend(conversation_messages)

        # Add final instruction for follow-up generation
        messages.append({
            "role": "user",
            "content": "Generate follow-up questions for this conversation."
        })

        return messages
    

