"""
Command Line Interface for GitControl
"""

import argparse
import sys
from pathlib import Path
from typing import List

from .gitcontrol import GitControl, GitControlError
from .config import GitControlConfig


def format_repo_info(repos: List[dict]) -> None:
    """Format and display repository information"""
    if not repos:
        print("No git repositories found.")
        return
    
    print(f"\nFound {len(repos)} git repositories:\n")
    
    for repo in repos:
        print(f"📁 {repo['name']}")
        print(f"   Path: {repo['path']}")
        print(f"   Branch: {repo['branch']}")
        print(f"   Remote: {repo['remote']}")
        print(f"   Status: {repo['status']}")
        print(f"   Last Commit: {repo['last_commit']}")
        print()


def cmd_list(args) -> None:
    """List all git repositories"""
    git_control = GitControl()
    
    search_paths = args.paths if args.paths else None
    repos = git_control.list_git_repositories(search_paths)
    
    format_repo_info(repos)


def cmd_clone(args) -> None:
    """Clone a repository"""
    git_control = GitControl()
    
    success = git_control.clone_repository(
        repo_url=args.url,
        destination=args.destination,
        branch=args.branch,
        token=args.token
    )
    
    if success:
        print("✅ Repository cloned successfully!")
    else:
        print("❌ Failed to clone repository")
        sys.exit(1)


def cmd_pr(args) -> None:
    """Create a pull request"""
    git_control = GitControl()
    
    repo_path = args.path if args.path else "."
    
    success = git_control.create_pull_request(
        repo_path=repo_path,
        title=args.title,
        body=args.body or "",
        base_branch=args.base,
        head_branch=args.head
    )
    
    if success:
        print("✅ Pull request created successfully!")
    else:
        print("❌ Failed to create pull request")
        sys.exit(1)


def cmd_status(args) -> None:
    """Get repository status"""
    git_control = GitControl()
    
    repo_path = args.path if args.path else "."
    
    try:
        status = git_control.get_repository_status(repo_path)
        
        print(f"Repository Status: {Path(repo_path).absolute()}")
        print(f"Current Branch: {status['current_branch']}")
        
        if status.get('tracking_branch'):
            print(f"Tracking: {status['tracking_branch']}")
            
            if 'commits_ahead' in status:
                print(f"Commits Ahead: {status['commits_ahead']}")
            if 'commits_behind' in status:
                print(f"Commits Behind: {status['commits_behind']}")
        
        print(f"Has Changes: {'Yes' if status['has_changes'] else 'No'}")
        
        if status['changes']:
            print("\nChanged Files:")
            for change in status['changes']:
                print(f"  {change}")
                
    except GitControlError as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


def cmd_sync(args) -> None:
    """Sync repository with remote"""
    git_control = GitControl()
    
    repo_path = args.path if args.path else "."
    
    success = git_control.sync_repository(
        repo_path=repo_path,
        auto_pull=not args.no_pull,
        auto_push=args.push
    )
    
    if success:
        print("✅ Repository synced successfully!")
    else:
        print("❌ Failed to sync repository")
        sys.exit(1)


def cmd_config(args) -> None:
    """Manage configuration"""
    config = GitControlConfig()
    
    if args.action == 'set':
        if args.key and args.value:
            config.set(args.key, args.value)
            print(f"✅ Set {args.key} = {args.value}")
        else:
            print("❌ Both key and value are required for 'set' action")
            sys.exit(1)
            
    elif args.action == 'get':
        if args.key:
            value = config.get(args.key)
            if value is not None:
                print(f"{args.key} = {value}")
            else:
                print(f"❌ Key '{args.key}' not found")
                sys.exit(1)
        else:
            print("❌ Key is required for 'get' action")
            sys.exit(1)
            
    elif args.action == 'token':
        if args.provider and args.token:
            config.set_token(args.provider, args.token)
            print(f"✅ Set token for {args.provider}")
        else:
            print("❌ Both provider and token are required for 'token' action")
            sys.exit(1)


def main() -> None:
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="GitControl - Comprehensive Git Repository Management",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser(
        'list', 
        help='List all git repositories'
    )
    list_parser.add_argument(
        'paths', 
        nargs='*', 
        help='Directories to search for repositories'
    )
    list_parser.set_defaults(func=cmd_list)
    
    # Clone command
    clone_parser = subparsers.add_parser(
        'clone', 
        help='Clone a git repository'
    )
    clone_parser.add_argument(
        'url', 
        help='Repository URL to clone'
    )
    clone_parser.add_argument(
        '--destination', '-d',
        help='Destination directory'
    )
    clone_parser.add_argument(
        '--branch', '-b',
        help='Branch to clone'
    )
    clone_parser.add_argument(
        '--token', '-t',
        help='Authentication token'
    )
    clone_parser.set_defaults(func=cmd_clone)
    
    # Pull Request command
    pr_parser = subparsers.add_parser(
        'pr', 
        help='Create a pull request'
    )
    pr_parser.add_argument(
        'title', 
        help='Pull request title'
    )
    pr_parser.add_argument(
        '--body', '-b',
        help='Pull request body/description'
    )
    pr_parser.add_argument(
        '--path', '-p',
        help='Repository path (default: current directory)'
    )
    pr_parser.add_argument(
        '--base',
        help='Base branch (default: main)'
    )
    pr_parser.add_argument(
        '--head',
        help='Head branch (default: current branch)'
    )
    pr_parser.set_defaults(func=cmd_pr)
    
    # Status command
    status_parser = subparsers.add_parser(
        'status', 
        help='Get repository status'
    )
    status_parser.add_argument(
        '--path', '-p',
        help='Repository path (default: current directory)'
    )
    status_parser.set_defaults(func=cmd_status)
    
    # Sync command
    sync_parser = subparsers.add_parser(
        'sync', 
        help='Sync repository with remote'
    )
    sync_parser.add_argument(
        '--path', '-p',
        help='Repository path (default: current directory)'
    )
    sync_parser.add_argument(
        '--no-pull',
        action='store_true',
        help='Skip automatic pull'
    )
    sync_parser.add_argument(
        '--push',
        action='store_true',
        help='Automatically push commits'
    )
    sync_parser.set_defaults(func=cmd_sync)
    
    # Config command
    config_parser = subparsers.add_parser(
        'config', 
        help='Manage configuration'
    )
    config_subparsers = config_parser.add_subparsers(dest='action', help='Config actions')
    
    # Config set
    set_parser = config_subparsers.add_parser('set', help='Set configuration value')
    set_parser.add_argument('key', help='Configuration key')
    set_parser.add_argument('value', help='Configuration value')
    
    # Config get
    get_parser = config_subparsers.add_parser('get', help='Get configuration value')
    get_parser.add_argument('key', help='Configuration key')
    
    # Config token
    token_parser = config_subparsers.add_parser('token', help='Set authentication token')
    token_parser.add_argument('provider', choices=['github', 'gitlab', 'bitbucket'], help='Git provider')
    token_parser.add_argument('token', help='Authentication token')
    
    config_parser.set_defaults(func=cmd_config)
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        sys.exit(1)
    
    # Execute command
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
