# Knowledge Base Upload Event (`upload.py`)

The `upload.py` module manages the creation of knowledge bases. This is a complex, multi-step process that involves chunking source data, generating vector embeddings, storing the data locally, and optionally syncing it to the cloud.

## Overview

The user initiates the process by sending an `upload` event with data describing the knowledge base to be created (e.g., its name, type, and source path). The handler then orchestrates a pipeline to process this data, providing real-time progress updates to the client.

## Data Models

- `UploadData`: The main payload for the upload request. It extends the `QdrantKnowledgeBase` model with a `request_id` and `session` information.
- `UploadProgressData`: Used for sending progress updates to the client. Includes `status`, `progress`, and a `message`.
- `UploadResponseData`: Used for sending the final success or error response.

## Event Flow / Upload Pipeline

The `handle_upload_event` function orchestrates the following pipeline:

1.  **Validation and Pre-check**:

    - The incoming `UploadData` is validated.
    - It checks if a knowledge base with the same name already exists using `QdrantKnowledgeBase.exists()`. If it does, the process is aborted with an error.

2.  **Step 1: Chunking the Data (`_make_chunks`)**:

    - Based on the `upload_data.type` (`Codebase`, `Github`, `Docs`, `Swagger`), an appropriate chunker implementation is selected (e.g., `CodebaseChunker`).
    - The selected chunker processes the source data (e.g., iterating through files in a directory) and breaks it down into smaller, meaningful `QdrantKnowledgeBaseChunk`s.
    - **Progress**: Emits `upload:progress` events with the message `(1/3) Chunking files`.

3.  **Step 2: Generating Embeddings (`_fill_embeddings`)**:

    - The list of chunks from the previous step is passed to this function.
    - It uses an `EmbeddingInferenceBuilder` to get the appropriate backend for generating vector embeddings.
    - It processes the chunks in batches, calling the backend's `generate_batch` method to create a vector embedding for each chunk's content.
    - **Progress**: Emits `upload:progress` events with the message `(2/3) Generating embeddings`.

4.  **Step 3: Creating the Knowledge Base (`QdrantKnowledgeBase.from_chunks`)**:

    - With the chunks now containing their vector embeddings, this function creates the final `QdrantKnowledgeBase` object.
    - This involves persisting the chunks and their vectors into the local Qdrant vector database.
    - **Progress**: Emits `upload:progress` events with the message `(3/3) Creating knowledge base`.

5.  **Step 4: Cloud Sync (Optional)**:
    - If `upload_data.syncConfig.enabled` is `True`, the `kb.sync_to_cloud()` method is called.
    - This method handles the process of uploading the knowledge base (including chunks and vectors) to the cloud service.
    - **Progress**: Emits `upload:progress` events with the message `Preparing data to upload`.

## Final Response

- If all steps complete successfully, an `upload:success` event is emitted, containing the data of the newly created knowledge base.
- If any step in the pipeline fails, an `upload:error` event is emitted with a descriptive error message, and the process is halted.
