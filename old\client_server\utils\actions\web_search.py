import json
import requests
from typing import Any

from client_server.core.logger import LOGGER
from client_server.core.state import G_BASE_URL
from .utils import SearchReferences

# -----------------------------------------------------------------------------
# Perform web search
# -----------------------------------------------------------------------------


async def process_web_search(
    *, query: str, tool_id: str, session: str, search_references: SearchReferences
):
    """
    Process a web search request.

    Args:
        query: Search query string
        tool_id: ID of the tool making the request
        session: Session ID for authentication
        search_references: SearchReferences object to track search results

    Returns:
        Tuple of (message, search_references)
    """
    base_url = G_BASE_URL.get().general
    try:
        response = requests.post(
            f"{base_url}/web_search",
            json={"query": query},
            headers={
                "Content-Type": "application/json",
                "x-session": session,
            },
        )
        data = response.json()
        LOGGER.info(f"web_search_response: {data}")
        sources = data["data"]["sources"]

        for source in sources:
            search_references.add_search_result(
                path=source["url"], name=source["title"], content="", type="web"
            )

        message = [
            {
                "role": "assistant",
                "action": {"id": tool_id, "name": "web_search", "arguments": ""},
            },
            {
                "role": "action",
                "content": data["data"]["content"],
                "action_id": tool_id,
            },
        ]

        return message, search_references
    except Exception as e:
        LOGGER.error(f"Error in process_web_search: {e}")
        raise
