from concurrent.futures import Future, ThreadPoolExecutor
import os
import time
from itertools import chain
from typing import Any, Callable, Coroutine

from .utils import make_chunks_from_content


async def process_docs_chunks(
    urls: list[str],
    progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
) -> list[dict]:
    """Process documentation URLs and return chunks as simple dictionaries."""
    print("Processing Docs type knowledge base")
    print(f"Found {len(urls)} URLs for docs processing")

    progress_till_scraping = 70

    # For now, we'll create a simple mock scraper since the original uses complex dependencies
    # In a real implementation, you would replace this with your preferred web scraping solution
    scraped_contents = {}
    for url in urls:
        print(f"Processing URL: {url}")
        # Mock content - replace with actual scraping logic
        scraped_contents[url] = f"Mock content for {url}"
        if progress_callback:
            await progress_callback(progress_till_scraping)

    try:
        chunking_phase_start = time.time()
        print("Starting chunk creation phase")

        MAX_WORKERS = int(os.cpu_count() * 0.4)
        chunks_by_file = {}
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            chunk_futures = {}
            # Dispatch all the futures
            dispatch_start = time.time()
            print("Dispatching chunk creation tasks to thread pool")
            for url, content in scraped_contents.items():
                chunk_futures[url] = executor.submit(
                    make_chunks_from_content,
                    content,
                    "markdown",
                )
            dispatch_time = time.time() - dispatch_start
            print(f"Dispatched {len(chunk_futures)} chunk creation tasks in {dispatch_time:.2f}s")

            # Collect the results
            collection_start = time.time()
            completed_files = 0
            for i, (file, chunk_future) in enumerate(chunk_futures.items()):
                file_start = time.time()
                # Wait for the future to complete
                chunks_by_file[file] = chunk_future.result()
                file_time = time.time() - file_start
                completed_files += 1

                file_chunk_count = len(chunks_by_file[file])
                print(f"Completed chunking for file {completed_files}/{len(chunk_futures)}: {file} "
                      f"({file_chunk_count} chunks, took {file_time:.2f}s)")

                progress = i / len(chunk_futures) * 100
                print(f"Progress: {progress:.2f}%")
                if progress_callback:
                    await progress_callback(
                        progress_till_scraping
                        + progress * (100 - progress_till_scraping) / 100
                    )

            collection_time = time.time() - collection_start
            print(f"Chunk collection completed in {collection_time:.2f} seconds")

        total_chunks = len(list(chain(*chunks_by_file.values())))
        chunking_phase_time = time.time() - chunking_phase_start

        print(f"Chunk creation completed. Total chunks created: {total_chunks} in {chunking_phase_time:.2f}s")

        if total_chunks == 0:
            print("No chunks found after processing all files")
            return []

        # Log chunking statistics
        files_with_chunks = sum(
            1 for chunks_list in chunks_by_file.values() if len(chunks_list) > 0
        )
        files_without_chunks = len(chunks_by_file) - files_with_chunks
        avg_chunks_per_file = (
            total_chunks / files_with_chunks if files_with_chunks > 0 else 0
        )

        print(f"Chunking stats - Files with chunks: {files_with_chunks}, "
              f"Files without chunks: {files_without_chunks}, "
              f"Avg chunks per file: {avg_chunks_per_file:.1f}")

        return list(chain(*chunks_by_file.values()))

    except Exception as e:
        print(f"Error processing files: {e}")
        return []
