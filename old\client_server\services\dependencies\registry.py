import json
from pathlib import Path
from typing import List, Dict, Set, Any, Optional, TYPE_CHECKING

from client_server.utils.path_selector import PathSelector

# Use TYPE_CHECKING to avoid circular imports
if TYPE_CHECKING:
    from client_server.services.dependencies import IDependency

# -----------------------------------------------------------------------------
# Dependency registry
# -----------------------------------------------------------------------------


class DependencyRegistry:
    _dependencies: Dict[str, Any] = {}  # Using Any to avoid circular imports
    _prerequisites: Set[str] = set()
    _status_json_path: Path = PathSelector.get_base_path() / "dependencies.json"

    @staticmethod
    def register_dependency(
        dependency: Any,  # Using Any to avoid circular imports
        prerequisite: bool = False,
    ) -> None:
        DependencyRegistry._dependencies[dependency.id] = dependency
        if prerequisite:
            DependencyRegistry._prerequisites.add(dependency.id)

    @staticmethod
    def get_dependency_by_id(
        dependency_id: str,
    ) -> Optional[Any]:  # Using Any to avoid circular imports
        if dependency_id not in DependencyRegistry._dependencies:
            return None
        return DependencyRegistry._dependencies[dependency_id]

    @staticmethod
    def get_all_dependencies() -> List[Any]:  # Using Any to avoid circular imports
        return list(DependencyRegistry._dependencies.values())

    @staticmethod
    def get_prerequisites() -> List[Any]:  # Using Any to avoid circular imports
        return [
            DependencyRegistry._dependencies[dependency_id]
            for dependency_id in DependencyRegistry._prerequisites
        ]

    @staticmethod
    def get_status(dependency_id: str) -> str:
        if not DependencyRegistry._status_json_path.exists():
            return None
        with open(DependencyRegistry._status_json_path, "r") as f:
            dependencies = json.load(f)
        return dependencies.get(dependency_id, None)

    @staticmethod
    def update_status(dependency_id: str, status: str):
        # Create the file if it doesn't exist
        if not DependencyRegistry._status_json_path.exists():
            with open(DependencyRegistry._status_json_path, "w+") as f:
                json.dump({}, f)

        # Load the statuses
        with open(DependencyRegistry._status_json_path, "r") as f:
            dependencies = json.load(f)

        # Update the status
        dependencies[dependency_id] = status

        # Save the statuses
        with open(DependencyRegistry._status_json_path, "w+") as f:
            json.dump(dependencies, f)
