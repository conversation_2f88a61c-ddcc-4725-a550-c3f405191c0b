# Inference Service

The Inference service provides a standardized way to perform text generation with Large Language Models (LLMs). It abstracts the complexities of interacting with different model serving backends, whether they are local or remote. This service is used for features like chat and code generation.

## Design

- **`IInferenceBackend` Interface**: A contract that defines the essential methods for interacting with an LLM, such as generating and streaming responses.
- **Implementations**: Concrete classes that manage the lifecycle and request/response flow for specific inference engines.
  - `OllamaInferenceBackend`: Manages LLM inference using a local Ollama server.
  - `InferXInferenceBackend`: Manages LLM inference using the specialized InferX engine for Snapdragon ARM platforms.
- **`InferenceBuilder`**: A factory that selects the correct backend implementation based on the host system's architecture.

## Core Components

### `IInferenceBackend` Interface

Located in `client_server/services/inference/__init__.py`, this ABC defines the core methods:

- `generate(model, messages, **model_params)`: Performs a standard, blocking request-response generation.
- `stream(model, messages, **model_params)`: Initiates a streaming response, yielding chunks of text as they are generated by the model.
- `ensure_model(model)`: A crucial method that makes sure the requested model is available for inference. For some backends, this might trigger a download or load-into-memory operation.
- `dispose()`: Cleans up resources, such as terminating server processes.

### Implementations

1.  **`OllamaInferenceBackend` (`ollama_handler.py`)**

    - This is the backend for non-ARM systems.
    - **Server Management**: It automatically starts and manages a background `ollama serve` process. The server's models are stored in the application's cache directory.
    - **Model Management**: The `ensure_model` method checks if a model exists locally using `ollama.show()`. If the model is not found, it automatically pulls it using `ollama.pull()`.
    - **Requests**: It uses the `litellm` library to make `completion` calls to the local Ollama server, abstracting the raw HTTP requests.

2.  **`InferXInferenceBackend` (`inferx_handler.py`)**
    - This backend is optimized for Snapdragon ARM platforms.
    - **Server Management**: Unlike the Ollama backend, which runs a single server, InferX starts a _separate server process for each model_ on a random available port. This is because each InferX process is tied to a specific model library. It keeps track of these processes in the `_model_processes` dictionary.
    - **Model Management**: `ensure_model` is responsible for starting a new `inferx.exe` process if one for the requested model isn't already running.
    - **Requests**: Like the Ollama backend, it uses `litellm` to interact with the model-specific server endpoint.
    - **Cleanup**: The `dispose` method is critical here for terminating all spawned `inferx.exe` processes.

### `InferenceBuilder`

This factory, located in `client_server/services/inference/utils.py`, simplifies backend creation.

- `create()`: This static method checks the platform using `PlatformDetector.is_snapdragon_arm()`. It returns a singleton instance of `InferXInferenceBackend` on ARM systems and `OllamaInferenceBackend` otherwise.
- `dispose()`: Calls the `dispose` method on the active backend instance, ensuring a clean shutdown.

## Workflow

1.  **Instantiation**: The application calls `InferenceBuilder.create()` to get the correct inference backend for the current platform.
2.  **Model Readiness**: Before making a generation request, `ensure_model(model_name)` is called. This might trigger a model download (Ollama) or start a new server process (InferX).
3.  **Generation**: The `generate()` or `stream()` method is called with the model name and messages. `litellm` handles the communication with the appropriate local server.
4.  **Cleanup**: When the application or session ends, `InferenceBuilder.dispose()` is called to terminate any running background processes.
