import asyncio
import httpx
from typing import Union
import constants

async def generate_cloud_embeddings(batch: bool, texts: Union[str, list[str]]) -> Union[list[float], list[list[float]]]:
    """Generate embeddings using cloud service."""
    if not batch:
        if isinstance(texts, str):
            texts = [texts]
        elif isinstance(texts, list) and len(texts) > 0:
            texts = [texts[0]]
        else:
            raise ValueError("Provide at least one text string.")
    elif isinstance(texts, str):
        texts = [texts]

    base_url = constants.embeddings
    error = None
    retry_count = 10

    while retry_count > 0:
        try:
            async with httpx.AsyncClient(verify=constants.SSL_CONTEXT) as client:
                response = await client.post(
                    f"{base_url}/generate",
                    json={"texts": texts},
                    timeout=30.0,
                )
            response.raise_for_status()
            body = response.json()
            embeddings = [item["embedding"] for item in body]

            return embeddings if batch else embeddings[0]

        except Exception as e:
            retry_count -= 1
            await asyncio.sleep(1)
            error = e
            print(f"[Cloud Retry] {retry_count} left. Error: {e}")

    raise error
