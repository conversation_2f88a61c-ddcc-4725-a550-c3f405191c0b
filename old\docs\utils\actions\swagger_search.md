# Swagger Search (`swagger_search.py`)

This module implements the `swagger_search` action, a sophisticated tool for searching within a knowledge base created from a Swagger or OpenAPI specification. It uses a two-stage process to identify the most relevant API endpoints for a given query.

## Two-Stage Search Process

1.  **Vector Search:** It first performs a standard semantic search on the Qdrant collection containing the endpoint data to find a set of potentially relevant endpoints.
2.  **LLM-based Filtering:** The results from the vector search are then passed to a secondary language model prompt (`query_api`). This model is tasked with analyzing the initial results and selecting the most relevant endpoint paths that best match the user's original query. This re-ranking step significantly improves the quality and relevance of the final results.

## Functions

### `process_swagger_search(*, query: str, tool_id: str, kbid: str, search_refrences: SearchReferences)`

The main entry point for the Swagger search action.

- It calls `_perform_swagger_search` to execute the two-stage search.
- It adds the final, filtered results to the `search_refrences` object.
- It formats the output into the standard action message format.

### `_perform_swagger_search(...)`

Orchestrates the search and filtering process.

- It performs a vector search on the Qdrant database to get an initial list of endpoints.
- It then calls the `query_api` function, passing the initial results to the language model for filtering.
- It uses the paths returned by `query_api` to select the final set of endpoint data to return.

### `query_api(query, results) -> list[str]`

This function interacts with a language model to refine the search results.

- It constructs a prompt containing the original query and the initial search results (formatted as XML).
- It sends this prompt to a dedicated `/swagger/search` API endpoint.
- It parses the response from the model, which contains a list of the most relevant endpoint paths.
- **Returns:**
  - `list[str]`: A list of endpoint paths selected by the language model.
