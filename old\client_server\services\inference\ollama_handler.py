import subprocess
import time
from pathlib import Path
from subprocess import <PERSON><PERSON>
from typing import Optional
import os
import socket

import litellm
import ollama
from overrides import override

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.utils.platform_detector import PlatformDetector
from . import IInferenceBackend


class OllamaInferenceBackend(IInferenceBackend):
    """
    A singleton interface for Ollama interactions.
    Automatically manages server lifecycle and model pulling.
    """

    _instance: Optional["OllamaInferenceBackend"] = None
    _server_process: Optional[Popen[str]] = None
    _ollama_base_path: str
    _api_base: str = "http://localhost:11434"

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(
        self, ollama_base_path: str = PathSelector.get_cache_path() / "ollama"
    ):
        if not hasattr(self, "_initialized"):
            self._ollama_base_path = ollama_base_path
            self._ollama_binary_path = self._get_binary_path(ollama_base_path)
            self._initialized = True
            self._start_server()

    def _get_binary_path(self, ollama_base_path: str) -> str:
        """Get the platform-specific path to the Ollama binary."""
        binary_paths = {
            "windows": f"{ollama_base_path}/ollama.exe",
            "linux": f"{ollama_base_path}/bin/ollama",
            "darwin": f"{ollama_base_path}/ollama",
        }

        os = PlatformDetector.get_os_name()
        if os not in binary_paths:
            raise ValueError(f"Unsupported platform: {os}")
        return binary_paths[os]

    def _is_port_available(self, port: int = 11434) -> bool:
        """Check if the specified port is available."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return True
        except socket.error:
            return False

    def _start_server(self):
        """Start the Ollama server if not already running"""
        if self._server_process is None:
            # Check if port is available
            if not self._is_port_available():
                LOGGER.info("Ollama server is already running, using existing instance")
                return

            log_path = PathSelector.get_logs_path() / "ollama.log"
            print(f"Starting ollama server at {self._ollama_binary_path}")
            with open(log_path, "w") as log_file:
                env = os.environ.copy()
                env["OLLAMA_MODELS"] = self._ollama_base_path
                self._server_process = subprocess.Popen(
                    [self._ollama_binary_path, "serve"],
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    text=True,
                    env=env,
                    shell=False,
                )
                time.sleep(1)
                retry_count = 100
                while retry_count > 0:
                    try:
                        ollama.ps()
                        break
                    except Exception as e:
                        print(e)
                        retry_count -= 1
                        LOGGER.info(
                            f"Failed to start ollama server, retrying... ({retry_count}/10)"
                        )
                        time.sleep(1)
                LOGGER.info("ollama server started")

    @override(check_signature=False)
    def ensure_model(self, model: str):
        """Ensure model is available, pull if not present"""
        try:
            # Try to list the model - if it fails, it needs to be pulled
            ollama.show(model)
        except Exception as e:
            if "not found" in str(e).lower():
                LOGGER.info(f"Pulling model: {model}")
                ollama.pull(model)
            else:
                raise e

    @override(check_signature=False)
    def generate(self, model: str, messages: list[dict[str, str]], **model_params):
        """
        Generate completion using specified model and chat messages.
        Automatically pulls model if not available.

        Args:
            model: The name of the model to use
            messages: List of message dictionaries with 'role' and 'content' keys
            **model_params: Additional parameters to pass to the model (temperature, top_p, etc.)
        """
        self.ensure_model(model)
        response = litellm.completion(
            model=f"ollama/{model}",
            messages=messages,
            api_base=self._api_base,
            **model_params,
        )
        return response

    @override(check_signature=False)
    def stream(self, model: str, messages: list[dict[str, str]], **model_params):
        """
        Stream completion using specified model and chat messages.
        Automatically pulls model if not available.
        Yields chunks of the response as they become available.

        Args:
            model: The name of the model to use
            messages: List of message dictionaries with 'role' and 'content' keys
            **model_params: Additional parameters to pass to the model (temperature, top_p, etc.)
        """
        self.ensure_model(model)

        response = litellm.completion(
            model=f"ollama/{model}",
            messages=messages,
            api_base=self._api_base,
            stream=True,
            **model_params,
        )
        return response

    @override(check_signature=False)
    def __enter__(self):
        return self

    @override(check_signature=False)
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._server_process is not None:
            self._server_process.terminate()
            self._server_process = None
            LOGGER.info("ollama server stopped")

    def __del__(self):
        """Destructor to ensure cleanup"""
        if self._server_process is not None:
            self._server_process.terminate()
            self._server_process = None

    @override
    def dispose(self) -> None:
        """Dispose of all loaded models"""
        # Ollama doesn't require explicit model unloading
        pass


# -----------------------------------------------------------------------------
