# API Endpoint Reference

This document provides a reference for all the RESTful API endpoints defined in the `client_server/api/routes` directory.

---

## Basic (`basic.py`)

### GET `/`

- **Description**: A simple health check endpoint.
- **Response**: Returns a `200 OK` with the text "OK" if the server is running.

---

## Knowledge Base Management (`delete_kb.py`, `list_kbs.py`)

### POST `/delete_kb`

- **Description**: Deletes a knowledge base from both the local database and the cloud.
- **Request Body**:
  ```json
  {
    "kbid": "knowledge-base-id-to-delete"
  }
  ```
- **Response**: A confirmation message on successful deletion.

### GET `/list_kbs`

- **Description**: Lists all available knowledge bases. It fetches the list of local knowledge bases from the database and combines it with the list of cloud-synced knowledge bases fetched from the backend.
- **Response**: An array of `QdrantKnowledgeBase` objects.

---

## Dependency and Hardware (`dependencies.py`, `hardware.py`)

### GET `/dependencies_status`

- **Description**: Checks the status of all registered dependencies (e.g., Ollama, embedding models).
- **Response**: A JSON object grouping dependencies by "common" and "eco_mode", with the status of each dependency (`installed`, `missing`, etc.).

### GET `/hardware_stats`

- **Description**: Provides statistics about the host machine's hardware.
- **Response**: A JSON object containing details about memory usage, CPU load, and disk space. It includes an `allowed` flag indicating if there is sufficient memory for certain operations.

---

## Swagger Endpoint Listing (`list_swagger_endpoints.py`)

### POST `/swagger_list`

- **Description**: Parses a Swagger/OpenAPI specification and returns a list of its endpoints.
- **Request Body**:
  ```json
  {
      "type": "file" | "url" | "content",
      "value": "path/to/file.json" | "http://url/to/spec" | "{...}",
      "full_specs": false | true
  }
  ```
  - `type`: The source of the Swagger spec.
  - `value`: The path, URL, or raw JSON/YAML content.
  - `full_specs`: If `true`, returns the full specification for each endpoint. If `false` (default), returns a simple list of paths and methods.
- **Response**: An array of endpoint definitions.

---

## Metadata and Reporting (`register_meta.py`, `report_to_cloud.py`)

### POST `/register_meta`

- **Description**: Allows the client (e.g., the VS Code extension) to register its metadata with the server at startup.
- **Request Body**:
  ```json
  {
      "session_id": "...",
      "extension_version": "...",
      "client_server_version": "...",
      "base_url": { ... }
  }
  ```
- **Details**: This is crucial for initializing global state, such as the session ID and the correct backend URLs, which are used in subsequent API calls.

### GET `/report_to_cloud`

- **Description**: Gathers system information and recent logs and sends them to a cloud reporting service for debugging and monitoring.
- **Response**: A `200 OK` on success.

---

## Search (`search.py`)

### POST `/search`

- **Description**: Performs a semantic search within a specific folder of a given knowledge base.
- **Request Body**:
  ```json
  {
    "query": "your search query",
    "kbid": "knowledge-base-id",
    "folder_path": "path/to/folder/within/kb"
  }
  ```
- **Response**: The top search result.
