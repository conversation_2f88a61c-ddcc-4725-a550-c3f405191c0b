# Chunker Utilities (`utils.py`)

The `utils.py` module is the engine of the chunking sub-package. It provides the low-level, performance-optimized functions for splitting text and code into manageable chunks. This module is used by the `CodebaseChunker`, `DocsChunker`, and `GithubChunker`.

## Key Features

- **Language-Aware Splitting:** It uses the `langchain_text_splitters` library to perform syntax-aware chunking. It maintains a dictionary (`EXTENSION_LANGAUGE_LIST`) to map file extensions to the appropriate language splitter, ensuring that code is split along logical boundaries (like functions or classes) where possible. For unknown extensions, it defaults to a Markdown splitter.

- **Performance Optimization:** The module is heavily optimized for speed.
  - **Caching:** Splitter instances and file line-position maps are cached using `@lru_cache` to avoid redundant computations when processing many similar files.
  - **Parallelism:** The `create_chunks` function uses a `ThreadPoolExecutor` to break up a single large file and process its parts in parallel.
  - **Efficient Search:** It uses a custom binary search on a compact `array.array` data structure to find chunk line numbers much faster than a naive line-by-line search.

## Core Functions

### `create_chunks(content: str, extension: str, ...)`

This is the main chunking function. It takes a string of content and a file extension, and returns a list of chunks with their corresponding line numbers.

### `find_line_numbers_batch(content: str, chunks: List[str]) -> List[Tuple[int, int]]`

A highly optimized function that finds the starting and ending line numbers for a list of text chunks within the original content.

### `make_qdrant_knowledgebase_chunks_from_content(content: str, extension: str)`

A wrapper function that takes raw content, calls `create_chunks`, and then transforms the output into a list of `QdrantKnowledgeBaseChunk` model objects.

### `make_qdrant_knowledgebase_chunks_from_file(path: str)`

A high-level utility function that encapsulates the entire process for a single file. It reads the file content (handling different text encodings like UTF-8 and cp1252), and then uses the other utility functions to produce a final list of `QdrantKnowledgeBaseChunk` objects.
