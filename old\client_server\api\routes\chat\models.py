"""
Chat data models and request/response schemas.

This module contains all Pydantic models used for chat functionality,
including request models, response chunks, action definitions, and tool call events.
"""

from typing import Any, Literal, Optional
from pydantic import BaseModel


class ChatStreamRequest(BaseModel):
    """Request model for HTTP streaming chat"""
    mode: Literal["NORMAL", "ECO", "PRO"] = "NORMAL"
    messages: list[dict[str, Any]] = []
    provider: str | dict[str, Any] = ""
    conversation_id: str = ""
    session__: str | None = None
    request_id: str = ""
    web_search: bool = False
    provide_followups: bool = True
    image: bool = False


class ChatStreamChunkAction(BaseModel):
    id: str
    name: str
    type: Literal["function"]
    args: dict[str, Any]


class ChatStreamChunk(BaseModel):
    type: Literal["content", "action"]
    memory_id: str
    chunk_index: int = 0


class ChatStreamActionChunk(ChatStreamChunk):
    type: Literal["action"]
    action: list[ChatStreamChunkAction]


class ChatStreamContentChunk(ChatStreamChunk):
    type: Literal["content"]
    content: str


# -----------------------------------------------------------------------------
# Tool Call Event Models
# -----------------------------------------------------------------------------


class ToolCallRequestEvent(BaseModel):
    """Event emitted when LLM requests to execute a tool call"""
    request_id: str
    tool_call_id: str
    tool_name: str
    tool_args: dict[str, Any]
    timestamp: float


class ToolCallExecutionEvent(BaseModel):
    """Event emitted when tool call execution begins"""
    request_id: str
    tool_call_id: str
    tool_name: str
    status: Literal["executing"]
    timestamp: float


class ToolCallResultEvent(BaseModel):
    """Event emitted when tool call completes with results"""
    request_id: str
    tool_call_id: str
    tool_name: str
    status: Literal["success", "error"]
    result_summary: Optional[str] = None
    error_message: Optional[str] = None
    timestamp: float


class ToolCallErrorEvent(BaseModel):
    """Event emitted when tool call fails with an error"""
    request_id: str
    tool_call_id: str
    tool_name: str
    error_message: str
    timestamp: float
