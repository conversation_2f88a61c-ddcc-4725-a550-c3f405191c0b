from concurrent.futures import Future, ThreadPoolExecutor
import os
import time
from itertools import chain
import traceback
from typing import Any, Callable, Coroutine

from . import IChunker
from overrides import override
from client_server.utils.chunkers.utils import (
    make_qdrant_knowledgebase_chunks_from_content,
)
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_file_stats, log_memory_usage
from client_server.utils.models.knowledgebase import (
    QdrantDocsMetadata,
    QdrantKnowledgeBaseChunk,
)
from client_server.services.crawler.playwright_crawler import PlaywrightCrawlerBackend
from client_server.utils.path_selector import PathSelector
from client_server.utils.files import get_files


class DocsChunker(IChunker):
    def __init__(self, metadata: QdrantDocsMetadata):
        self.metadata = metadata

    @override
    async def process(
        self,
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        chunks = await self._make_chunks(self.metadata.urls, progress_callback)
        return chunks

    async def _make_chunks(
        self,
        urls: list[str],
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        LOGGER.info("Processing Docs type knowledge base")
        LOGGER.info(f"Found {len(urls)} files for docs processing")

        progress_till_scraping = 70

        crawler_backend = PlaywrightCrawlerBackend(max_depth=10, max_pages=100)
        scraped_contents = await crawler_backend.scrape(
            urls[0],
            progress_callback=lambda x: (
                progress_callback(x * (progress_till_scraping / 100))
                if progress_callback
                else None
            ),
        )

        try:
            chunking_phase_start = time.time()
            LOGGER.info("Starting chunk creation phase")
            log_memory_usage("before_chunking")

            MAX_WORKERS = int(os.cpu_count() * 0.4)
            chunks_by_file: dict[str, list[QdrantKnowledgeBaseChunk]] = {}
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                chunk_futures: dict[str, Future[list[QdrantKnowledgeBaseChunk]]] = {}
                # Dispatch all the futures
                dispatch_start = time.time()
                LOGGER.debug("Dispatching chunk creation tasks to thread pool")
                for url, content in scraped_contents.items():
                    chunk_futures[url] = executor.submit(
                        make_qdrant_knowledgebase_chunks_from_content,
                        content,
                        "markdown",
                    )
                dispatch_time = time.time() - dispatch_start
                LOGGER.debug(
                    f"Dispatched {len(chunk_futures)} chunk creation tasks in {dispatch_time:.2f}s"
                )

                # Collect the results
                collection_start = time.time()
                completed_files = 0
                for i, (file, chunk_future) in enumerate(chunk_futures.items()):
                    file_start = time.time()
                    # Wait for the future to complete
                    chunks_by_file[file] = chunk_future.result()
                    file_time = time.time() - file_start
                    completed_files += 1

                    file_chunk_count = len(chunks_by_file[file])
                    LOGGER.debug(
                        f"Completed chunking for file {completed_files}/{len(chunk_futures)}: {file} "
                        f"({file_chunk_count} chunks, took {file_time:.2f}s)"
                    )

                    progress = i / len(chunk_futures) * 100
                    LOGGER.info(f"Progress: {progress:.2f}%")
                    if progress_callback:
                        await progress_callback(
                            progress_till_scraping
                            + progress * (100 - progress_till_scraping) / 100
                        )

                collection_time = time.time() - collection_start
                LOGGER.debug(
                    f"Chunk collection completed in {collection_time:.2f} seconds"
                )

            total_chunks = len(list(chain(*chunks_by_file.values())))
            chunking_phase_time = time.time() - chunking_phase_start

            LOGGER.info(
                f"Chunk creation completed. Total chunks created: {total_chunks} in {chunking_phase_time:.2f}s"
            )
            log_memory_usage("after_chunking")

            if total_chunks == 0:
                LOGGER.error("No chunks found after processing all files")
                raise RuntimeError("No chunks found")

            # Log chunking statistics
            files_with_chunks = sum(
                1 for chunks_list in chunks_by_file.values() if len(chunks_list) > 0
            )
            files_without_chunks = len(chunks_by_file) - files_with_chunks
            avg_chunks_per_file = (
                total_chunks / files_with_chunks if files_with_chunks > 0 else 0
            )

            LOGGER.info(
                f"Chunking stats - Files with chunks: {files_with_chunks}, "
                f"Files without chunks: {files_without_chunks}, "
                f"Avg chunks per file: {avg_chunks_per_file:.1f}"
            )

            return list(chain(*chunks_by_file.values()))

        except Exception as e:
            LOGGER.error(f"Error processing files: {e}")
            LOGGER.error(f"Error traceback: {traceback.format_exc()}")
            log_memory_usage("error_state")
            return []
