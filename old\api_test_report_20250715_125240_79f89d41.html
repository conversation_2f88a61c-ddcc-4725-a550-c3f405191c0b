<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Report - 20250715_125240_79f89d41</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
        h1, h2, h3, h4 { color: #2c3e50; }
        .container { max-width: 1200px; margin: 0 auto; }
        .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .endpoint { background-color: #fff; border: 1px solid #ddd; border-radius: 5px;
                   margin-bottom: 15px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .endpoint-header { display: flex; justify-content: space-between; align-items: center; }
        .method { font-weight: bold; padding: 5px 10px; border-radius: 3px; color: white; }
        .GET { background-color: #61affe; }
        .POST { background-color: #49cc90; }
        .PUT { background-color: #fca130; }
        .DELETE { background-color: #f93e3e; }
        .PATCH { background-color: #50e3c2; }
        .details { margin-top: 10px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .status { padding: 3px 8px; border-radius: 3px; color: white; }
        .status-2xx { background-color: #28a745; }
        .status-4xx { background-color: #dc3545; }
        .status-5xx { background-color: #dc3545; }
        .status-other { background-color: #6c757d; }
        .tab { overflow: hidden; border: 1px solid #ccc; background-color: #f1f1f1; }
        .tab button { background-color: inherit; float: left; border: none; outline: none; cursor: pointer;
                     padding: 10px 16px; transition: 0.3s; }
        .tab button:hover { background-color: #ddd; }
        .tab button.active { background-color: #ccc; }
        .tabcontent { display: none; padding: 6px 12px; border: 1px solid #ccc; border-top: none; }
        .show { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Test Report</h1>
        <div class="summary">
            <h2>Test Run Summary</h2>
            <p><strong>Test Run ID:</strong> 20250715_125240_79f89d41</p>
            <p><strong>Collection:</strong> File Actions API</p>
            <p><strong>Start Time:</strong> 2025-07-15T12:52:40.311902</p>
            <p><strong>Duration:</strong> 129.58 seconds</p>
            <p><strong>Total Requests:</strong> 19</p>
            <p><strong>Success Rate:</strong>
                <span class="success">
                13 /
                19
                (68.4%)
                </span>
            </p>
        </div>

        <h2>API Endpoints Tested</h2>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Basic Code Review</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/review</p>
            <p><strong>Category:</strong> Code Review</p>
            <p><strong>Response Time:</strong> 9776.6 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-0')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-0')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-0')">Headers</button>
            </div>

            <div id="request-0" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/utils/calculator.py\",\n  \"file_content\": \"def calculate(a, b, operation):\\n    if operation == 'add':\\n        return a + b\\n    elif operation == 'subtract':\\n        return a - b\\n    elif operation == 'multiply':\\n        return a * b\\n    elif operation == 'divide':\\n        if b == 0:\\n            raise ValueError('Cannot divide by zero')\\n        return a / b\\n    else:\\n        raise ValueError('Invalid operation')\\n\\ndef process_numbers(numbers):\\n    total = 0\\n    for num in numbers:\\n        total += num\\n    return total\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"analysis_depth\": \"standard\",\n  \"focus_areas\": [\"performance\", \"security\", \"style\"],\n  \"include_suggestions\": true\n}"</pre>
            </div>

            <div id="response-0" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "review",
  "file_path": "src/utils/calculator.py",
  "result": {
    "raw_content": "```json\n{\n    \"overall_score\": 7,\n    \"summary\": \"The code is generally functional and straightforward, but there are areas for improvement in error handling, performance, and documentation.\",\n    \"issues\": [\n        {\n            \"severity\": \"HIGH\",\n            \"category\": \"Logic\",\n            \"line_numbers\": [1, 2, 3, 4, 5, 6, 7, 8, 9],\n            \"title\": \"Lack of Type Checking\",\n            \"description\": \"The function 'calculate' does not enforce type checking for its parameters. This could lead to unexpected behavior if non-numeric types are passed.\",\n            \"recommendation\": \"Add type hints and check the types of 'a' and 'b' to ensure they are numeric (int or float).\",\n            \"impact\": \"Passing non-numeric types could lead to runtime errors or incorrect calculations.\",\n            \"confidence\": 0.9\n        },\n        {\n            \"severity\": \"MEDIUM\",\n            \"category\": \"Performance\",\n            \"line_numbers\": [12, 13],\n            \"title\": \"Inefficient Summation in process_numbers\",\n            \"description\": \"The 'process_numbers' function uses a loop to sum the numbers, which is less efficient than using the built-in sum function.\",\n            \"recommendation\": \"Replace the loop with 'return sum(numbers)' to improve performance and readability.\",\n            \"impact\": \"While the current implementation works, using the built-in function can lead to better performance, especially with large lists.\",\n            \"confidence\": 0.8\n        },\n        {\n            \"severity\": \"LOW\",\n            \"category\": \"Documentation\",\n            \"line_numbers\": [1, 10],\n            \"title\": \"Missing Docstrings\",\n            \"description\": \"The functions lack docstrings, which makes it difficult for other developers to understand their purpose and usage.\",\n            \"recommendation\": \"Add docstrings to both functions explaining their parameters, return values, and any exceptions raised.\",\n            \"impact\": \"Without documentation, the code is harder to maintain and understand, especially for new developers.\",\n            \"confidence\": 0.7\n        },\n        {\n            \"severity\": \"LOW\",\n            \"category\": \"Style\",\n            \"line_numbers\": [1, 10],\n            \"title\": \"Inconsistent Naming Conventions\",\n            \"description\": \"The function names are clear, but the parameter names could be more descriptive. For example, 'operation' could be 'operation_type'.\",\n            \"recommendation\": \"Consider renaming parameters to be more descriptive and consistent with Python's naming conventions.\",\n            \"impact\": \"Improving naming conventions can enhance code readability and maintainability.\",\n            \"confidence\": 0.6\n        }\n    ],\n    \"suggestions\": [\n        {\n            \"category\": \"Performance\",\n            \"description\": \"Utilize built-in functions for common operations.\",\n            \"benefit\": \"Improves performance and reduces code complexity.\",\n            \"effort\": \"LOW\"\n        },\n        {\n            \"category\": \"Documentation\",\n            \"description\": \"Add comprehensive docstrings to all functions.\",\n            \"benefit\": \"Enhances code maintainability and helps other developers understand the codebase.\",\n            \"effort\": \"MEDIUM\"\n        },\n        {\n            \"category\": \"Maintainability\",\n            \"description\": \"Implement type checking for function parameters.\",\n            \"benefit\": \"Prevents runtime errors and improves code reliability.\",\n            \"effort\": \"MEDIUM\"\n        }\n    ]\n}\n```"
  },
  "error": null,
  "processing_time": 7.642484426498413,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:22:50.086545",
  "metadata": null
}</pre>
            </div>

            <div id="headers-0" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:22:42 GMT",
  "server": "uvicorn",
  "content-length": "4000",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Comprehensive Code Review</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/review</p>
            <p><strong>Category:</strong> Code Review</p>
            <p><strong>Response Time:</strong> 9425.3 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-1')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-1')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-1')">Headers</button>
            </div>

            <div id="request-1" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/api/user_controller.js\",\n  \"file_content\": \"const express = require('express');\\nconst bcrypt = require('bcrypt');\\nconst jwt = require('jsonwebtoken');\\nconst User = require('../models/User');\\n\\nconst router = express.Router();\\n\\n// User login endpoint\\nrouter.post('/login', async (req, res) => {\\n  const { email, password } = req.body;\\n  \\n  // Find user by email\\n  const user = await User.findOne({ email: email });\\n  if (!user) {\\n    return res.status(401).json({ error: 'Invalid credentials' });\\n  }\\n  \\n  // Check password\\n  const isValid = await bcrypt.compare(password, user.password);\\n  if (!isValid) {\\n    return res.status(401).json({ error: 'Invalid credentials' });\\n  }\\n  \\n  // Generate JWT token\\n  const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);\\n  \\n  res.json({ token, user: { id: user._id, email: user.email } });\\n});\\n\\n// Get user profile\\nrouter.get('/profile/:id', async (req, res) => {\\n  const userId = req.params.id;\\n  const user = await User.findById(userId);\\n  \\n  if (!user) {\\n    return res.status(404).json({ error: 'User not found' });\\n  }\\n  \\n  res.json(user);\\n});\\n\\nmodule.exports = router;\",\n  \"language\": \"javascript\",\n  \"model\": \"gpt-4o-mini\",\n  \"analysis_depth\": \"comprehensive\",\n  \"focus_areas\": [\"security\", \"error_handling\", \"validation\"],\n  \"include_suggestions\": true\n}"</pre>
            </div>

            <div id="response-1" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "review",
  "file_path": "src/api/user_controller.js",
  "result": {
    "raw_content": "```json\n{\n    \"overall_score\": 6,\n    \"summary\": \"The code implements basic user authentication and profile retrieval, but it has several critical security and maintainability issues that need to be addressed.\",\n    \"issues\": [\n        {\n            \"severity\": \"CRITICAL\",\n            \"category\": \"Security\",\n            \"line_numbers\": [12],\n            \"title\": \"Missing JWT Secret Validation\",\n            \"description\": \"The JWT secret used for signing tokens is not validated for existence before use, which can lead to runtime errors if the environment variable is not set.\",\n            \"recommendation\": \"Add a check to ensure that process.env.JWT_SECRET is defined before using it to sign the token.\",\n            \"impact\": \"If the JWT secret is not set, the application will crash when trying to sign a token, leading to a denial of service.\",\n            \"confidence\": 0.9\n        },\n        {\n            \"severity\": \"HIGH\",\n            \"category\": \"Security\",\n            \"line_numbers\": [4],\n            \"title\": \"Potential Information Disclosure\",\n            \"description\": \"The error messages returned for invalid credentials do not specify whether the email or password was incorrect, which is a good practice to prevent user enumeration attacks.\",\n            \"recommendation\": \"Keep the error message generic, such as 'Invalid credentials', without specifying the reason.\",\n            \"impact\": \"Providing specific error messages can allow attackers to determine valid email addresses in the system.\",\n            \"confidence\": 0.8\n        },\n        {\n            \"severity\": \"MEDIUM\",\n            \"category\": \"Error Handling\",\n            \"line_numbers\": [7, 20],\n            \"title\": \"Lack of Error Handling for Database Operations\",\n            \"description\": \"The code does not handle potential errors that may occur during database operations (e.g., User.findOne, User.findById). This can lead to unhandled promise rejections.\",\n            \"recommendation\": \"Wrap database calls in try-catch blocks to handle errors gracefully and return appropriate HTTP status codes.\",\n            \"impact\": \"Uncaught errors can crash the server or lead to uninformative responses to the client.\",\n            \"confidence\": 0.85\n        },\n        {\n            \"severity\": \"MEDIUM\",\n            \"category\": \"Maintainability\",\n            \"line_numbers\": [5],\n            \"title\": \"Hardcoded Status Codes\",\n            \"description\": \"HTTP status codes are hardcoded in the response, which can lead to inconsistencies if the codes need to be changed in the future.\",\n            \"recommendation\": \"Define constants for status codes at the top of the file or use an enum to improve readability and maintainability.\",\n            \"impact\": \"Hardcoding status codes can lead to errors and make the code less readable and maintainable.\",\n            \"confidence\": 0.7\n        }\n    ],\n    \"suggestions\": [\n        {\n            \"category\": \"Documentation\",\n            \"description\": \"Add JSDoc comments to functions to describe their purpose, parameters, and return values.\",\n            \"benefit\": \"Improves code readability and helps other developers understand the codebase quickly.\",\n            \"effort\": \"LOW\"\n        },\n        {\n            \"category\": \"Security\",\n            \"description\": \"Implement rate limiting on the login endpoint to prevent brute-force attacks.\",\n            \"benefit\": \"Enhances security by limiting the number of login attempts from a single IP address.\",\n            \"effort\": \"MEDIUM\"\n        }\n    ]\n}\n```"
  },
  "error": null,
  "processing_time": 7.386459827423096,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:23:00.025122",
  "metadata": null
}</pre>
            </div>

            <div id="headers-1" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:22:52 GMT",
  "server": "uvicorn",
  "content-length": "4009",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Generate Inline Documentation</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/document</p>
            <p><strong>Category:</strong> Documentation Generation</p>
            <p><strong>Response Time:</strong> 19464.3 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-2')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-2')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-2')">Headers</button>
            </div>

            <div id="request-2" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/services/data_processor.py\",\n  \"file_content\": \"import pandas as pd\\nimport numpy as np\\nfrom typing import List, Dict, Optional\\n\\nclass DataProcessor:\\n    def __init__(self, config: Dict):\\n        self.config = config\\n        self.data = None\\n    \\n    def load_data(self, file_path: str) -> pd.DataFrame:\\n        if file_path.endswith('.csv'):\\n            return pd.read_csv(file_path)\\n        elif file_path.endswith('.json'):\\n            return pd.read_json(file_path)\\n        else:\\n            raise ValueError('Unsupported file format')\\n    \\n    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:\\n        df = df.dropna()\\n        df = df.drop_duplicates()\\n        return df\\n    \\n    def transform_data(self, df: pd.DataFrame, transformations: List[str]) -> pd.DataFrame:\\n        for transform in transformations:\\n            if transform == 'normalize':\\n                numeric_cols = df.select_dtypes(include=[np.number]).columns\\n                df[numeric_cols] = (df[numeric_cols] - df[numeric_cols].mean()) / df[numeric_cols].std()\\n            elif transform == 'log_transform':\\n                numeric_cols = df.select_dtypes(include=[np.number]).columns\\n                df[numeric_cols] = np.log1p(df[numeric_cols])\\n        return df\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"doc_type\": \"inline\",\n  \"include_examples\": true\n}"</pre>
            </div>

            <div id="response-2" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "document",
  "file_path": "src/services/data_processor.py",
  "result": {
    "content": "Here is the inline documentation for the provided Python code, following best practices for clarity, accuracy, completeness, consistency, helpfulness, and maintainability:\n\n```python\nimport pandas as pd\nimport numpy as np\nfrom typing import List, Dict, Optional\n\nclass DataProcessor:\n    \"\"\"\n    A class to process data for analysis.\n\n    Attributes:\n        config (Dict): Configuration settings for data processing.\n        data (Optional[pd.DataFrame]): The loaded data, initially set to None.\n\n    Example:\n        config = {'normalize': True, 'log_transform': False}\n        processor = DataProcessor(config)\n        df = processor.load_data('data.csv')\n        cleaned_df = processor.clean_data(df)\n        transformed_df = processor.transform_data(cleaned_df, ['normalize'])\n    \"\"\"\n\n    def __init__(self, config: Dict):\n        \"\"\"\n        Initializes the DataProcessor with a configuration dictionary.\n\n        Parameters:\n            config (Dict): A dictionary containing configuration settings for data processing.\n        \"\"\"\n        self.config = config\n        self.data = None\n    \n    def load_data(self, file_path: str) -> pd.DataFrame:\n        \"\"\"\n        Loads data from a specified file path.\n\n        Supports CSV and JSON file formats.\n\n        Parameters:\n            file_path (str): The path to the data file.\n\n        Returns:\n            pd.DataFrame: A DataFrame containing the loaded data.\n\n        Raises:\n            ValueError: If the file format is unsupported (not .csv or .json).\n        \n        Example:\n            df = processor.load_data('data.csv')\n        \"\"\"\n        if file_path.endswith('.csv'):\n            return pd.read_csv(file_path)\n        elif file_path.endswith('.json'):\n            return pd.read_json(file_path)\n        else:\n            raise ValueError('Unsupported file format')\n    \n    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:\n        \"\"\"\n        Cleans the provided DataFrame by removing missing values and duplicates.\n\n        Parameters:\n            df (pd.DataFrame): The DataFrame to be cleaned.\n\n        Returns:\n            pd.DataFrame: A cleaned DataFrame with no missing values or duplicates.\n\n        Example:\n            cleaned_df = processor.clean_data(df)\n        \"\"\"\n        df = df.dropna()  # Remove rows with missing values\n        df = df.drop_duplicates()  # Remove duplicate rows\n        return df\n    \n    def transform_data(self, df: pd.DataFrame, transformations: List[str]) -> pd.DataFrame:\n        \"\"\"\n        Applies specified transformations to the DataFrame.\n\n        Supported transformations:\n            - 'normalize': Normalizes numeric columns to have a mean of 0 and standard deviation of 1.\n            - 'log_transform': Applies a log transformation to numeric columns.\n\n        Parameters:\n            df (pd.DataFrame): The DataFrame to be transformed.\n            transformations (List[str]): A list of transformation names to apply.\n\n        Returns:\n            pd.DataFrame: The transformed DataFrame.\n\n        Example:\n            transformed_df = processor.transform_data(cleaned_df, ['normalize', 'log_transform'])\n        \n        Common Pitfall:\n            Ensure that the DataFrame contains numeric columns before applying transformations.\n        \"\"\"\n        for transform in transformations:\n            if transform == 'normalize':\n                numeric_cols = df.select_dtypes(include=[np.number]).columns\n                df[numeric_cols] = (df[numeric_cols] - df[numeric_cols].mean()) / df[numeric_cols].std()\n            elif transform == 'log_transform':\n                numeric_cols = df.select_dtypes(include=[np.number]).columns\n                df[numeric_cols] = np.log1p(df[numeric_cols])  # log1p is used to handle log(0) case\n        return df\n```\n\n### Key Points of the Documentation:\n- **Class and Method Descriptions**: Each class and method has a clear description of its purpose.\n- **Parameters and Return Values**: Each method includes detailed descriptions of parameters and return values.\n- **Exceptions**: The `load_data` method specifies the exception that may be raised.\n- **Examples**: Practical examples are provided for each method to illustrate usage.\n- **Common Pitfalls**: A note on potential issues is included in the `transform_data` method to guide users.\n- **Type Annotations**: Type hints are used throughout to clarify expected types for parameters and return values."
  },
  "error": null,
  "processing_time": 17.382094144821167,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:23:19.992174",
  "metadata": null
}</pre>
            </div>

            <div id="headers-2" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:23:01 GMT",
  "server": "uvicorn",
  "content-length": "4819",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Generate API Documentation</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/document</p>
            <p><strong>Category:</strong> Documentation Generation</p>
            <p><strong>Response Time:</strong> 12588.7 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-3')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-3')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-3')">Headers</button>
            </div>

            <div id="request-3" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/api/products.py\",\n  \"file_content\": \"from fastapi import APIRouter, HTTPException, Depends\\nfrom typing import List, Optional\\nfrom pydantic import BaseModel\\n\\nrouter = APIRouter()\\n\\nclass Product(BaseModel):\\n    id: int\\n    name: str\\n    price: float\\n    category: str\\n    in_stock: bool\\n\\<EMAIL>('/products')\\nasync def get_products(category: Optional[str] = None, limit: int = 10):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products/{product_id}')\\nasync def get_product(product_id: int):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products')\\nasync def create_product(product: Product):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products/{product_id}')\\nasync def update_product(product_id: int, product: Product):\\n    # Implementation here\\n    pass\\n\\<EMAIL>('/products/{product_id}')\\nasync def delete_product(product_id: int):\\n    # Implementation here\\n    pass\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"doc_type\": \"api\",\n  \"include_examples\": true\n}"</pre>
            </div>

            <div id="response-3" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "document",
  "file_path": "src/api/products.py",
  "result": {
    "content": "# API Documentation for Products\n\nThis documentation provides a comprehensive overview of the API endpoints related to product management in a FastAPI application. Each endpoint includes details about parameters, request and response formats, and examples.\n\n## Base URL\n```\n/products\n```\n\n## Endpoints\n\n### 1. Get All Products\n\n#### `GET /products`\n\nRetrieves a list of products. You can filter the products by category and limit the number of results returned.\n\n**Parameters:**\n- `category` (Optional[str]): The category to filter products by. If not provided, all products are returned.\n- `limit` (int): The maximum number of products to return. Default is 10.\n\n**Responses:**\n- **200 OK**: Returns a list of products.\n- **400 Bad Request**: If the limit is less than 1.\n\n**Example Request:**\n```http\nGET /products?category=electronics&limit=5\n```\n\n**Example Response:**\n```json\n[\n    {\n        \"id\": 1,\n        \"name\": \"Smartphone\",\n        \"price\": 699.99,\n        \"category\": \"electronics\",\n        \"in_stock\": true\n    },\n    {\n        \"id\": 2,\n        \"name\": \"Laptop\",\n        \"price\": 999.99,\n        \"category\": \"electronics\",\n        \"in_stock\": false\n    }\n]\n```\n\n---\n\n### 2. Get a Single Product\n\n#### `GET /products/{product_id}`\n\nRetrieves a specific product by its ID.\n\n**Parameters:**\n- `product_id` (int): The ID of the product to retrieve.\n\n**Responses:**\n- **200 OK**: Returns the product details.\n- **404 Not Found**: If the product with the specified ID does not exist.\n\n**Example Request:**\n```http\nGET /products/1\n```\n\n**Example Response:**\n```json\n{\n    \"id\": 1,\n    \"name\": \"Smartphone\",\n    \"price\": 699.99,\n    \"category\": \"electronics\",\n    \"in_stock\": true\n}\n```\n\n---\n\n### 3. Create a New Product\n\n#### `POST /products`\n\nCreates a new product.\n\n**Request Body:**\n- `product` (Product): The product details to create. Must include `name`, `price`, `category`, and `in_stock`.\n\n**Responses:**\n- **201 Created**: Returns the created product.\n- **400 Bad Request**: If the product data is invalid.\n\n**Example Request:**\n```http\nPOST /products\nContent-Type: application/json\n\n{\n    \"name\": \"Tablet\",\n    \"price\": 399.99,\n    \"category\": \"electronics\",\n    \"in_stock\": true\n}\n```\n\n**Example Response:**\n```json\n{\n    \"id\": 3,\n    \"name\": \"Tablet\",\n    \"price\": 399.99,\n    \"category\": \"electronics\",\n    \"in_stock\": true\n}\n```\n\n---\n\n### 4. Update an Existing Product\n\n#### `PUT /products/{product_id}`\n\nUpdates an existing product by its ID.\n\n**Parameters:**\n- `product_id` (int): The ID of the product to update.\n- `product` (Product): The updated product details.\n\n**Responses:**\n- **200 OK**: Returns the updated product.\n- **404 Not Found**: If the product with the specified ID does not exist.\n- **400 Bad Request**: If the product data is invalid.\n\n**Example Request:**\n```http\nPUT /products/1\nContent-Type: application/json\n\n{\n    \"name\": \"Smartphone Pro\",\n    \"price\": 899.99,\n    \"category\": \"electronics\",\n    \"in_stock\": true\n}\n```\n\n**Example Response:**\n```json\n{\n    \"id\": 1,\n    \"name\": \"Smartphone Pro\",\n    \"price\": 899.99,\n    \"category\": \"electronics\",\n    \"in_stock\": true\n}\n```\n\n---\n\n### 5. Delete a Product\n\n#### `DELETE /products/{product_id}`\n\nDeletes a product by its ID.\n\n**Parameters:**\n- `product_id` (int): The ID of the product to delete.\n\n**Responses:**\n- **204 No Content**: Successfully deleted the product.\n- **404 Not Found**: If the product with the specified ID does not exist.\n\n**Example Request:**\n```http\nDELETE /products/1\n```\n\n**Example Response:**\n```http\n204 No Content\n```\n\n---\n\n## Data Model\n\n### Product\n\nThe `Product` model represents the structure of a product in the system.\n\n```python\nclass Product(BaseModel):\n    id: int\n    name: str\n    price: float\n    category: str\n    in_stock: bool\n```\n\n**Attributes:**\n- `id` (int): Unique identifier for the product.\n- `name` (str): Name of the product.\n- `price` (float): Price of the product.\n- `category` (str): Category to which the product belongs.\n- `in_stock` (bool): Availability status of the product.\n\n## Common Pitfalls\n- Ensure that the `product_id` provided in the URL exists when trying to retrieve, update, or delete a product.\n- Validate the `limit` parameter to avoid excessively large requests.\n- When creating or updating a product, ensure that all required fields are included in the request body.\n\n## Conclusion\nThis API documentation provides a clear and concise reference for developers to interact with the product management endpoints. Each endpoint is designed to be intuitive and follows RESTful principles, making it easy to integrate into applications."
  },
  "error": null,
  "processing_time": 10.528634786605835,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:23:33.088966",
  "metadata": null
}</pre>
            </div>

            <div id="headers-3" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:23:22 GMT",
  "server": "uvicorn",
  "content-length": "5140",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Generate API Documentation with Schemas</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/document</p>
            <p><strong>Category:</strong> Documentation Generation</p>
            <p><strong>Response Time:</strong> 9400.4 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-4')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-4')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-4')">Headers</button>
            </div>

            <div id="request-4" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/api/auth_service.py\",\n  \"file_content\": \"from flask import Flask, request, jsonify\\nfrom functools import wraps\\nimport jwt\\nimport datetime\\n\\napp = Flask(__name__)\\napp.config['SECRET_KEY'] = 'your-secret-key'\\n\\ndef token_required(f):\\n    @wraps(f)\\n    def decorated(*args, **kwargs):\\n        token = request.headers.get('Authorization')\\n        if not token:\\n            return jsonify({'message': 'Token is missing'}), 401\\n        try:\\n            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])\\n        except:\\n            return jsonify({'message': 'Token is invalid'}), 401\\n        return f(*args, **kwargs)\\n    return decorated\\n\\<EMAIL>('/api/login', methods=['POST'])\\ndef login():\\n    auth = request.get_json()\\n    if auth and auth['username'] == 'admin' and auth['password'] == 'password':\\n        token = jwt.encode({\\n            'user': auth['username'],\\n            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)\\n        }, app.config['SECRET_KEY'])\\n        return jsonify({'token': token})\\n    return jsonify({'message': 'Invalid credentials'}), 401\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"doc_type\": \"api\",\n  \"include_examples\": true,\n  \"include_schemas\": true\n}"</pre>
            </div>

            <div id="response-4" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "document",
  "file_path": "src/api/auth_service.py",
  "result": {
    "content": "# API Documentation for Auth Service\n\nThis document provides a comprehensive overview of the authentication service API, including endpoints, parameters, return values, and examples. The API is built using Flask and utilizes JWT (JSON Web Tokens) for authentication.\n\n## Overview\n\nThe Auth Service provides a simple login mechanism that generates a JWT token for authenticated users. The token can be used to access protected resources in the application.\n\n## Base URL\n\n```\nhttp://<your-domain>/api\n```\n\n## Endpoints\n\n### 1. Login\n\n- **Endpoint**: `/api/login`\n- **Method**: `POST`\n- **Description**: Authenticates a user and returns a JWT token if the credentials are valid.\n\n#### Request\n\n- **Headers**: \n  - `Content-Type: application/json`\n  \n- **Body**: \n  - **Type**: JSON\n  - **Schema**:\n    ```json\n    {\n      \"username\": \"string\",\n      \"password\": \"string\"\n    }\n    ```\n  \n- **Parameters**:\n  - `username` (string): The username of the user attempting to log in.\n  - `password` (string): The password of the user attempting to log in.\n\n#### Response\n\n- **Success (200)**:\n  - **Content-Type**: application/json\n  - **Body**:\n    ```json\n    {\n      \"token\": \"string\"\n    }\n    ```\n  \n- **Error (401)**:\n  - **Content-Type**: application/json\n  - **Body**:\n    ```json\n    {\n      \"message\": \"Invalid credentials\"\n    }\n    ```\n\n#### Example Request\n\n```bash\ncurl -X POST http://<your-domain>/api/login \\\n-H \"Content-Type: application/json\" \\\n-d '{\"username\": \"admin\", \"password\": \"password\"}'\n```\n\n#### Example Response (Success)\n\n```json\n{\n  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}\n```\n\n#### Example Response (Error)\n\n```json\n{\n  \"message\": \"Invalid credentials\"\n}\n```\n\n### 2. Token Required Decorator\n\n- **Function**: `token_required`\n- **Description**: A decorator that checks for the presence of a JWT token in the request headers. If the token is missing or invalid, it returns an error response.\n\n#### Usage\n\nTo protect any endpoint, simply decorate the function with `@token_required`.\n\n#### Example\n\n```python\<EMAIL>('/api/protected', methods=['GET'])\n@token_required\ndef protected():\n    return jsonify({'message': 'This is a protected route'})\n```\n\n### Exception Handling\n\nThe `token_required` decorator handles the following exceptions:\n\n- **Missing Token**: Returns a 401 status code with the message \"Token is missing\".\n- **Invalid Token**: Returns a 401 status code with the message \"Token is invalid\".\n\n## Code Documentation\n\n### `token_required(f)`\n\n```python\ndef token_required(f):\n    \"\"\"\n    Decorator to check if a token is present in the request headers.\n\n    Args:\n        f (function): The function to be decorated.\n\n    Returns:\n        function: The decorated function that checks for a valid token.\n\n    Raises:\n        Unauthorized: If the token is missing or invalid.\n    \"\"\"\n```\n\n### `login()`\n\n```python\<EMAIL>('/api/login', methods=['POST'])\ndef login():\n    \"\"\"\n    Authenticates a user and returns a JWT token.\n\n    Request Body:\n        - username (str): The username of the user.\n        - password (str): The password of the user.\n\n    Returns:\n        Response: JSON object containing the token if successful, or an error message.\n\n    Raises:\n        Unauthorized: If the credentials are invalid.\n    \"\"\"\n```\n\n## Common Pitfalls\n\n- Ensure that the `SECRET_KEY` is kept confidential and is not hard-coded in production environments.\n- The token expiration time is set to 24 hours; consider implementing token refresh logic for better user experience.\n- Always validate user input to prevent security vulnerabilities such as SQL injection or other attacks.\n\n## Conclusion\n\nThis documentation provides a clear and concise overview of the Auth Service API. By following the examples and guidelines provided, developers can easily integrate authentication into their applications. For further enhancements, consider implementing additional security measures and user management features."
  },
  "error": null,
  "processing_time": 7.3574488162994385,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:23:42.994151",
  "metadata": null
}</pre>
            </div>

            <div id="headers-4" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:23:35 GMT",
  "server": "uvicorn",
  "content-length": "4398",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Basic Security Scan</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/security-scan</p>
            <p><strong>Category:</strong> Security Scanning</p>
            <p><strong>Response Time:</strong> 5819.3 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-5')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-5')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-5')">Headers</button>
            </div>

            <div id="request-5" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/auth/login.php\",\n  \"file_content\": \"<?php\\n$username = $_POST['username'];\\n$password = $_POST['password'];\\n\\n$query = \\\"SELECT * FROM users WHERE username = '$username' AND password = '$password'\\\";\\n$result = mysql_query($query);\\n\\nif (mysql_num_rows($result) > 0) {\\n    echo \\\"Login successful\\\";\\n    $_SESSION['user'] = $username;\\n} else {\\n    echo \\\"Invalid credentials\\\";\\n}\\n?>\",\n  \"language\": \"php\",\n  \"model\": \"gpt-4o-mini\",\n  \"scan_depth\": \"standard\"\n}"</pre>
            </div>

            <div id="response-5" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "security-scan",
  "file_path": "src/auth/login.php",
  "result": {
    "raw_content": "```json\n{\n    \"security_score\": 2,\n    \"risk_level\": \"CRITICAL\",\n    \"summary\": \"The code contains multiple critical vulnerabilities including SQL Injection and insecure password handling.\",\n    \"vulnerabilities\": [\n        {\n            \"severity\": \"CRITICAL\",\n            \"type\": \"SQL Injection\",\n            \"cwe_id\": \"CWE-89\",\n            \"line_numbers\": [5],\n            \"title\": \"SQL Injection in Login Query\",\n            \"description\": \"The code directly interpolates user input into the SQL query without any sanitization or parameterization, allowing an attacker to manipulate the SQL query.\",\n            \"attack_vector\": \"An attacker could input a username like 'admin' OR '1'='1' and any password, which would bypass authentication.\",\n            \"remediation\": \"Use prepared statements with parameterized queries to prevent SQL injection. For example, use PDO or MySQLi with bound parameters.\",\n            \"cvss_score\": 9.0,\n            \"confidence\": 1.0\n        },\n        {\n            \"severity\": \"HIGH\",\n            \"type\": \"Insecure Password Storage\",\n            \"cwe_id\": \"CWE-256\",\n            \"line_numbers\": [5],\n            \"title\": \"Insecure Password Handling\",\n            \"description\": \"The code compares plaintext passwords directly in the SQL query, which is insecure. Passwords should be hashed and verified using secure hashing algorithms.\",\n            \"attack_vector\": \"An attacker who gains access to the database could retrieve user passwords in plaintext, compromising user accounts.\",\n            \"remediation\": \"Implement password hashing using a secure algorithm like bcrypt. Store only the hashed password in the database and verify it during login.\",\n            \"cvss_score\": 7.5,\n            \"confidence\": 1.0\n        }\n    ],\n    \"recommendations\": [\n        {\n            \"category\": \"Input Validation and Sanitization\",\n            \"description\": \"Implement input validation and sanitization for all user inputs to prevent SQL injection and other injection attacks.\",\n            \"priority\": \"HIGH\"\n        },\n        {\n            \"category\": \"Authentication\",\n            \"description\": \"Use secure password storage practices, including hashing and salting passwords before storing them in the database.\",\n            \"priority\": \"HIGH\"\n        },\n        {\n            \"category\": \"Database Security\",\n            \"description\": \"Switch to using prepared statements with parameterized queries to enhance database security.\",\n            \"priority\": \"HIGH\"\n        }\n    ]\n}\n```"
  },
  "error": null,
  "processing_time": 3.7700002193450928,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:23:49.317771",
  "metadata": null
}</pre>
            </div>

            <div id="headers-5" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:23:45 GMT",
  "server": "uvicorn",
  "content-length": "2935",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Comprehensive Security Scan</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/security-scan</p>
            <p><strong>Category:</strong> Security Scanning</p>
            <p><strong>Response Time:</strong> 7505.6 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-6')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-6')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-6')">Headers</button>
            </div>

            <div id="request-6" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/api/file_upload.js\",\n  \"file_content\": \"const express = require('express');\\nconst multer = require('multer');\\nconst path = require('path');\\nconst fs = require('fs');\\n\\nconst app = express();\\n\\nconst storage = multer.diskStorage({\\n  destination: function (req, file, cb) {\\n    cb(null, 'uploads/')\\n  },\\n  filename: function (req, file, cb) {\\n    cb(null, file.originalname)\\n  }\\n});\\n\\nconst upload = multer({ storage: storage });\\n\\napp.post('/upload', upload.single('file'), (req, res) => {\\n  const file = req.file;\\n  if (!file) {\\n    return res.status(400).json({ error: 'No file uploaded' });\\n  }\\n  \\n  // Execute uploaded file\\n  const filePath = path.join(__dirname, 'uploads', file.filename);\\n  require(filePath);\\n  \\n  res.json({ message: 'File uploaded and executed successfully' });\\n});\\n\\napp.listen(3000);\",\n  \"language\": \"javascript\",\n  \"model\": \"gpt-4o-mini\",\n  \"scan_depth\": \"comprehensive\"\n}"</pre>
            </div>

            <div id="response-6" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "security-scan",
  "file_path": "src/api/file_upload.js",
  "result": {
    "raw_content": "```json\n{\n    \"security_score\": 2,\n    \"risk_level\": \"CRITICAL\",\n    \"summary\": \"The code allows for arbitrary file execution, which can lead to remote code execution vulnerabilities.\",\n    \"vulnerabilities\": [\n        {\n            \"severity\": \"CRITICAL\",\n            \"type\": \"Remote Code Execution\",\n            \"cwe_id\": \"CWE-94\",\n            \"line_numbers\": [20],\n            \"title\": \"Arbitrary File Execution\",\n            \"description\": \"The application executes any uploaded file without validation or restrictions, allowing an attacker to upload and execute malicious scripts.\",\n            \"attack_vector\": \"An attacker can upload a malicious JavaScript file that, when executed, can perform unauthorized actions on the server.\",\n            \"remediation\": \"Implement strict file type validation and avoid executing uploaded files directly. Use a safe execution environment or sandboxing techniques if execution is necessary.\",\n            \"cvss_score\": 9.8,\n            \"confidence\": 0.9\n        },\n        {\n            \"severity\": \"HIGH\",\n            \"type\": \"Insecure File Upload\",\n            \"cwe_id\": \"CWE-434\",\n            \"line_numbers\": [10],\n            \"title\": \"Insecure File Upload\",\n            \"description\": \"The application does not validate the type of files being uploaded, allowing potentially harmful files to be uploaded.\",\n            \"attack_vector\": \"An attacker can upload executable files (e.g., .exe, .sh) or scripts (e.g., .js, .php) that could be executed on the server.\",\n            \"remediation\": \"Implement file type validation to restrict uploads to only safe file types (e.g., images, documents) and use a whitelist approach.\",\n            \"cvss_score\": 7.5,\n            \"confidence\": 0.8\n        },\n        {\n            \"severity\": \"MEDIUM\",\n            \"type\": \"Directory Traversal\",\n            \"cwe_id\": \"CWE-22\",\n            \"line_numbers\": [15],\n            \"title\": \"Potential Directory Traversal\",\n            \"description\": \"The filename is taken directly from the uploaded file without sanitization, which could allow directory traversal attacks.\",\n            \"attack_vector\": \"An attacker could upload a file with a name like '../malicious.js', potentially allowing access to sensitive files or directories.\",\n            \"remediation\": \"Sanitize the filename to remove any path traversal characters and ensure that the file is saved in a secure location.\",\n            \"cvss_score\": 6.5,\n            \"confidence\": 0.7\n        }\n    ],\n    \"recommendations\": [\n        {\n            \"category\": \"Input Validation\",\n            \"description\": \"Implement strict validation for uploaded files, including file type and size restrictions.\",\n            \"priority\": \"HIGH\"\n        },\n        {\n            \"category\": \"Code Execution\",\n            \"description\": \"Avoid executing uploaded files directly. If execution is necessary, use a secure sandbox environment.\",\n            \"priority\": \"CRITICAL\"\n        },\n        {\n            \"category\": \"File Handling\",\n            \"description\": \"Sanitize file names to prevent directory traversal and ensure files are stored securely.\",\n            \"priority\": \"MEDIUM\"\n        }\n    ]\n}\n```"
  },
  "error": null,
  "processing_time": 5.4400951862335205,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:23:57.327252",
  "metadata": null
}</pre>
            </div>

            <div id="headers-6" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:23:50 GMT",
  "server": "uvicorn",
  "content-length": "3648",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Generate Fix Suggestions</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/fix-suggestions</p>
            <p><strong>Category:</strong> Fix Suggestions</p>
            <p><strong>Response Time:</strong> 9139.4 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-7')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-7')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-7')">Headers</button>
            </div>

            <div id="request-7" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"file_path\": \"src/utils/data_validator.py\",\n  \"file_content\": \"def validate_email(email):\\n    if '@' in email:\\n        return True\\n    return False\\n\\ndef validate_password(password):\\n    if len(password) > 6:\\n        return True\\n    return False\\n\\ndef process_user_data(data):\\n    users = []\\n    for item in data:\\n        if validate_email(item['email']) and validate_password(item['password']):\\n            users.append(item)\\n    return users\\n\\ndef calculate_average(numbers):\\n    total = 0\\n    for num in numbers:\\n        total += num\\n    return total / len(numbers)\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"issues\": [\\n    {\\n      \\\"severity\\\": \\\"HIGH\\\",\\n      \\\"category\\\": \\\"validation\\\",\\n      \\\"line_numbers\\\": [1, 2, 3, 4],\\n      \\\"title\\\": \\\"Weak email validation\\\",\\n      \\\"description\\\": \\\"Email validation only checks for @ symbol\\\",\\n      \\\"recommendation\\\": \\\"Use proper regex or email validation library\\\"\\n    },\\n    {\\n      \\\"severity\\\": \\\"MEDIUM\\\",\\n      \\\"category\\\": \\\"error_handling\\\",\\n      \\\"line_numbers\\\": [18, 19, 20, 21],\\n      \\\"title\\\": \\\"Division by zero risk\\\",\\n      \\\"description\\\": \\\"No check for empty list in average calculation\\\",\\n      \\\"recommendation\\\": \\\"Add check for empty list before division\\\"\\n    }\\n  ],\n  \"safety_level\": \"review_required\"\n}"</pre>
            </div>

            <div id="response-7" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "fix-suggestions",
  "file_path": "src/utils/data_validator.py",
  "result": {
    "raw_content": "```json\n{\n    \"fixes\": [\n        {\n            \"issue_id\": \"weak_email_validation\",\n            \"confidence\": 0.8,\n            \"safety_rating\": \"LIKELY_SAFE\",\n            \"impact\": \"MODERATE\",\n            \"fix_type\": \"LOGIC\",\n            \"title\": \"Improve email validation logic\",\n            \"description\": \"The current email validation only checks for the presence of an '@' symbol, which is insufficient. This fix uses a regular expression to validate the email format more robustly, ensuring it meets common email standards.\",\n            \"line_changes\": [\n                {\n                    \"line_number\": 1,\n                    \"old_code\": \"def validate_email(email):\",\n                    \"new_code\": \"import re\\ndef validate_email(email):\",\n                    \"change_type\": \"ADD\"\n                },\n                {\n                    \"line_number\": 2,\n                    \"old_code\": \"if '@' in email:\",\n                    \"new_code\": \"if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$', email):\",\n                    \"change_type\": \"MODIFY\"\n                }\n            ],\n            \"testing_requirements\": [\"Test with valid and invalid email formats.\"],\n            \"dependencies\": [\"re\"]\n        },\n        {\n            \"issue_id\": \"division_by_zero_risk\",\n            \"confidence\": 0.9,\n            \"safety_rating\": \"LIKELY_SAFE\",\n            \"impact\": \"SIGNIFICANT\",\n            \"fix_type\": \"LOGIC\",\n            \"title\": \"Add check for empty list in average calculation\",\n            \"description\": \"The current implementation of calculate_average does not handle the case where the input list is empty, which would lead to a division by zero error. This fix adds a check to return 0 or raise an exception if the list is empty.\",\n            \"line_changes\": [\n                {\n                    \"line_number\": 17,\n                    \"old_code\": \"return total / len(numbers)\",\n                    \"new_code\": \"if len(numbers) == 0:\\n    return 0\\nreturn total / len(numbers)\",\n                    \"change_type\": \"MODIFY\"\n                }\n            ],\n            \"testing_requirements\": [\"Test with non-empty lists, empty lists, and lists with one element.\"],\n            \"dependencies\": []\n        }\n    ],\n    \"summary\": {\n        \"total_fixes\": 2,\n        \"safe_fixes\": 2,\n        \"risky_fixes\": 0,\n        \"estimated_time\": \"1 hour\"\n    }\n}\n```"
  },
  "error": null,
  "processing_time": 4.577349901199341,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:46:24.691494",
  "metadata": null
}</pre>
            </div>

            <div id="headers-7" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:23:59 GMT",
  "server": "uvicorn",
  "content-length": "34",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Apply Safe Fixes</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/apply-fixes</p>
            <p><strong>Category:</strong> Auto Apply Fixes</p>
            <p><strong>Response Time:</strong> 9150.5 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-8')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-8')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-8')">Headers</button>
            </div>

            <div id="request-8" class="tabcontent">
                <h4>Request Body</h4>
                <pre>{
  "file_path": "C:\\Users\\<USER>\\Desktop\\testing_knb\\text.txt",
  "file_content": "def format_name(first, last):\n    return first + ' ' + last\n\ndef clean_string(text):\n    return text.strip().lower()\n\ndef validate_input(value):\n    if value == None:\n        return False\n    return True",
  "language": "python",
  "model": "gpt-4o-mini",
  "fixes": [
    {
      "fix_id": "fix_1",
      "confidence": 0.95,
      "safety_rating": "SAFE",
      "title": "Use f-string for string formatting",
      "line_changes": [
        {
          "line_number": 2,
          "old_code": "    return first + ' ' + last",
          "new_code": "    return f'{first} {last}'",
          "change_type": "MODIFY"
        }
      ]
    },
    {
      "fix_id": "fix_2",
      "confidence": 0.9,
      "safety_rating": "SAFE",
      "title": "Use 'is None' instead of '== None'",
      "line_changes": [
        {
          "line_number": 7,
          "old_code": "    if value == None:",
          "new_code": "    if value is None:",
          "change_type": "MODIFY"
        }
      ]
    }
  ],
  "safety_level": "safe_only",
  "create_backup": true
}
</pre>
            </div>

            <div id="response-8" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "operation": "auto-apply",
  "file_path": "C:\\Users\\<USER>\\Desktop\\testing_knb\\text.txt",
  "result": {
    "success": true,
    "modified_content": "```python\n\ndef format_name(first, last):\n    # Using f-string for string formatting\n    return f'{first} {last}'\n\ndef clean_string(text):\n    return text.strip().lower()\n\ndef validate_input(value):\n    # Using 'is None' for comparison\n    if value is None:\n        return False\n    return True\n```",
    "applied_fixes": [
      {
        "fix_id": "fix_1",
        "title": "Use f-string for string formatting",
        "lines_modified": [
          4
        ],
        "change_summary": "Replaced string concatenation with f-string for better readability."
      },
      {
        "fix_id": "fix_2",
        "title": "Use 'is None' instead of '== None'",
        "lines_modified": [
          10
        ],
        "change_summary": "Changed comparison from '== None' to 'is None' for better practice."
      }
    ],
    "skipped_fixes": [],
    "warnings": [],
    "backup_path": "C:\\Users\\<USER>\\Desktop\\testing_knb\\text_backup_20250715_132919.txt"
  },
  "error": null,
  "processing_time": 3.5172345638275146,
  "model_used": "gpt-4o-mini",
  "timestamp": "2025-07-15T07:59:22.858633",
  "metadata": null
}</pre>
            </div>

            <div id="headers-8" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:08 GMT",
  "server": "uvicorn",
  "content-length": "34",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Batch File Analysis</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/batch</p>
            <p><strong>Category:</strong> Batch Operations</p>
            <p><strong>Response Time:</strong> 9135.6 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-9')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-9')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-9')">Headers</button>
            </div>

            <div id="request-9" class="tabcontent">
                <h4>Request Body</h4>
                <pre>{
  "files": [
    {
      "operation": "review",
      "file_path": "src/models/user.py",
      "file_content": "class User:\n    def __init__(self, name, email):\n        self.name = name\n        self.email = email\n    \n    def get_info(self):\n        return self.name + ' - ' + self.email",
      "language": "python",
      "model": "gpt-4o-mini"
    },
    {
      "operation": "security-scan",
      "file_path": "src/auth/validator.js",
      "file_content": "function validateUser(input) {\n  eval(input.code);\n  return true;\n}",
      "language": "javascript",
      "model": "gpt-4o-mini"
    }
  ],
  "operation": "batch-process",
  "parallel_processing": true,
  "max_concurrent": 5
}
</pre>
            </div>

            <div id="response-9" class="tabcontent">
                <h4>Response Body</h4>
                <pre></pre>
            </div>

            <div id="headers-9" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:19 GMT",
  "server": "uvicorn",
  "content-length": "487",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Add to Queue</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/queue</p>
            <p><strong>Category:</strong> Queue Management</p>
            <p><strong>Response Time:</strong> 2066.2 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-10')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-10')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-10')">Headers</button>
            </div>

            <div id="request-10" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"operation\": \"review\",\n  \"file_path\": \"src/services/payment.py\",\n  \"file_content\": \"import requests\\n\\ndef process_payment(amount, card_number):\\n    # Send payment to external API\\n    response = requests.post('https://api.payment.com/charge', {\\n        'amount': amount,\\n        'card': card_number\\n    })\\n    return response.json()\",\n  \"language\": \"python\",\n  \"model\": \"gpt-4o-mini\",\n  \"priority\": \"HIGH\",\n  \"max_retries\": 3\n}"</pre>
            </div>

            <div id="response-10" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "item_id": "2ec82c81-6b8e-4fbc-85db-ea1a59ae4e42",
  "operation": "review",
  "priority": "HIGH",
  "queue_position": 0
}</pre>
            </div>

            <div id="headers-10" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:28 GMT",
  "server": "uvicorn",
  "content-length": "123",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Get Queue Item Status</h3>
                <div>
                    <span class="method GET">GET</span>
                    <span class="status status-4xx">404</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/queue/status/{{queueItemId}}</p>
            <p><strong>Category:</strong> Queue Management</p>
            <p><strong>Response Time:</strong> 2054.3 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-11')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-11')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-11')">Headers</button>
            </div>

            <div id="request-11" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-11" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "detail": "Not Found"
}</pre>
            </div>

            <div id="headers-11" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:30 GMT",
  "server": "uvicorn",
  "content-length": "22",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Get Queue Stats</h3>
                <div>
                    <span class="method GET">GET</span>
                    <span class="status status-4xx">404</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/queue/stats</p>
            <p><strong>Category:</strong> Queue Management</p>
            <p><strong>Response Time:</strong> 2057.8 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-12')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-12')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-12')">Headers</button>
            </div>

            <div id="request-12" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-12" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "detail": "Queue item not found"
}</pre>
            </div>

            <div id="headers-12" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:33 GMT",
  "server": "uvicorn",
  "content-length": "33",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Get Change History</h3>
                <div>
                    <span class="method GET">GET</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/changes?limit=20</p>
            <p><strong>Category:</strong> Safety & Rollback</p>
            <p><strong>Response Time:</strong> 2105.3 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-13')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-13')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-13')">Headers</button>
            </div>

            <div id="request-13" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-13" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "changes": [],
  "count": 0,
  "limit": 20
}</pre>
            </div>

            <div id="headers-13" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:35 GMT",
  "server": "uvicorn",
  "content-length": "50",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Get Audit Log</h3>
                <div>
                    <span class="method GET">GET</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/audit?limit=50</p>
            <p><strong>Category:</strong> Safety & Rollback</p>
            <p><strong>Response Time:</strong> 2052.1 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-14')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-14')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-14')">Headers</button>
            </div>

            <div id="request-14" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-14" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "audit_entries": [],
  "count": 0,
  "limit": 50
}</pre>
            </div>

            <div id="headers-14" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:38 GMT",
  "server": "uvicorn",
  "content-length": "56",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Rollback Change</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-4xx">400</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/rollback/change/{{changeId}}</p>
            <p><strong>Category:</strong> Safety & Rollback</p>
            <p><strong>Response Time:</strong> 2053.1 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-15')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-15')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-15')">Headers</button>
            </div>

            <div id="request-15" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-15" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "detail": "Change record {{changeId}} not found"
}</pre>
            </div>

            <div id="headers-15" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:41 GMT",
  "server": "uvicorn",
  "content-length": "49",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Rollback Operation</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/rollback/operation/{{operationId}}</p>
            <p><strong>Category:</strong> Safety & Rollback</p>
            <p><strong>Response Time:</strong> 2042.2 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-16')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-16')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-16')">Headers</button>
            </div>

            <div id="request-16" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-16" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": false,
  "operation_id": "{{operationId}}",
  "message": "No changes found for operation {{operationId}}",
  "rolled_back_changes": [],
  "changes_count": 0
}</pre>
            </div>

            <div id="headers-16" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:43 GMT",
  "server": "uvicorn",
  "content-length": "152",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Get Analysis Statistics</h3>
                <div>
                    <span class="method GET">GET</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/stats</p>
            <p><strong>Category:</strong> Analytics & Stats</p>
            <p><strong>Response Time:</strong> 2072.6 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-17')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-17')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-17')">Headers</button>
            </div>

            <div id="request-17" class="tabcontent">
                <h4>Request Body</h4>
                <pre>No request body</pre>
            </div>

            <div id="response-17" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "stats": {
    "total_operations": 15,
    "operations_by_type": {
      "review": 5,
      "document": 6,
      "security-scan": 4
    },
    "average_processing_time": 7.259,
    "success_rate": 1.0,
    "most_analyzed_files": [
      {
        "file_path": "src/utils/calculator.py",
        "analysis_count": 3
      },
      {
        "file_path": "src/api/user_controller.js",
        "analysis_count": 2
      },
      {
        "file_path": "src/services/data_processor.py",
        "analysis_count": 2
      },
      {
        "file_path": "src/api/products.py",
        "analysis_count": 2
      },
      {
        "file_path": "src/api/auth_service.py",
        "analysis_count": 2
      },
      {
        "file_path": "src/auth/login.php",
        "analysis_count": 2
      },
      {
        "file_path": "src/api/file_upload.js",
        "analysis_count": 2
      }
    ]
  }
}</pre>
            </div>

            <div id="headers-17" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:45 GMT",
  "server": "uvicorn",
  "content-length": "601",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <h3>Cleanup Old Data</h3>
                <div>
                    <span class="method POST">POST</span>
                    <span class="status status-2xx">200</span>
                </div>
            </div>
            <p><strong>URL:</strong> http://localhost:45213/file-actions/cleanup</p>
            <p><strong>Category:</strong> Analytics & Stats</p>
            <p><strong>Response Time:</strong> 2053.8 ms</p>

            <div class="tab">
                <button class="tablinks" onclick="openTab(event, 'request-18')">Request</button>
                <button class="tablinks" onclick="openTab(event, 'response-18')">Response</button>
                <button class="tablinks" onclick="openTab(event, 'headers-18')">Headers</button>
            </div>

            <div id="request-18" class="tabcontent">
                <h4>Request Body</h4>
                <pre>"{\n  \"days_to_keep\": 30,\n  \"cleanup_backups\": true,\n  \"cleanup_analysis_records\": true\n}"</pre>
            </div>

            <div id="response-18" class="tabcontent">
                <h4>Response Body</h4>
                <pre>{
  "success": true,
  "files_removed": 0,
  "records_cleaned": 0,
  "days_old": 30,
  "message": "Cleaned up 0 backup files and 0 change records older than 30 days"
}</pre>
            </div>

            <div id="headers-18" class="tabcontent">
                <h4>Request Headers</h4>
                <pre>{
  "Content-Type": "application/json",
  "x-session": "48ece17b-605c-4c68-bb4f-fa8b662467e7"
}</pre>
                <h4>Response Headers</h4>
                <pre>{
  "date": "Tue, 15 Jul 2025 07:24:49 GMT",
  "server": "uvicorn",
  "content-length": "146",
  "content-type": "application/json"
}</pre>
            </div>
        </div>

    </div>

    <script>
    function openTab(evt, tabName) {
        var i, tabcontent, tablinks;
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }
        document.getElementById(tabName).style.display = "block";
        evt.currentTarget.className += " active";
    }

    // Set the first tab of each endpoint as active by default
    window.onload = function() {
        var tabs = document.getElementsByClassName('tab');
        for (var i = 0; i < tabs.length; i++) {
            var firstTab = tabs[i].getElementsByClassName('tablinks')[0];
            var event = new Event('click');
            firstTab.dispatchEvent(event);
        }
    };
    </script>
</body>
</html>
