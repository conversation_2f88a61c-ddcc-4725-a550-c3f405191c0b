import time
import json
import yaml
import requests
from pydantic import BaseModel
from typing import Literal, Optional
from pathlib import Path

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_file_stats, log_operation_stats, log_api_call_stats
from client_server.services.swagger.resolver import SwaggerSpecRefsResolver
from client_server.swagger.swagger_parser import _extract_endpoints
from . import route


class SwaggerListRequest(BaseModel):
    """Request model for listing Swagger endpoints"""

    type: Literal["file", "url", "content"]
    value: str
    full_specs: Optional[bool] = False


@route("POST", "/swagger_list")
async def swagger_list(data: SwaggerListRequest):
    """List Swagger endpoints from various sources"""
    start_time = time.time()
    LOGGER.info(
        f"Processing Swagger list request - Type: {data.type}, "
        f"Full specs: {data.full_specs}"
    )

    base_uri = "http://your-api-base-url"
    content = None

    try:
        # Load Swagger content based on type
        content_start = time.time()
        if data.type == "file":
            LOGGER.info(f"Loading Swagger from file: {data.value}")
            file_path = Path(data.value)
            if not file_path.exists():
                raise FileNotFoundError(f"Swagger file not found: {data.value}")

            file_size = log_file_stats(file_path)
            LOGGER.debug(f"Swagger file size: {file_size/1024:.2f}KB")

            with open(data.value, "r") as file:
                content = json.loads(file.read())

        elif data.type == "url":
            LOGGER.info(f"Loading Swagger from URL: {data.value}")
            api_start = time.time()
            response = requests.get(
                url=data.value,
                headers={"Content-Type": "application/json"},
            )
            api_time = time.time() - api_start

            log_api_call_stats(
                data.value,
                "GET",
                response.status_code,
                api_time,
                len(response.content),
            )

            if response.status_code == 200:
                content = response.json()
            else:
                LOGGER.error(
                    f"HTTP error fetching Swagger spec: {response.status_code}"
                )
                return {"error": f"HTTP error: {response.status_code}"}

        elif data.type == "content":
            LOGGER.info("Loading Swagger from provided content")
            try:
                content = json.loads(data.value)
            except Exception as e:
                try:
                    # the content is in yaml format. convert it to json
                    content = yaml.safe_load(data.value)
                    content = json.loads(json.dumps(content))
                except Exception as e:
                    LOGGER.error(f"Error loading Swagger content: {e}")
                    return {"error": f"Error loading Swagger content: {e}"}

        else:
            LOGGER.error(f"Invalid request type: {data.type}")
            return {"error": "Invalid type. Must be 'file', 'url', or 'content'"}

        content_time = time.time() - content_start
        LOGGER.debug(f"Content loading completed in {content_time:.3f}s")

        # Process the content
        processing_start = time.time()
        result = None

        if not data.full_specs:
            # Return simple endpoints list
            LOGGER.debug("Generating simple endpoints list")
            result = _extract_endpoints(swagger_data=content)
            LOGGER.info(f"Extracted {len(result)} endpoints")

        else:
            # Generate full endpoint specifications
            LOGGER.debug("Generating full endpoint specifications")
            swagger_spec_resolver = SwaggerSpecRefsResolver()

            resolution_start = time.time()
            resolved_spec = swagger_spec_resolver.resolve_references(
                swagger_dict=content, base_uri=base_uri
            )
            resolution_time = time.time() - resolution_start
            LOGGER.debug(f"Reference resolution completed in {resolution_time:.3f}s")

            spec_generation_start = time.time()
            spec_list = swagger_spec_resolver.generate_endpoint_specs(resolved_spec)
            spec_generation_time = time.time() - spec_generation_start
            LOGGER.debug(f"Spec generation completed in {spec_generation_time:.3f}s")

            specs = []
            for spec in spec_list:
                specs.append(
                    {
                        "path": spec["path"],
                        "method": spec["method"],
                        "spec": spec["spec"],
                    }
                )
            result = specs
            LOGGER.info(f"Generated full specs for {len(specs)} endpoints")

        processing_time = time.time() - processing_start
        total_time = time.time() - start_time

        # Log operation statistics
        stats = log_operation_stats(
            "swagger_list", start_time, len(result) if result else 0, success=True
        )

        LOGGER.info(
            f"Swagger list request completed - "
            f"Type: {data.type}, Full specs: {data.full_specs}, "
            f"Processing time: {processing_time:.3f}s, "
            f"Total time: {total_time:.3f}s"
        )

        return result

    except json.JSONDecodeError as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Invalid JSON format after {total_time:.3f}s: {e}")
        return {"error": "Invalid JSON format"}

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error processing swagger request after {total_time:.3f}s: {e}")
        return {"error": str(e)}
