from typing import Optional

from client_server.utils.platform_detector import PlatformDetector

from . import IEmbeddingBackend
from client_server.utils.interet_check import check_internet_connection


class EmbeddingInferenceBuilder:
    """
    A builder class that selects and instantiates the appropriate embedding backend
    based on the platform architecture.
    """

    _instance: Optional[IEmbeddingBackend] = None

    @staticmethod
    def create(is_local: bool = False) -> IEmbeddingBackend:
        """
        Creates and returns the appropriate embedding backend based on platform architecture.
        If is_local is True, it will use the local backend. In this case, it will check if the platform is ARM and if it is, it will use the InferX backend.
        If is_local is False, it will use the cloud backend.

        Returns:
            IEmbeddingBackend: The appropriate embedding backend instance
        """

        # from .inferx import InferXEmbeddingsBackend

        # EmbeddingInferenceBuilder._instance = InferXEmbeddingsBackend()
        # return EmbeddingInferenceBuilder._instance

        is_local = is_local or not check_internet_connection()
        if is_local:
            if PlatformDetector.is_snapdragon_arm():
                from .inferx import InferXEmbeddingsBackend

                EmbeddingInferenceBuilder._instance = InferXEmbeddingsBackend()
            else:
                from .ollama import OllamaEmbeddingsBackend

                EmbeddingInferenceBuilder._instance = OllamaEmbeddingsBackend()
        else:
            from .cloud import CloudEmbeddingsBackend

            EmbeddingInferenceBuilder._instance = CloudEmbeddingsBackend()

        return EmbeddingInferenceBuilder._instance

    @staticmethod
    def dispose():
        """
        Disposes of the current embedding backend instance if it exists.
        """
        if EmbeddingInferenceBuilder._instance is not None:
            EmbeddingInferenceBuilder._instance = None
