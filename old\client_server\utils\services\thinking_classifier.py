"""
Thinking Classification Utility

This module provides functionality to classify user chat messages into different
thinking modes using Hugging Face's zero-shot classification pipeline.

The classifier categorizes messages into:
- fast_thinking: Quick, intuitive responses
- slow_thinking: Deliberate, analytical responses  
- calculative_thinking: Mathematical, computational responses
"""

import logging
from typing import Dict, Optional, Tuple
from functools import lru_cache

from client_server.core.logger import LOGGER

# Global classifier instance
_classifier = None


class ThinkingClassifier:
    """
    A classifier that determines the type of thinking required for user messages.
    
    Uses Hugging Face's zero-shot classification with the brahmairesearch/TFS-ZS-MNLI model
    to categorize user input into different thinking modes.
    """
    
    def __init__(self, model_name: str = "brahmairesearch/TFS-ZS-MNLI"):
        """
        Initialize the thinking classifier.
        
        Args:
            model_name: The Hugging Face model to use for classification
        """
        self.model_name = model_name
        self.candidate_labels = ["fast_thinking", "slow_thinking", "calculative_thinking"]
        self._pipeline = None
        
    def _get_pipeline(self):
        """
        Lazy initialization of the classification pipeline.
        
        Returns:
            The initialized classification pipeline
        """
        if self._pipeline is None:
            try:
                from transformers import pipeline
                LOGGER.info(f"Initializing thinking classifier with model: {self.model_name}")
                self._pipeline = pipeline(
                    "zero-shot-classification", 
                    model=self.model_name,
                    device=-1  # Use CPU to avoid GPU dependencies
                )
                LOGGER.info("Thinking classifier initialized successfully")
            except ImportError as e:
                LOGGER.error(f"Failed to import transformers: {e}")
                raise ImportError(
                    "transformers library is required for thinking classification. "
                    "Please install it with: pip install transformers"
                )
            except Exception as e:
                LOGGER.error(f"Failed to initialize thinking classifier: {e}")
                raise
                
        return self._pipeline
    
    def classify(self, text: str, confidence_threshold: float = 0.3) -> Tuple[str, float]:
        """
        Classify a text message into a thinking mode.
        
        Args:
            text: The user message to classify
            confidence_threshold: Minimum confidence required for classification
            
        Returns:
            Tuple of (thinking_mode, confidence_score)
            
        Raises:
            ValueError: If text is empty or None
            RuntimeError: If classification fails
        """
        if not text or not text.strip():
            raise ValueError("Text cannot be empty or None")
            
        try:
            pipeline = self._get_pipeline()
            
            # Perform classification
            result = pipeline(text.strip(), self.candidate_labels, multi_label=False)
            
            # Extract the top prediction
            top_label = result['labels'][0]
            top_score = result['scores'][0]
            
            LOGGER.debug(f"Classification result for '{text[:50]}...': {top_label} (confidence: {top_score:.3f})")
            
            # Apply confidence threshold
            if top_score < confidence_threshold:
                LOGGER.warning(f"Low confidence classification ({top_score:.3f}), defaulting to slow_thinking")
                return "slow_thinking", top_score
                
            return top_label, top_score
            
        except Exception as e:
            LOGGER.error(f"Error during text classification: {e}")
            # Fallback to slow_thinking on error
            return "slow_thinking", 0.0
    
    def classify_with_details(self, text: str) -> Dict[str, float]:
        """
        Classify text and return detailed scores for all categories.
        
        Args:
            text: The user message to classify
            
        Returns:
            Dictionary mapping thinking modes to confidence scores
        """
        if not text or not text.strip():
            raise ValueError("Text cannot be empty or None")
            
        try:
            pipeline = self._get_pipeline()
            result = pipeline(text.strip(), self.candidate_labels, multi_label=False)
            
            # Create detailed results dictionary
            detailed_results = {}
            for label, score in zip(result['labels'], result['scores']):
                detailed_results[label] = score
                
            LOGGER.debug(f"Detailed classification for '{text[:50]}...': {detailed_results}")
            return detailed_results
            
        except Exception as e:
            LOGGER.error(f"Error during detailed classification: {e}")
            # Return default scores on error
            return {
                "slow_thinking": 1.0,
                "fast_thinking": 0.0,
                "calculative_thinking": 0.0
            }


@lru_cache(maxsize=1)
def get_thinking_classifier() -> ThinkingClassifier:
    """
    Get a singleton instance of the thinking classifier.
    
    Returns:
        ThinkingClassifier: The global classifier instance
    """
    global _classifier
    if _classifier is None:
        _classifier = ThinkingClassifier()
    return _classifier


def classify_thinking_mode(text: str, confidence_threshold: float = 0.3) -> Tuple[str, float]:
    """
    Convenience function to classify a text message into a thinking mode.
    
    Args:
        text: The user message to classify
        confidence_threshold: Minimum confidence required for classification
        
    Returns:
        Tuple of (thinking_mode, confidence_score)
    """
    classifier = get_thinking_classifier()
    return classifier.classify(text, confidence_threshold)


def get_thinking_tag(text: str, confidence_threshold: float = 0.3) -> str:
    """
    Get the thinking mode tag for a text message.
    
    Args:
        text: The user message to classify
        confidence_threshold: Minimum confidence required for classification
        
    Returns:
        String tag in format "<thinking_mode>" (e.g., "<fast_thinking>")
    """
    thinking_mode, _ = classify_thinking_mode(text, confidence_threshold)
    return f"<{thinking_mode}>"


def classify_message_content(message: Dict) -> Tuple[str, float]:
    """
    Extract content from a message dictionary and classify it.
    
    Args:
        message: Message dictionary with 'content' key
        
    Returns:
        Tuple of (thinking_mode, confidence_score)
        
    Raises:
        ValueError: If message doesn't contain valid content
    """
    if not isinstance(message, dict):
        raise ValueError("Message must be a dictionary")
        
    content = message.get("content", "")
    if not content or not isinstance(content, str):
        raise ValueError("Message must contain valid string content")
        
    return classify_thinking_mode(content)
