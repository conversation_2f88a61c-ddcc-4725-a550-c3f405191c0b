"""
Tool schemas for LLM tool calling.

This module defines the tool schemas that LLMs can understand and call,
mapping them to existing action handlers.
"""

from typing import Any, Optional


def get_tool_schemas(
    context_types: Optional[set[str]] = None,
    web_search_enabled: bool = False
) -> list[dict[str, Any]]:
    """
    Define tool schemas that the LLM can understand and call.
    Maps to existing action handlers.

    Args:
        context_types: Set of context types present in the message payload
        web_search_enabled: Whether web search is enabled for this request

    Returns:
        List of tool schemas conditionally selected based on context and settings
    """
    # Base tools that are always available (for future extensibility)
    base_tools = []

    # Context-based tools
    context_tools = []

    # Conditional tools
    conditional_tools = []

    # Add context_search tool if relevant context types are present
    if context_types and any(ctx_type in ["docs", "codebase", "git"] for ctx_type in context_types):
        context_tools.append({
            "type": "function",
            "function": {
                "name": "context_search",
                "description": "Search for relevant information from a knowledgebase. The function is used when there is not enough contextual information available for the user's query. The type of queries that are to be searched for are code-based. You can ask something related to a function, or code-block, using only that you can answer any general level queries too. Feel free to call it multiple times to get more responses.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The query to search for in the knowledgebase. The query should be tuned as described in the description of the function above."
                        },
                        "kbid": {
                            "type": "string",
                            "description": "The ID of the knowledgebase to search in. If there is no available ID, you cannot call this function."
                        }
                    },
                    "required": ["query", "kbid"]
                }
            }
        })

    # Add folder_search tool if folder context type is present
    if context_types and "folder" in context_types:
        context_tools.append({
            "type": "function",
            "function": {
                "name": "folder_search",
                "description": "Search for relevant information from a knowledgebase. The function is used when there is not enough contextual information available for the user's query.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The query to search for in the knowledgebase."
                        },
                        "folder_path": {
                            "type": "string",
                            "description": "The path of the folder to search in."
                        },
                        "kbid": {
                            "type": "string",
                            "description": "The ID of the knowledgebase to search in."
                        }
                    },
                    "required": ["query", "folder_path", "kbid"]
                }
            }
        })

    # Add web_search tool if web search is enabled
    if web_search_enabled:
        conditional_tools.append({
            "type": "function",
            "function": {
                "name": "web_search",
                "description": "Perform web search using Gemini API and extracts concise, relevant information.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The query to search for on the web."
                        }
                    },
                    "required": ["query"]
                }
            }
        })

    # Combine all tools
    return base_tools + context_tools + conditional_tools


def get_all_tool_schemas() -> list[dict[str, Any]]:
    """
    Get all available tool schemas (backward compatibility function).
    This function returns all tools without any conditional logic.
    """
    return get_tool_schemas(
        context_types={"docs", "codebase", "git", "folder"},
        web_search_enabled=True
    )
