import ssl
import certifi

# Server Port Configuration
HTTP_PORT = 45213  # REST API endpoints
WEBSOCKET_PORT = 45214  # Socket.IO WebSocket connections

# Legacy support - maintain backward compatibility
PORT = HTTP_PORT

PRELOAD_EMBEDDING_MODEL = False
PRELOAD_CHAT_MODEL = False
PRELOAD_INLINE_SUGGESTION_MODEL = False

SSL_CERT_FILE = certifi.where()
SSL_CONTEXT = ssl.create_default_context(cafile=SSL_CERT_FILE)

# Socket.IO Configuration for Sleep/Wake Resilience
SOCKETIO_PING_INTERVAL = 25  # Seconds between ping packets (increased for sleep mode)
SOCKETIO_PING_TIMEOUT = 60   # Seconds to wait for ping response before disconnecting
SOCKETIO_MAX_HTTP_BUFFER_SIZE = 100 * 1024 * 1024  # 100 MB limit for Socket.IO messages
SOCKETIO_SESSION_CLEANUP_INTERVAL = 60  # Seconds between session cleanup runs
SOCKETIO_SESSION_TIMEOUT = 300  # Seconds before old sessions are cleaned up
