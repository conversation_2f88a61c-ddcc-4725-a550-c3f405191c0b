# Build Script (`client_server/cmd/build.py`)

This script is responsible for building the client server application into a standalone executable for different operating systems. It uses `PyInstaller` to bundle the application and its dependencies.

## Key Features

- **Cross-Platform Builds**: Generates executables for Windows, macOS (Darwin), and Linux.
- **Architecture Support**: Supports various CPU architectures like x86, x64 (amd64), and arm64 (aarch64).
- **Dependency Management**: Automatically finds and includes necessary data files, such as the `anthropic_tokenizer.json` from the `litellm` library.
- **Streamlined Process**: The build process is divided into clear, sequential steps: pre-build cleanup, building, and packaging.

## Build Process Overview

The build process is orchestrated by the `main` function and consists of the following classes and steps:

1.  **`Prebuild`**:

    - Cleans up previous build artifacts by deleting the `build/` and `dist/` directories. This ensures a fresh build every time.

2.  **`Builder`**:

    - Detects the current operating system and architecture to create a platform-specific build.
    - Prompts the user to enter the target Python version (e.g., `3.11`), which is used to locate dependencies within the correct virtual environment path.
    - Locates the `anthropic_tokenizer.json` file, which is a critical dependency for the application's tokenization functionality.
    - Constructs and runs the `pyinstaller` command with appropriate arguments for the target platform, including:
      - Hidden imports for libraries that `PyInstaller` might miss (e.g., `tiktoken`, `socketio`).
      - The path to the main server script (`client_server/cmd/server.py`).
      - Output paths for build and distribution files.

3.  **`Signing`**:

    - For macOS, this step packages the final application bundle into a `.zip` file for distribution. Code signing is currently skipped.
    - For other operating systems (Windows, Linux), this step does nothing.

4.  **`Postbuild`**:
    - A placeholder for any future post-build actions. Currently, it performs no operations.

## How to Run the Build Script

To build the application, run the script from the project root:

```sh
python -m client_server.cmd.build
```

The script will prompt for the Python version used in the project's virtual environment. The final distributable will be located in the `dist/` directory, inside a folder named after the platform (e.g., `dist/darwin`).
