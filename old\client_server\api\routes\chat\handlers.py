"""
Chat processing handlers and utility functions.

This module contains the core chat processing logic including:
- Local chat processing (ECO mode)
- Server-based chat processing (NORMAL/PRO modes)
- Utility functions for file handling and dependencies
"""

import asyncio
import json
import os
import time
import traceback
from typing import Any, AsyncGenerator
from uuid import uuid4

import requests
import tqdm

from client_server.utils.models.knowledgebase import QdrantKnowledgeBase
from client_server.utils.actions.swagger_search import (
    _perform_swagger_search,
    process_swagger_search,
)
from client_server.utils.actions.context_search import (
    process_context_search,
    _perform_local_search,
)
from client_server.utils.actions.folder_search import (
    _perform_folder_search,
    process_folder_search,
)
from client_server.services.tokenization.utils import (
    TokenizationBuilder,
    ensure_messages_within_token_limit,
)
from client_server.utils.model_selector import ModelSelector
from client_server.api.events.connection import update_session_activity
from client_server.utils.actions.web_search import process_web_search
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import (
    log_memory_usage,
    log_file_stats,
    log_execution_time,
    log_api_call_stats,
    log_system_info,
    log_operation_stats,
)
from client_server.utils.platform_detector import PlatformDetector
from client_server.core.state import G_BASE_URL, G_CANCELLED_REQUEST_IDS
from client_server.utils.actions.utils import SearchReferences
from client_server.core.constants import SSL_CERT_FILE
from client_server.services.dependencies.ollama import OllamaDependency
from client_server.utils.path_selector import PathSelector
from client_server.services.dependencies import DependencyStatus
from client_server.utils.router.litellm_router import get_litellm_session_router

from .models import ChatStreamRequest, ChatStreamActionChunk, ChatStreamContentChunk
from .streaming import HTTPStreamEventEmitter

# Import tool call handler from the tool_call module
from client_server.utils.tool_call import ToolCallHandler

# Import for ECO mode (local inference) - only used in process_local_chat_request_http
try:
    from client_server.services.inference.utils import InferenceBuilder
except ImportError:
    InferenceBuilder = None
    LOGGER.warning("InferenceBuilder not available - ECO mode may not work")


MESSAGE_DELIMITER = "<__!!__END__!!__>"


def format_filepath(filepath: str):
    """Format file path based on platform"""
    if PlatformDetector.is_macos():
        formatted_path = f"/{filepath}"
        LOGGER.debug(f"Formatted macOS path: {filepath} -> {formatted_path}")
        return formatted_path
    LOGGER.debug(f"Using original path: {filepath}")
    return filepath


def ensure_eco_mode_dependencies():
    """Ensure Ollama dependencies are installed for ECO mode"""
    dependency_start = time.time()
    LOGGER.info("Starting Ollama dependency setup for ECO mode")
    log_memory_usage("before_ollama_setup")

    ollama_dependency = OllamaDependency(PathSelector.get_cache_path() / "ollama")

    # Create a progress bar for installation progress
    pbar = None
    installation_start = None

    try:
        status_count = 0
        for status, data in ollama_dependency.ensure():
            status_count += 1

            if status == DependencyStatus.READY:
                LOGGER.info(f"Ollama ready: {data}")
                continue

            if status == DependencyStatus.INSTALLED:
                if installation_start:
                    install_time = time.time() - installation_start
                    LOGGER.info(
                        f"Ollama installed successfully in {install_time:.2f}s: {data}"
                    )
                else:
                    LOGGER.info(f"Ollama installed: {data}")
                continue

            if status == DependencyStatus.ERROR:
                LOGGER.error(f"Ollama error: {data}")
                continue

            if status == DependencyStatus.MISSING:
                LOGGER.warning(f"Ollama missing: {data}")
                continue

            if status == DependencyStatus.INSTALLING:
                if installation_start is None:
                    installation_start = time.time()
                    LOGGER.info("Starting Ollama installation")

                # Handle progress updates
                if isinstance(data, float):
                    if pbar is None:
                        pbar = tqdm.tqdm(total=100, desc="Installing Ollama")
                    pbar.n = round(data)
                    pbar.refresh()

                    if round(data) % 10 == 0:  # Log every 10%
                        LOGGER.debug(f"Ollama installation progress: {data:.1f}%")
                else:
                    LOGGER.info(f"Ollama installation: {data}")
                continue

            # Handle other status messages
            if isinstance(data, str):
                LOGGER.debug(f"Ollama status {status}: {data}")

    except Exception as e:
        LOGGER.error(f"Error during Ollama dependency setup: {e}")
        LOGGER.error(f"Ollama setup error traceback: {traceback.format_exc()}")
        raise
    finally:
        if pbar is not None:
            pbar.close()

    dependency_time = time.time() - dependency_start
    log_memory_usage("after_ollama_setup")
    LOGGER.info(f"Ollama dependency setup completed in {dependency_time:.2f}s")


async def process_local_chat_request_http(
    event_emitter: HTTPStreamEventEmitter,
    payload: ChatStreamRequest
) -> AsyncGenerator[str, None]:
    """
    HTTP streaming version of process_local_chat_request.
    Process chat request locally using ECO mode with HTTP streaming.
    """
    overall_start_time = time.time()
    log_memory_usage("http_local_chat_start")

    LOGGER.info(
        f"Processing HTTP local chat request - "
        f"Request ID: {payload.request_id}, Messages: {len(payload.messages)}"
    )

    sequence_messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant developed by CodeMate AI that can answer questions and help with tasks.",
        }
    ]
    search_results = {
        "request_id": payload.request_id,
        "results": [],
    }

    try:
        message_processing_start = time.time()
        total_contexts = sum(len(message.get("context", [])) for message in payload.messages)
        LOGGER.info(f"Processing {total_contexts} contexts in local mode")

        for msg_idx, message in enumerate(payload.messages):
            LOGGER.debug(
                f"Processing message {msg_idx+1}/{len(payload.messages)}: {message.get('role', 'unknown')}"
            )

            if message.get("role") == "user":
                message_contexts = message.get("context", [])
                for ctx_idx, context in enumerate(message_contexts):
                    context_start = time.time()
                    LOGGER.debug(
                        f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type}"
                    )

                    formatted_context = ""
                    additional_content = ""

                    context_type = context.get("type") if isinstance(context, dict) else getattr(context, "type", None)

                    if context_type == "file":
                        context_path = context.get("path") if isinstance(context, dict) else getattr(context, "path", None)
                        if context_path:
                            try:
                                file_size = log_file_stats(context_path)
                                read_start = time.time()

                                with open(context_path, "r", encoding="utf-8") as e:
                                    file_content = e.read()
                                    if isinstance(context, dict):
                                        context["content"] = file_content
                                    else:
                                        context.content = file_content

                                read_time = time.time() - read_start
                                content_length = len(file_content)
                                LOGGER.debug(
                                    f"File read - Path: {context_path}, Size: {file_size} bytes, "
                                    f"Content: {content_length} chars, Read time: {read_time:.2f}s"
                                )
                            except Exception as e:
                                LOGGER.error(f"Failed to read file {context_path}: {e}")
                                if isinstance(context, dict):
                                    context["content"] = ""
                                else:
                                    context.content = ""

                        context_name = context.get("name") if isinstance(context, dict) else getattr(context, "name", "")
                        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
                        formatted_context = (
                            f"File Name: {context_name}\n"
                            f"File Path: {context_path}\n"
                            f"File Content:\n{context_content}\n\n"
                        )

                    elif context_type == "terminal":
                        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
                        formatted_context = f"Terminal:\n{context_content}\n\n"

                    elif context_type == "warnings":
                        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
                        formatted_context = f"Warnings:\n{context_content}\n\n"

                    elif context_type == "errors":
                        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
                        formatted_context = f"Errors:\n{context_content}\n\n"

                    elif context_type == "commit":
                        context_content = context.get("content") if isinstance(context, dict) else getattr(context, "content", "")
                        formatted_context = (
                            f"COMMIT INFORMATION:\n{context_content}\n\n"
                        )

                    elif context_type == "folder":
                        context_path = context.get("path") if isinstance(context, dict) else getattr(context, "path", None)
                        context_kbid = context.get("kbid") if isinstance(context, dict) else getattr(context, "kbid", None)
                        context_id = context.get("id") if isinstance(context, dict) else getattr(context, "id", None)

                        LOGGER.info(
                            f"Processing folder context - Path: {context_path}, KBID: {context_kbid}"
                        )

                        if not context_path:
                            LOGGER.warning(f"Ignoring folder without path: {context}")
                            continue

                        if QdrantKnowledgeBase.exists_id(context_kbid or ""):
                            LOGGER.info("Knowledge base found for folder search")
                            # Emit searching event before folder search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()

                            message_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
                            folder_contexts = await _perform_folder_search(
                                query=message_content.replace(
                                    f"<__$__{context_id}__$__>", context_path or ""
                                ),
                                index_name=context_kbid or "",
                                folder_path=context_path or "",
                                is_local=True,
                            )

                            search_time = time.time() - search_start
                            LOGGER.info(
                                f"Folder search completed - Found {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                text = folder_context["content"]["text"]
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                additional_content += f"FileName: {_filename} | FilePath: {_filepath} \n {text}\n"
                        else:
                            LOGGER.warning(
                                f"Knowledge base not found for ID: {context_kbid}"
                            )
                        print("Emitting folder search results")
                        await event_emitter.emit(
                            "chat_response_references", data=search_results
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": search_results
                        })
                        print("Folder search results emitted to client")
                        await asyncio.sleep(0.001)

                    elif context_type in ["docs", "codebase", "git"]:
                        context_name = context.get("name") if isinstance(context, dict) else getattr(context, "name", "")
                        context_kbid = context.get("kbid") if isinstance(context, dict) else getattr(context, "kbid", "")
                        context_path = context.get("path") if isinstance(context, dict) else getattr(context, "path", "")
                        context_id = context.get("id") if isinstance(context, dict) else getattr(context, "id", "")

                        LOGGER.info(
                            f"Processing {context_type} context - Name: {context_name}, KBID: {context_kbid}"
                        )

                        formatted_context = f"{context_type} {context_name}"
                        additional_content = (
                            f"\n\n[ Attached Knowledgebases:\n"
                            f"Name: {context_name}    |    KNOWLEDGEBASE ID: {context_kbid}]\n\n"
                        )

                        message_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
                        search_query = message_content.replace(
                            f"<__$__{context_id}__$__>",
                            context_path or context_name or "",
                        )

                        if context_type == "codebase":
                            # Emit searching event before codebase search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            folder_contexts = await _perform_folder_search(
                                query=search_query,
                                index_name=context_kbid or "",
                                folder_path="",
                                is_local=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Codebase search completed - {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                text = folder_context["content"]["text"]
                                additional_content += text + "\n\n"

                            print("Emitting codebase search results")
                            await event_emitter.emit(
                                "chat_response_references", data=search_results
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_references",
                                "data": search_results
                            })
                            print("Codebase search results emitted to client")
                            await asyncio.sleep(0.001)

                        elif context_type == "docs":
                            # Emit searching event before docs search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            docs_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context_kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Docs search completed - {len(docs_contexts)} results in {search_time:.2f}s"
                            )

                            for docs_context in docs_contexts:
                                text = docs_context["content"]["text"]
                                additional_content += text + "\n\n"

                        elif context_type == "github":
                            # Emit searching event before git search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            git_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context_kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.info(
                                f"GitHub search completed - {len(git_contexts)} results in {search_time:.2f}s"
                            )
                            LOGGER.debug(
                                f"First GitHub context: {git_contexts[0] if git_contexts else 'None'}"
                            )

                            for git_context in git_contexts:
                                text = git_context["content"]["text"]
                                additional_content += text + "\n\n"

                    # Replace context placeholders in message content
                    current_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
                    current_content = current_content or ""
                    updated_content = (
                        current_content.replace(
                            f"<__$__{context_id}__$__>", formatted_context
                        )
                        + additional_content
                    )

                    if isinstance(message, dict):
                        message["content"] = updated_content
                    else:
                        message.content = updated_content

                    context_time = time.time() - context_start
                    LOGGER.debug(
                        f"Context {context_type} processed in {context_time:.2f}s"
                    )

                final_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
                sequence_messages.append(
                    {
                        "role": "user",
                        "content": final_content,
                    }
                )

                content_length = len(final_content)
                LOGGER.debug(f"Added user message with {content_length} characters")
            else:
                assistant_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
                sequence_messages.append(
                    {"role": "assistant", "content": assistant_content}
                )
                LOGGER.debug(f"Added assistant message")

        message_processing_time = time.time() - message_processing_start
        LOGGER.info(f"Message processing completed in {message_processing_time:.2f}s")

        # Apply token limiting to fit within the 70k token limit
        token_limiting_start = time.time()
        original_message_count = len(sequence_messages)

        sequence_messages = ensure_messages_within_token_limit(
            tokenizer=TokenizationBuilder.create(),
            messages=sequence_messages,
            max_tokens=70000,
            model=ModelSelector.chat(),
        )

        token_limiting_time = time.time() - token_limiting_start
        final_message_count = len(sequence_messages)

        LOGGER.info(
            f"Token limiting completed - Original: {original_message_count} messages, "
            f"Final: {final_message_count} messages, Time: {token_limiting_time:.2f}s"
        )

        if original_message_count != final_message_count:
            LOGGER.warning(
                f"Truncated {original_message_count - final_message_count} messages due to token limit"
            )

    except Exception as e:
        LOGGER.error(f"Error during HTTP local chat message processing: {e}")
        LOGGER.error(f"HTTP local chat processing error traceback: {traceback.format_exc()}")

        await event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        })
        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })
        return

    try:
        inference_start = time.time()
        LOGGER.info("Starting local LLM inference")
        log_memory_usage("before_local_inference")

        llm_backend = InferenceBuilder.create()
        selected_model = ModelSelector.chat()
        LOGGER.info(f"Using local model: {selected_model}")

        llm_response = llm_backend.stream(
            model=selected_model,
            messages=sequence_messages,
        )

        chat_id = str(uuid4())
        chunk_count = 0
        total_response_length = 0

        LOGGER.info(f"Starting response streaming with chat ID: {chat_id}")

        for i, chunk in enumerate(llm_response):
            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, skipping chunk")
                break

            if len(chunk.choices) <= 0:
                continue

            if chunk.choices[0].finish_reason is not None:
                LOGGER.info(
                    f"Stream finished with reason: {chunk.choices[0].finish_reason}"
                )
                break

            if chunk.choices[0].delta.content is not None:
                chunk_count += 1
                content = chunk.choices[0].delta.content
                total_response_length += len(content)

                if chunk_count <= 5:  # Log first few chunks
                    LOGGER.debug(f"Chunk {chunk_count}: {len(content)} chars")

                response_data = {
                    "type": "content",
                    "request_id": payload.request_id,
                    "conversation_id": "",
                    "data": {
                        "type": "content",
                        "memory_id": chat_id,
                        "chunk_index": i,
                        "content": content,
                    },
                }

                await event_emitter.emit("chat_response", data=response_data)
                yield event_emitter.format_as_sse({
                    "event": "chat_response",
                    "data": response_data
                })

        inference_time = time.time() - inference_start
        avg_time_per_chunk = inference_time / chunk_count if chunk_count > 0 else 0

        LOGGER.info(
            f"Local inference completed - Chunks: {chunk_count}, "
            f"Total response: {total_response_length} chars, "
            f"Time: {inference_time:.2f}s, "
            f"Avg per chunk: {avg_time_per_chunk:.3f}s"
        )

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("http_local_chat_complete")

        LOGGER.info(f"HTTP local chat request completed successfully in {overall_time:.2f}s")

    except Exception as e:
        inference_time = (
            time.time() - inference_start if "inference_start" in locals() else 0
        )
        LOGGER.error(
            f"Error during HTTP local chat inference after {inference_time:.2f}s: {e}"
        )
        LOGGER.error(f"HTTP local inference error traceback: {traceback.format_exc()}")

        error_response = {
            "request_id": payload.request_id,
            "error": "An error occurred while processing the chat request.",
        }
        await event_emitter.emit("chat_response_error", data=error_response)
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": error_response
        })
        raise
