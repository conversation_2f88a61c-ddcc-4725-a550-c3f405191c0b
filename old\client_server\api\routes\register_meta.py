from typing import Optional
from pydantic import BaseModel

from . import route

from client_server.core.state_types import BaseURL
from client_server.core.state import (
    G_CLIENT_SERVER_VERSION,
    G_SESSION_ID,
    G_EXTENSION_VERSION,
    G_BASE_URL,
)


class RegisterMetaRequest(BaseModel):
    session_id: str
    extension_version: str
    client_server_version: str
    base_url: Optional[BaseURL] = None


@route("POST", "/register_meta")
async def register_meta(request: RegisterMetaRequest):
    G_SESSION_ID.set(request.session_id)
    G_EXTENSION_VERSION.set(request.extension_version)
    G_CLIENT_SERVER_VERSION.set(request.client_server_version)

    if request.base_url:
        G_BASE_URL.set(request.base_url)
