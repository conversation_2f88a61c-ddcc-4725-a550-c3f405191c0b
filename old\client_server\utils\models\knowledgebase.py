import asyncio
from pathlib import Path
import re
import time
import traceback
from typing import Literal, Optional, Union, Dict, Any, List, Callable, Coroutine
from enum import Enum

import httpx
from pydantic import BaseModel, Field
from qdrant_client.http import models as rest
from qdrant_client.models import PointStruct, Filter, FieldCondition, MatchValue
from client_server.services.db_handler import Collection, Database
from client_server.services.embeddings import IEmbeddingBackend
from client_server.services.qdrant_handler import get_db_client
from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector
from client_server.core.constants import SSL_CERT_FILE
from client_server.core.state import G_BASE_URL

# -----------------------------------------------------------------------------
# Models
# -----------------------------------------------------------------------------


class QdrantKnowledgebaseChunkMetadata(BaseModel):
    id: str
    file: str
    name: str
    content: str
    additional_metadata: Optional[dict[str, Any]] = None


class QdrantKnowledgeBaseChunk(BaseModel):
    metadata: QdrantKnowledgebaseChunkMetadata
    embeddings: list[float]

    async def fill_embeddings(self, backend: IEmbeddingBackend):
        try:
            self.embeddings = await backend.generate(self.metadata.content)
        except Exception as e:
            LOGGER.error(
                f"Error generating embeddings for chunk {self.metadata.id}\n\n{traceback.format_exc()}"
            )
            raise e


class QdrantKnowledgeBaseSearchResult(BaseModel):
    """Result of searching for a file path across knowledge bases."""
    kb_id: str
    kb_name: str
    kb_description: str
    kb_type: str
    file_path: str
    chunks: list[QdrantKnowledgeBaseChunk]
    chunk_ids: list[str]
    total_chunks: int
    total_content_length: int
    search_time_seconds: float
    error: Optional[str] = None

    @property
    def has_chunks(self) -> bool:
        """Check if this result contains any chunks."""
        return self.total_chunks > 0

    @property
    def average_chunk_size(self) -> float:
        """Calculate average chunk size in characters."""
        return self.total_content_length / self.total_chunks if self.total_chunks > 0 else 0.0


class QdrantCrossKBSearchResult(BaseModel):
    """Comprehensive result of searching for a file path across all knowledge bases."""
    file_path: str
    total_kbs_searched: int
    kbs_with_matches: int
    total_chunks_found: int
    total_content_length: int
    search_time_seconds: float
    kb_results: list[QdrantKnowledgeBaseSearchResult]
    errors: list[str] = Field(default_factory=list)

    @property
    def has_matches(self) -> bool:
        """Check if any knowledge base contains the file."""
        return self.kbs_with_matches > 0

    @property
    def matching_kb_names(self) -> list[str]:
        """Get names of knowledge bases that contain the file."""
        return [result.kb_name for result in self.kb_results if result.has_chunks]

    @property
    def average_chunk_size_across_all_kbs(self) -> float:
        """Calculate average chunk size across all matching KBs."""
        return self.total_content_length / self.total_chunks_found if self.total_chunks_found > 0 else 0.0


class QdrantKnowledgebaseSource(str, Enum):
    Local = "LOCAL"
    Remote = "REMOTE"


class QdrantKnowledgebaseType(str, Enum):
    Codebase = "codebase"
    Github = "git"
    Docs = "docs"
    Swagger = "swagger"


class QdrantKnowledgebaseScope(str, Enum):
    Personal = "personal"
    Organization = "organization"


class QdrantKnowledgebaseSyncConfig(str, Enum):
    DoNotSync = "do_not_sync"
    CloudSyncPersonal = "cloud_sync_personal"
    CloudSyncTeam = "cloud_sync_team"


class QdrantKnowledgebaseStatus(str, Enum):
    READY = "ready"
    DRAFT = "draft"
    PROGRESS = "progress"
    ERROR = "error"


class QdrantKnowledgebaseSyncConfig_t(BaseModel):
    enabled: bool
    lastSynced: int


class QdrantProgressInfo(BaseModel):
    message: str
    status: str
    progress: float


class QdrantBaseMetadata(BaseModel):
    pass


class QdrantCodebaseMetadata(QdrantBaseMetadata):
    path: str
    files: List[str]
    file_timestamps: Dict[str, int] = {}  # Unix-format file paths mapped to modification timestamps in milliseconds

    def __init__(self, **data):
        # Normalize path to ensure consistent comparison
        if 'path' in data:
            data['path'] = str(Path(data['path']).resolve())
        # Initialize file_timestamps if not provided
        if 'file_timestamps' not in data:
            data['file_timestamps'] = {}
        super().__init__(**data)

    def update_file_timestamp(self, file_path: str, timestamp: int) -> None:
        """
        Update the timestamp for a specific file.

        Args:
            file_path: The file path in Unix format
            timestamp: The modification timestamp in milliseconds
        """
        from client_server.utils.file_timestamp_manager import normalize_path_format
        normalized_path = normalize_path_format(file_path)
        self.file_timestamps[normalized_path] = timestamp

    def is_file_modified(self, file_path: str, current_timestamp: int) -> bool:
        """
        Check if a file has been modified since last processing.

        Args:
            file_path: The file path to check
            current_timestamp: The current file modification timestamp in milliseconds

        Returns:
            True if the file has been modified or is new, False otherwise
        """
        from client_server.utils.file_timestamp_manager import normalize_path_format
        normalized_path = normalize_path_format(file_path)
        stored_timestamp = self.file_timestamps.get(normalized_path)

        # If no stored timestamp, consider it modified (new file)
        if stored_timestamp is None:
            return True

        # Compare timestamps
        return current_timestamp > stored_timestamp

    def get_file_timestamp(self, file_path: str) -> Optional[int]:
        """
        Retrieve the stored timestamp for a file.

        Args:
            file_path: The file path to look up

        Returns:
            The stored timestamp in milliseconds, or None if not found
        """
        from client_server.utils.file_timestamp_manager import normalize_path_format
        normalized_path = normalize_path_format(file_path)
        return self.file_timestamps.get(normalized_path)


class QdrantGithubMetadata(QdrantBaseMetadata):
    repo_url: str
    branch: str
    accessToken: Optional[str] = None

    def make_repo_url(self) -> str:
        url = self.repo_url
        if url.startswith("https://github.com/"):
            url = url.replace("https://github.com/", "")
        if self.accessToken:
            return f"https://{self.accessToken}@github.com/{url}"
        return f"https://github.com/{url}"

    def make_git_clone_command(self, repo_dir: str) -> list[str]:
        command = ["git", "clone"]
        if self.branch:
            print(f"Cloning branch '{self.branch}'")
            command.extend(["-b", self.branch])
        command.extend([self.make_repo_url(), str(repo_dir)])
        print(" ".join(command))
        return command

    def get_repo_name(self) -> str:
        url = self.repo_url
        if url.startswith("https://github.com/"):
            url = url.replace("https://github.com/", "")
        return url

    def get_repo_name_sanitized(self) -> str:
        return re.sub(r"[^a-zA-Z0-9_]", "_", self.get_repo_name())

    def get_repo_dir(self) -> Path:
        return PathSelector.get_qdrant_db_path() / "git_repos"


class QdrantDocsMetadata(QdrantBaseMetadata):
    urls: List[str]


class QdrantSwaggerEndpoint(BaseModel):
    path: str
    method: str
    operation_id: str
    summary: str = ""
    description: str = ""
    parameters: List[Dict[str, Any]] = Field(default_factory=list)
    responses: Dict[str, Any] = Field(default_factory=dict)
    consumes: List[str] = Field(default_factory=list)
    produces: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)


class QdrantSwaggerMetadata(QdrantBaseMetadata):
    endpoints: List[QdrantSwaggerEndpoint]
    source_type: Literal["file", "content", "url"]
    source_value: str


class QdrantKnowledgeBaseMetadata(BaseModel):
    id: Optional[str] = None
    cloud_id: Optional[str] = None
    name: str
    description: str = ""
    type: QdrantKnowledgebaseType

    source: QdrantKnowledgebaseSource = QdrantKnowledgebaseSource.Local
    scope: QdrantKnowledgebaseScope = QdrantKnowledgebaseScope.Personal
    syncConfig: QdrantKnowledgebaseSyncConfig_t = Field(
        default_factory=lambda: QdrantKnowledgebaseSyncConfig_t(
            enabled=False, lastSynced=0
        )
    )

    isAutoIndexed: bool = False
    status: QdrantKnowledgebaseStatus = QdrantKnowledgebaseStatus.DRAFT
    progress: Optional[QdrantProgressInfo] = None

    # Type-specific metadata
    metadata: Optional[
        Union[
            QdrantCodebaseMetadata,
            QdrantGithubMetadata,
            QdrantDocsMetadata,
            QdrantSwaggerMetadata,
        ]
    ] = None


class QdrantKnowledgeBase(QdrantKnowledgeBaseMetadata):
    @staticmethod
    def _get_kbdb() -> Collection:
        return Database()["knowledge_bases"]

    @staticmethod
    def exists(name: str) -> bool:
        """Check if a knowledge base with the given name exists."""
        return QdrantKnowledgeBase._get_kbdb().find_one({"name": name}) is not None

    @staticmethod
    def exists_id(kbid: str) -> bool:
        """Check if a knowledge base with the given kbid exists"""
        return QdrantKnowledgeBase._get_kbdb().find({"id": kbid}) is not None

    @staticmethod
    def exists_by_path(path: str) -> Optional["QdrantKnowledgeBase"]:
        """Check if a knowledge base with the given path exists and return it."""
        # Normalize path for consistent comparison
        normalized_path = str(Path(path).resolve())

        # Search for any KB type with matching path in metadata
        kb_record = QdrantKnowledgeBase._get_kbdb().find_one({
            "metadata.path": normalized_path
        })

        if kb_record:
            return QdrantKnowledgeBase(**kb_record)
        return None

    @staticmethod
    def exists_auto_indexed_by_path(path: str) -> Optional["QdrantKnowledgeBase"]:
        """Check if an auto-indexed knowledge base with the given path exists."""
        # Normalize path for consistent comparison
        normalized_path = str(Path(path).resolve())

        # Search for auto-indexed KBs with matching path
        kb_record = QdrantKnowledgeBase._get_kbdb().find_one({
            "metadata.path": normalized_path,
            "isAutoIndexed": True
        })

        if kb_record:
            return QdrantKnowledgeBase(**kb_record)
        return None

    @staticmethod
    def validate_path_uniqueness_for_auto_indexing(path: str) -> tuple[bool, str]:
        """
        Validate that a path can be used for auto-indexing.

        Path-based uniqueness constraint:
        - If path already has an uploaded KB (not auto-indexed), prevent auto-indexing
        - If path already has an auto-indexed KB, allow (will be reused)

        Args:
            path: The path to validate

        Returns:
            Tuple of (can_auto_index, reason)
        """
        # Normalize path for consistent comparison
        normalized_path = str(Path(path).resolve())

        # Check if any KB exists for this path
        existing_kb = QdrantKnowledgeBase.exists_by_path(normalized_path)

        if not existing_kb:
            # No KB exists for this path - can auto-index
            return True, "Path is available for auto-indexing"

        if existing_kb.isAutoIndexed:
            # Auto-indexed KB exists - can reuse/update it
            return True, f"Auto-indexed KB already exists for this path (ID: {existing_kb.id})"
        else:
            # Uploaded KB exists - prevent auto-indexing to maintain uniqueness
            return False, f"Path already has an uploaded knowledge base (ID: {existing_kb.id}). Cannot auto-index the same path."

    @staticmethod
    def get(kbid: str) -> "QdrantKnowledgeBase":
        """Get a knowledge base by its ID"""
        kb = QdrantKnowledgeBase._get_kbdb().find_one({"id": kbid})
        if not kb:
            raise ValueError(f"Knowledge base with ID {kbid} not found")
        return QdrantKnowledgeBase(**kb)

    @staticmethod
    def get_all() -> list["QdrantKnowledgeBase"]:
        """Get all knowledge bases from the database"""
        kbdb = QdrantKnowledgeBase._get_kbdb()
        all_kbs = []
        for kb_record in kbdb.find():
            try:
                all_kbs.append(QdrantKnowledgeBase(**kb_record))
            except Exception as e:
                from client_server.core.logger import LOGGER
                LOGGER.warning(f"Error loading KB record {kb_record.get('id', 'unknown')}: {e}")
                continue
        return all_kbs

    def save(self) -> None:
        """Save the current knowledge base instance to the database"""
        if not self.id:
            raise ValueError("Cannot save knowledge base without an ID")

        kbdb = QdrantKnowledgeBase._get_kbdb()

        # Update the existing record
        result = kbdb.update_one(
            {"id": self.id},
            {"$set": self.model_dump()}
        )

        if result["matched_count"] == 0:
            # If no existing record found, insert as new
            kbdb.insert_one(self.model_dump())
            LOGGER.debug(f"Inserted new KB record for ID: {self.id}")
        else:
            LOGGER.debug(f"Updated existing KB record for ID: {self.id}")

    def delete(self) -> None:
        """Delete the knowledge base from both the database and Qdrant collection"""
        if not self.id:
            raise ValueError("Cannot delete knowledge base without an ID")

        # Delete from database
        kbdb = QdrantKnowledgeBase._get_kbdb()
        kbdb.delete_one({"id": self.id})

        # Delete Qdrant collection
        try:
            client = get_db_client()
            client.delete_collection(collection_name=self.id)
            LOGGER.debug(f"Deleted Qdrant collection for KB: {self.id}")
        except Exception as e:
            LOGGER.warning(f"Error deleting Qdrant collection for KB {self.id}: {e}")

        LOGGER.debug(f"Deleted KB record for ID: {self.id}")

    @staticmethod
    def migrate_existing_kbs_for_timestamps() -> int:
        """
        Migrate existing knowledge bases to include file_timestamps field.
        This is a one-time migration function for backward compatibility.

        For legacy KBs, this function will:
        1. Add the file_timestamps field if missing
        2. Populate it with actual file modification timestamps for existing files
        3. Use Unix-format paths for cross-platform compatibility

        Returns:
            Number of knowledge bases that were migrated
        """
        LOGGER.info("Starting migration of existing knowledge bases for timestamp tracking")

        kbdb = QdrantKnowledgeBase._get_kbdb()
        migrated_count = 0

        # Find all codebase type KBs that need timestamp migration
        for kb_record in kbdb.find({"type": "codebase"}):
            try:
                # Check if metadata exists and is a codebase type
                metadata = kb_record.get("metadata", {})
                if not metadata:
                    continue

                # Check if migration is needed:
                # 1. file_timestamps field is missing completely
                # 2. file_timestamps exists but is empty while KB has files
                file_timestamps = metadata.get("file_timestamps", {})
                files_list = metadata.get("files", [])

                needs_migration = (
                    "file_timestamps" not in metadata or  # Missing field
                    (len(files_list) > 0 and len(file_timestamps) == 0)  # Empty timestamps but has files
                )

                if needs_migration:
                    kb_id = kb_record.get("id", "unknown")
                    kb_name = kb_record.get("name", "unknown")

                    LOGGER.info(f"🔄 Migrating KB '{kb_name}' (ID: {kb_id}) - populating file timestamps")
                    LOGGER.debug(f"Found {len(files_list)} files in KB '{kb_name}' to process for timestamps")

                    # Populate file timestamps for existing files
                    populated_timestamps = QdrantKnowledgeBase._populate_file_timestamps_for_migration(
                        files_list, kb_name, kb_id
                    )

                    # Update the record in database with populated timestamps
                    kbdb.update_one(
                        {"id": kb_record["id"]},
                        {"$set": {"metadata.file_timestamps": populated_timestamps}}
                    )

                    migrated_count += 1
                    LOGGER.info(f"✅ Migrated KB '{kb_name}' (ID: {kb_id}) - added {len(populated_timestamps)} file timestamps")

            except Exception as e:
                LOGGER.error(f"Error migrating KB {kb_record.get('id', 'unknown')}: {e}")
                continue

        LOGGER.info(f"Migration completed: {migrated_count} knowledge bases migrated for timestamp tracking")
        return migrated_count

    @staticmethod
    def _populate_file_timestamps_for_migration(files_list: List[str], kb_name: str, kb_id: str) -> Dict[str, int]:
        """
        Populate file timestamps for migration by scanning existing files.

        Args:
            files_list: List of file paths from the KB metadata
            kb_name: Name of the KB (for logging)
            kb_id: ID of the KB (for logging)

        Returns:
            Dictionary mapping Unix-format file paths to modification timestamps in milliseconds
        """
        from client_server.utils.file_timestamp_manager import batch_update_timestamps

        if not files_list:
            LOGGER.debug(f"No files found in KB '{kb_name}' - returning empty timestamps")
            return {}

        LOGGER.debug(f"Populating timestamps for {len(files_list)} files in KB '{kb_name}'")

        try:
            # Use batch processing for efficiency
            file_timestamps = batch_update_timestamps(files_list)

            # Filter out files that couldn't be processed (timestamp = 0)
            valid_timestamps = {
                path: timestamp for path, timestamp in file_timestamps.items()
                if timestamp > 0
            }

            skipped_count = len(files_list) - len(valid_timestamps)
            if skipped_count > 0:
                LOGGER.debug(f"Skipped {skipped_count} files in KB '{kb_name}' (not accessible or not found)")

            LOGGER.debug(f"Successfully populated {len(valid_timestamps)} file timestamps for KB '{kb_name}'")
            return valid_timestamps

        except Exception as e:
            LOGGER.error(f"Error populating file timestamps for KB '{kb_name}' (ID: {kb_id}): {e}")
            # Return empty dict on error to ensure migration doesn't fail completely
            return {}

    @staticmethod
    def migrate_legacy_knowledge_bases():
        """
        Migrate legacy knowledge bases from name-based to path-based storage.

        This function identifies knowledge bases that were created before path-based
        storage was implemented and migrates them to use path-based identifiers.
        """
        from client_server.core.logger import LOGGER

        LOGGER.info("Starting migration of legacy knowledge bases to path-based storage")

        kbdb = QdrantKnowledgeBase._get_kbdb()
        migrated_count = 0
        error_count = 0

        # Find all knowledge bases
        all_kbs = list(kbdb.find())
        LOGGER.info(f"Found {len(all_kbs)} knowledge bases to check for migration")

        for kb_record in all_kbs:
            try:
                # Check if this is a codebase type KB that might need migration
                if kb_record.get("type") == "codebase":
                    metadata = kb_record.get("metadata", {})

                    # Check if metadata has a path field
                    if "path" not in metadata or not metadata["path"]:
                        # This is a legacy KB without proper path metadata
                        # Try to infer path from name or other metadata
                        kb_name = kb_record.get("name", "")

                        # Skip migration if we can't determine a reasonable path
                        if not kb_name:
                            LOGGER.warning(f"Skipping KB {kb_record.get('id', 'unknown')} - no name or path available")
                            continue

                        # For legacy KBs, we'll use the name as a fallback path
                        # This is not ideal but maintains backward compatibility
                        inferred_path = kb_name.replace(" ", "_").replace("/", "_").replace("\\", "_")

                        LOGGER.info(f"Migrating legacy KB '{kb_name}' (ID: {kb_record.get('id')}) - adding inferred path: {inferred_path}")

                        # Update the metadata with the inferred path
                        updated_metadata = metadata.copy()
                        updated_metadata["path"] = inferred_path

                        # Update the KB record in the database
                        kbdb.update_one(
                            {"id": kb_record["id"]},
                            {"$set": {"metadata": updated_metadata}}
                        )

                        migrated_count += 1
                        LOGGER.debug(f"Successfully migrated KB {kb_record['id']} with inferred path: {inferred_path}")

            except Exception as e:
                error_count += 1
                LOGGER.error(f"Error migrating KB {kb_record.get('id', 'unknown')}: {e}")
                continue

        LOGGER.info(f"Migration completed - Migrated: {migrated_count}, Errors: {error_count}, Total checked: {len(all_kbs)}")
        return migrated_count, error_count

    @staticmethod
    def from_chunks(
        metadata: QdrantKnowledgeBaseMetadata,
        chunks: list[QdrantKnowledgeBaseChunk],
    ) -> "QdrantKnowledgeBase":
        # if not processed_data:
        #     raise ValueError("No processed data provided")

        """Save processed chunks to local Qdrant database."""
        vector_size = len(chunks[0].embeddings)
        # print(vector_size)

        client = get_db_client()
        client.create_collection(
            collection_name=metadata.id,
            vectors_config={
                "vectors": rest.VectorParams(
                    size=vector_size, distance=rest.Distance.DOT
                )
            },
        )
        for chunk in chunks:
            client.upsert(
                collection_name=metadata.id,
                points=[
                    PointStruct(
                        id=chunk.metadata.id,
                        vector={"vectors": chunk.embeddings},
                        payload={"metadata": chunk.metadata},
                    ),
                ],
            )

        # Convert metadata to a dictionary
        metadata_dict = metadata.model_dump()

        # Create the QdrantKnowledgeBase instance
        kb = QdrantKnowledgeBase(**metadata_dict)
        kb.status = QdrantKnowledgebaseStatus.READY

        # Insert the knowledge base into the database
        QdrantKnowledgeBase._get_kbdb().insert_one(kb.model_dump())

        return kb

    @staticmethod
    def find_file_across_all_kbs(
        file_path: str,
        sort_by_line: bool = True,
        include_kb_metadata: bool = True,
        max_retries_per_kb: int = 3
    ) -> QdrantCrossKBSearchResult:
        """
        Search for a file path across all knowledge bases in the system.

        Args:
            file_path: The file path to search for in chunk metadata
            sort_by_line: Whether to sort chunks by line number within each KB
            include_kb_metadata: Whether to include detailed KB metadata in results
            max_retries_per_kb: Maximum number of retries for each KB if search fails

        Returns:
            QdrantCrossKBSearchResult containing comprehensive search results

        Raises:
            Exception: If no knowledge bases exist in the system
        """
        import time
        from client_server.core.logger import LOGGER

        search_start_time = time.time()

        # Get all knowledge bases
        try:
            all_kbs = QdrantKnowledgeBase.get_all()
        except Exception as e:
            LOGGER.error(f"Failed to retrieve knowledge bases: {e}")
            raise Exception(f"Could not access knowledge base system: {e}")

        if not all_kbs:
            raise Exception("No knowledge bases found in the system")

        LOGGER.info(f"Starting cross-KB search for file '{file_path}' across {len(all_kbs)} knowledge bases")

        kb_results = []
        total_chunks_found = 0
        total_content_length = 0
        kbs_with_matches = 0
        search_errors = []

        # Search each knowledge base
        for kb_index, kb in enumerate(all_kbs):
            kb_search_start = time.time()
            retries_left = max_retries_per_kb
            kb_error = None
            chunks = []

            LOGGER.debug(f"Searching KB {kb_index + 1}/{len(all_kbs)}: {kb.name} (ID: {kb.id})")

            # Retry logic for each KB
            while retries_left > 0:
                try:
                    chunks = kb.get_chunks_by_file_path(file_path, sort_by_line=sort_by_line)
                    kb_error = None
                    break
                except Exception as e:
                    retries_left -= 1
                    kb_error = str(e)
                    LOGGER.warning(f"Error searching KB {kb.name} (attempt {max_retries_per_kb - retries_left}): {e}")
                    if retries_left > 0:
                        time.sleep(0.1)  # Brief delay before retry

            kb_search_time = time.time() - kb_search_start

            # Calculate KB-specific metrics
            kb_total_content = sum(len(chunk.metadata.content) for chunk in chunks) if chunks else 0
            chunk_ids = [chunk.metadata.id for chunk in chunks] if chunks else []

            # Create KB result
            kb_result = QdrantKnowledgeBaseSearchResult(
                kb_id=kb.id,
                kb_name=kb.name,
                kb_description=kb.description if include_kb_metadata else "",
                kb_type=str(kb.type) if include_kb_metadata else "",
                file_path=file_path,
                chunks=chunks,
                chunk_ids=chunk_ids,
                total_chunks=len(chunks),
                total_content_length=kb_total_content,
                search_time_seconds=kb_search_time,
                error=kb_error
            )

            kb_results.append(kb_result)

            # Update overall statistics
            if chunks:
                kbs_with_matches += 1
                total_chunks_found += len(chunks)
                total_content_length += kb_total_content
                LOGGER.debug(f"Found {len(chunks)} chunks in KB {kb.name}")
            else:
                LOGGER.debug(f"No chunks found in KB {kb.name}")

            # Track errors
            if kb_error:
                error_msg = f"KB '{kb.name}' (ID: {kb.id}): {kb_error}"
                search_errors.append(error_msg)

        total_search_time = time.time() - search_start_time

        # Create comprehensive result
        result = QdrantCrossKBSearchResult(
            file_path=file_path,
            total_kbs_searched=len(all_kbs),
            kbs_with_matches=kbs_with_matches,
            total_chunks_found=total_chunks_found,
            total_content_length=total_content_length,
            search_time_seconds=total_search_time,
            kb_results=kb_results,
            errors=search_errors
        )

        LOGGER.info(
            f"Cross-KB search completed: found file in {kbs_with_matches}/{len(all_kbs)} KBs, "
            f"{total_chunks_found} total chunks, {total_search_time:.3f}s"
        )

        return result

    def update_cloud_id(self, new_id: str):
        """Update the knowledge base ID with cloud ID."""
        self.cloud_id = new_id
        QdrantKnowledgeBase._get_kbdb().update_one(
            {"id": self.id}, {"$set": {"cloud_id": new_id}}
        )

    def upgrade_to_cloud_version(self, cloud_id: str, enable_sync: bool = True):
        """Upgrade an auto-indexed KB to cloud version."""
        self.cloud_id = cloud_id
        self.isAutoIndexed = False  # No longer auto-indexed since it's now cloud-synced
        self.syncConfig.enabled = enable_sync
        self.syncConfig.lastSynced = int(time.time() * 1000)

        # Update in database
        QdrantKnowledgeBase._get_kbdb().update_one(
            {"id": self.id},
            {"$set": {
                "cloud_id": cloud_id,
                "isAutoIndexed": False,
                "syncConfig.enabled": enable_sync,
                "syncConfig.lastSynced": self.syncConfig.lastSynced
            }}
        )

    def get_all_chunks(self) -> list[QdrantKnowledgeBaseChunk]:
        """Retrieve all chunks from the knowledge base's Qdrant collection."""
        from client_server.services.qdrant_handler import get_db_client

        client = get_db_client()
        chunks = []

        # Use scroll to get all points from the collection
        scroll_result = client.scroll(
            collection_name=self.id,
            limit=10000,  # Large limit to get all points
            with_payload=True,
            with_vectors=True
        )

        points = scroll_result[0]  # scroll returns (points, next_page_offset)

        for point in points:
            # Extract metadata and vectors from the point
            metadata_dict = point.payload["metadata"]
            chunk_metadata = QdrantKnowledgebaseChunkMetadata(**metadata_dict)

            # Get the vector data
            vectors = point.vector["vectors"] if isinstance(point.vector, dict) else point.vector

            chunk = QdrantKnowledgeBaseChunk(
                metadata=chunk_metadata,
                embeddings=vectors
            )
            chunks.append(chunk)

        return chunks

    def get_chunks_by_file_path(self, file_path: str, sort_by_line: bool = True) -> list[QdrantKnowledgeBaseChunk]:
        """
        Retrieve all chunks associated with a specific file path from the knowledge base's Qdrant collection.

        Args:
            file_path: The file path to search for in chunk metadata
            sort_by_line: Whether to sort chunks by line number (if line_start exists in additional_metadata)

        Returns:
            List of QdrantKnowledgeBaseChunk objects for the specified file path
        """
        from client_server.services.qdrant_handler import get_db_client

        client = get_db_client()
        chunks = []

        # Create filter to match the specific file path
        file_filter = Filter(
            must=[
                FieldCondition(
                    key="metadata.file",
                    match=MatchValue(value=file_path)
                )
            ]
        )

        # Use scroll to get all points matching the file path filter
        scroll_result = client.scroll(
            collection_name=self.id,
            scroll_filter=file_filter,
            limit=10000,  # Large limit to get all matching points
            with_payload=True,
            with_vectors=True
        )

        points = scroll_result[0]  # scroll returns (points, next_page_offset)

        for point in points:
            # Extract metadata and vectors from the point
            metadata_dict = point.payload["metadata"]
            chunk_metadata = QdrantKnowledgebaseChunkMetadata(**metadata_dict)

            # Get the vector data
            vectors = point.vector["vectors"] if isinstance(point.vector, dict) else point.vector

            chunk = QdrantKnowledgeBaseChunk(
                metadata=chunk_metadata,
                embeddings=vectors
            )
            chunks.append(chunk)

        # Sort by line number if requested and line_start exists
        if sort_by_line:
            def get_line_start(chunk: QdrantKnowledgeBaseChunk) -> int:
                if (chunk.metadata.additional_metadata and
                    isinstance(chunk.metadata.additional_metadata, dict) and
                    'line_start' in chunk.metadata.additional_metadata):
                    try:
                        return int(chunk.metadata.additional_metadata['line_start'])
                    except (ValueError, TypeError):
                        return float('inf')  # Put chunks without valid line numbers at the end
                return float('inf')  # Put chunks without line_start at the end

            chunks.sort(key=get_line_start)

        return chunks

    async def sync_to_cloud(
        self,
        session: str,
        processed_data: list[QdrantKnowledgeBaseChunk],
        progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
        retries: int = 10,
    ):
        """Sync the knowledge base to cloud and return cloud ID."""

        upload_bin = {
            "name": self.name,
            "type": self.type,
            "sync": self.syncConfig.enabled,
            "shared": self.scope == QdrantKnowledgebaseScope.Organization,
            "processed_chunk": [
                {
                    "vectors": chunk.embeddings,
                    "metadata": {
                        "id": chunk.metadata.id,
                        "content": chunk.metadata.content,
                        "file": chunk.metadata.file,
                        "name": chunk.metadata.name,
                        "additional_metadata": chunk.metadata.additional_metadata,
                    },
                }
                for chunk in processed_data
            ],
        }

        LOGGER.info(f"Uploading to cloud: {upload_bin}")

        error = None
        while retries > 0:
            try:
                # Initialize progress
                await progress_fn(0)

                LOGGER.info("Sending to cloud")

                await asyncio.sleep(1)

                base_url = G_BASE_URL.get().codebase

                async with httpx.AsyncClient(
                    verify=SSL_CERT_FILE,
                    timeout=300000.0,
                ) as client:
                    req = await client.post(
                        f"{base_url}/upload/kb",
                        json=upload_bin,
                        headers={
                            "x-session": session,
                            "Content-Type": "application/json",
                        },
                    )
                await asyncio.sleep(1)

                LOGGER.info(f"Response: {req.status_code}")
                LOGGER.info(f"Response: {req.json()}")

                res = req.json()
                LOGGER.info(f"Status Code: {req.status_code}")
                if req.status_code != 200:
                    raise RuntimeError(res["error"])

                LOGGER.info(f"RES: {res}")

                # Update progress to complete
                await asyncio.sleep(1)
                self.progress = QdrantProgressInfo(
                    message="Upload complete", status="complete", progress=1.0
                )
                await progress_fn(95.0)

                LOGGER.info("Upload complete")

                # Update the local knowledge base ID with cloud ID
                new_id = res["cloud_index"]
                self.update_cloud_id(new_id)

                return
            except (httpx.ConnectError, httpx.ReadError, ConnectionResetError) as e:
                print(f"Connection error: {e}")
                LOGGER.error(f"Connection error: {e}")
                error = e
                retries -= 1
                if retries > 0:
                    wait_time = 2 ** (10 - retries)  # Exponential backoff
                    print(
                        f"Retrying in {wait_time} seconds... ({retries} retries left)"
                    )
                    time.sleep(wait_time)

                # Update progress to error state
                self.progress = QdrantProgressInfo(
                    message=f"Connection error: {str(e)}", status="error", progress=0.0
                )
                await progress_fn(0.0)
            except Exception as e:
                print(e)
                LOGGER.error(e)
                error = e
                retries -= 1
                time.sleep(1)

                # Update progress to error state
                self.progress = QdrantProgressInfo(
                    message=f"Upload failed: {str(e)}", status="error", progress=0.0
                )
                await progress_fn(0.0)

            await asyncio.sleep(1)
        if error:
            raise error

    LOGGER.info("Sync to cloud complete (Exit)")
