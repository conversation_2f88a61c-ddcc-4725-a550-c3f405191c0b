# Swagger Chunker (`swagger.py`)

The `swagger.py` module provides the `SwaggerChunker`, a highly specialized `IChunker` implementation for processing API definitions from a Swagger or OpenAPI specification.

Unlike other chunkers that split large text files, the `SwaggerChunker` operates on a structured list of API endpoints. Its primary strategy is to use a language model to generate a high-quality, natural language summary for each endpoint. This summary then serves as the content of the chunk to be embedded.

## Classes

### `SwaggerChunker(IChunker)`

Manages the process of converting a list of API endpoints into a set of summarized chunks.

#### `__init__(self, metadata: QdrantSwaggerMetadata)`

- **Parameters:**
  - `metadata` (`QdrantSwaggerMetadata`): An object containing the list of `QdrantSwaggerEndpoint` objects to be processed (`metadata.endpoints`).

#### `_make_chunks(self, progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

This method orchestrates the chunking process for the endpoints.

- **Parallel Processing:** It uses a `ThreadPoolExecutor` to process multiple endpoints concurrently, sending parallel requests to the summary generation API.
- **Summary Generation:** For each endpoint, it calls the `_get_summary` method.
- **Chunk Creation:** Once a summary is received, it creates a `QdrantKnowledgeBaseChunk`.
  - The `content` of the chunk is the AI-generated summary.
  - The full, original endpoint data is saved to a separate JSON file for reference.
  - The original endpoint data is also stored in the `additional_metadata` field of the chunk.
- **Progress Reporting:** It reports progress after each endpoint is processed.

#### `_get_summary(self, endpoint: QdrantSwaggerEndpoint) -> str`

This helper method is responsible for generating the summary for a single endpoint.

- It constructs a prompt that asks a language model to create a descriptive summary for the provided endpoint data.
- It makes a POST request to the `/swagger/summary` API endpoint.
- It includes retry logic to handle potential network errors or API failures.
- **Returns:**
  - `str`: The natural language summary of the endpoint.
