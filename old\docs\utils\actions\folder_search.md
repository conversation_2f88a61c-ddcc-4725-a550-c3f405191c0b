# Folder Search (`folder_search.py`)

This module implements the `folder_search` action, which allows for a semantic search to be performed within a specific folder of a knowledge base. It is a specialized version of the general context search, adding a path-based filter to the query.

## Functions

### `process_folder_search(*, query: str, tool_id: str, folder_path: str, index_name: str, search_references: SearchReferences)`

This is the main function for handling the folder search action.

- **Parameters:**
  - `query` (str): The search query.
  - `tool_id` (str): The unique identifier for this tool call.
  - `folder_path` (str): The path to the folder to search within.
  - `index_name` (str): The name of the Qdrant collection (knowledge base ID) to search.
  - `search_references` (SearchReferences): An object to track the search results.
- **Returns:**
  - A tuple containing:
    - `list[dict]`: A list of formatted messages for the language model.
    - `SearchReferences`: The updated search references object.

This function calls `_perform_folder_search` to get the results, adds them to `search_references`, and then formats them for the language model using `create_action_messages`.

### `_perform_folder_search(query: str, index_name: str, folder_path: str, limit: int = 10, is_local: bool = False)`

This function executes the search on the local Qdrant database with a folder filter.

- It generates embeddings for the query.
- It constructs a `Filter` object for Qdrant, specifying a `must` condition where the `metadata.file` field must match the provided `folder_path`.
- It executes the search with this filter.
- It formats the raw Qdrant results into a standardized dictionary structure.
