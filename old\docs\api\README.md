# API Documentation

The `api` module is the entry point for all communication with the client server, whether it's through RESTful APIs or WebSockets. It is divided into two main components:

- **Routes**: These are standard RESTful API endpoints used for synchronous requests like health checks, fetching data, or triggering specific actions. They are defined under `client_server/api/routes`.
- **Events**: These are WebSocket event handlers for real-time, bidirectional communication with the client. They are used for features like live chat, progress updates, and notifications. They are defined under `client_server/api/events`.

This documentation provides details on each route and event, including their purpose, parameters, and data structures.
