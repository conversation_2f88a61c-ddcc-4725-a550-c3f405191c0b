import re
import json
import unicodedata
from typing import Coroutine

import numpy as np
from onnxruntime import InferenceSession
from overrides import override

from . import IEmbeddingBackend


class _InferXSimpleTokenizer:
    def __init__(
        self,
        tokenizer_file: str,
        config_file: str = None,
        max_length: int = None,
        max_length_pad: bool = False,
        return_np_tensors: bool = False,
    ):
        with open(tokenizer_file, "r", encoding="utf-8") as f:
            tok = json.load(f)
        model = tok.get("model", {})
        self.vocab = model.get("vocab", {})
        self.unk_token = model.get("unk_token", "[UNK]")
        self.unk_id = self.vocab.get(self.unk_token)
        self.id_to_token = {idx: tok for tok, idx in self.vocab.items()}

        self.do_lower_case = False
        self.strip_accents = False
        self.max_length = max_length
        self.max_length_pad = max_length_pad
        self.return_np_tensors = return_np_tensors
        self.pad_id = None
        self.pad_token = None

        self.cls_token = "[CLS]"
        self.sep_token = "[SEP]"
        self.cls_id = self.vocab.get(self.cls_token)
        self.sep_id = self.vocab.get(self.sep_token)

        if config_file:
            with open(config_file, "r", encoding="utf-8") as f:
                cfg = json.load(f)
            norm = cfg.get("normalizer", {})
            self.do_lower_case = norm.get("lowercase", False)
            self.strip_accents = norm.get("strip_accents", False)
            trunc = cfg.get("truncation", {})
            if self.max_length is None:
                self.max_length = trunc.get("max_length", None)
            pad = cfg.get("padding", {})
            strategy = pad.get("strategy", {})
            fixed_len = strategy.get("Fixed", None)
            if fixed_len:
                self.pad_length = fixed_len
                self.pad_id = pad.get("pad_id", None)
                self.pad_token = pad.get("pad_token", None)
            else:
                self.pad_length = None

        if self.max_length_pad and self.max_length:
            self.pad_length = self.max_length
            if self.pad_token is None:
                self.pad_token = "[PAD]"
            if self.pad_id is None:
                self.pad_id = self.vocab.get(self.pad_token)

    def _normalize(self, text: str) -> str:
        text = "".join(c for c in text if unicodedata.category(c)[0] != "C")
        if self.strip_accents:
            text = unicodedata.normalize("NFD", text)
            text = "".join(c for c in text if unicodedata.category(c) != "Mn")
        if self.do_lower_case:
            text = text.lower()
        return text

    def _pre_tokenize(self, text: str) -> list:
        tokens = re.findall(r"\w+|[^\w\s]", text)
        return tokens

    def _wordpiece(self, token: str) -> list:
        if token in self.vocab:
            return [token]
        chars = list(token)
        sub_tokens = []
        start = 0
        while start < len(chars):
            end = len(chars)
            cur_substr = None
            while start < end:
                substr = "".join(chars[start:end])
                if start > 0:
                    substr = "##" + substr
                if substr in self.vocab:
                    cur_substr = substr
                    break
                end -= 1
            if cur_substr is None:
                sub_tokens.append(self.unk_token)
                break
            sub_tokens.append(cur_substr)
            start = end
        return sub_tokens

    def encode(self, text):
        is_single = False
        if isinstance(text, str):
            text = [text]

        if isinstance(text, list):
            texts = [t.lower() for t in text]
        else:
            raise TypeError("Input must be a string or a list of strings, bro.")

        all_tokens = []
        all_ids = []
        all_attention_mask = []

        for t in texts:
            t_ = self._normalize(t)
            words = self._pre_tokenize(t_)

            tokens = []
            for w in words:
                tokens.extend(self._wordpiece(w))

            if self.cls_token in self.vocab:
                tokens = [self.cls_token] + tokens
            if self.sep_token in self.vocab:
                tokens = tokens + [self.sep_token]

            ids = [self.vocab.get(tok, self.unk_id) for tok in tokens]
            attention_mask = [1] * len(ids)

            if self.max_length and len(ids) > self.max_length:
                ids = ids[: self.max_length]
                tokens = tokens[: self.max_length]
                attention_mask = attention_mask[: self.max_length]

            if getattr(self, "pad_length", None):
                pad_len = self.pad_length - len(ids)
                if pad_len > 0 and self.pad_id is not None:
                    ids = ids + [self.pad_id] * pad_len
                    tokens = tokens + [self.pad_token] * pad_len
                    attention_mask = attention_mask + [0] * pad_len

            all_tokens.append(tokens)
            all_ids.append(ids)
            all_attention_mask.append(attention_mask)

        if is_single:
            if self.return_np_tensors:
                return {
                    "tokens": all_tokens[0],
                    "input_ids": np.array(all_ids[0], dtype=np.int32),
                    "attention_mask": np.array(all_attention_mask[0], dtype=np.int32),
                }
            else:
                return {
                    "tokens": all_tokens[0],
                    "ids": all_ids[0],
                    "attention_mask": all_attention_mask[0],
                }
        else:
            if self.return_np_tensors:
                return {
                    "tokens": all_tokens,
                    "ids": np.array(all_ids, dtype=np.int32),  # shape (batch, seq)
                    "attention_mask": np.array(all_attention_mask, dtype=np.int32),
                }
            else:
                return {
                    "tokens": all_tokens,
                    "ids": all_ids,
                    "attention_mask": all_attention_mask,
                }

    def decode(self, ids):
        if isinstance(ids, np.ndarray):
            ids = ids.tolist()
        if isinstance(ids[0], (int, np.integer)):
            return self._decode_single(ids)
        elif isinstance(ids[0], (list, np.ndarray)):
            return [self._decode_single(seq) for seq in ids]
        else:
            raise TypeError("IDs must be a list or list of lists.")

    def _decode_single(self, ids):
        tokens = [self.id_to_token.get(i, self.unk_token) for i in ids]
        words = []
        for tok in tokens:
            if tok in (self.cls_token, self.sep_token, self.pad_token):
                continue
            if tok.startswith("##") and words:
                words[-1] += tok[2:]
            else:
                words.append(tok)
        return " ".join(words)


class InferXEmbeddingsBackend(IEmbeddingBackend):
    def __init__(
        self,
        inference_model: str = "models/embeddings/qnn/model.onnx",
        tokenizer_model: str = "models/embeddings/qnn/tokenizer.json",
        tokenizer_config: str = "models/embeddings/qnn/tokenizer_config.json",
    ) -> None:
        self._tokenizer = _InferXSimpleTokenizer(
            tokenizer_file=tokenizer_model,
            config_file=tokenizer_config,
            max_length=512,
            max_length_pad=True,
            return_np_tensors=True,
        )
        self._inference = InferenceSession(
            inference_model,
            providers=["QNNExecutionProvider"],
            provider_options=[
                {
                    "backend_path": "QnnHtp.dll",
                    "htp_performance_mode": "burst",
                    "high_power_saver": "sustained_high_performance",
                    "enable_htp_fp16_precision": "1",
                    "htp_graph_finalization_optimization": "3",
                }
            ],
        )

    @override
    async def generate(self, content: str) -> Coroutine[list[float], None, None]:
        return (await self.generate_batch([content]))[0]

    @override
    async def generate_batch(
        self, content: list[str]
    ) -> Coroutine[list[list[float]], None, None]:
        encoded_inputs = self._tokenizer(
            content,
            return_tensors="np",
            truncation=True,
            padding="max_length",  # ensures a fixed length
            max_length=512,
        )
        input_ids = encoded_inputs["input_ids"]
        attention_mask = encoded_inputs["attention_mask"]

        processed_content = {
            "input_ids": input_ids.astype(np.int32),
            "attention_mask": attention_mask.astype(np.int32),
        }

        output = self._inference.run(None, processed_content)
        # output[0] has shape: (1, seq_length, hidden_dim)
        token_embeddings = output[0]

        # Mean pooling: average only the embeddings corresponding to non-padding tokens
        sentence_embedding = (token_embeddings * attention_mask[..., None]).sum(
            1
        ) / attention_mask.sum(1)[..., None]

        print(sentence_embedding.tolist())
        return sentence_embedding.tolist()[0]
