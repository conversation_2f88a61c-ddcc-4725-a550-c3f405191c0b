import os
from qdrant_client import QdrantClient
from typing import Optional
from client_server.utils.path_selector import PathSelector

# -----------------------------------------------------------------------------
# Initialize Qdrant client
# -----------------------------------------------------------------------------

_qc: Optional[QdrantClient] = None


# -----------------------------------------------------------------------------
# Helper functions
# -----------------------------------------------------------------------------


def get_db_client() -> QdrantClient:
    """Get the Qdrant client"""
    global _qc
    if _qc is None:
        _qc = QdrantClient(path=PathSelector.get_qdrant_db_path())
    return _qc


# -----------------------------------------------------------------------------
