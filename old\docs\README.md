# Client Server

## Project Overview

The Client Server is a background service that acts as the backend for a VSCode extension. It provides both REST and WebSocket endpoints to handle various asynchronous tasks, facilitating seamless communication between the extension and the server's capabilities.

The server is built with Python using FastAPI, leveraging modern asynchronous programming practices to ensure high performance and responsiveness.

## Key Features

- **Hybrid Communication:** Exposes both RESTful APIs and WebSocket event channels for flexible communication.
- **Service-Oriented Architecture:** Built with a modular, service-oriented design where each service has a clear interface and can have multiple implementations.
- **Asynchronous Processing:** Leverages `asyncio`, `ThreadPoolExecutor`, and `ProcessPoolExecutor` to efficiently handle I/O-bound and CPU-bound tasks concurrently.
- **Code Intelligence:** Includes specialized utilities for code processing, such as language-aware chunkers and tool call handlers for chat-based interactions.
- **Knowledge Base Integration:** Supports knowledge base management, using Qdrant for local embedding storage and providing connectivity to cloud-based knowledge sources.
- **Robust Error Handling:** Integrates with a cloud service for logging error events, ensuring issues are tracked and addressed.
- **Global State Management:** A thread-safe global state manager handles shared application data like session IDs and client versions.
- **Context-Aware AI:** Implements precautions to manage context size before sending data to language models, preventing overflow issues.

## Architecture and Design

### Design Principles

The project adheres to fundamental software design principles, including:

- **SOLID:** Ensuring a scalable and maintainable object-oriented design.
- **DRY (Don't Repeat Yourself):** Minimizing code duplication.
- **KISS (Keep It Simple, Stupid):** Favoring simplicity in design and implementation.

### Service-Oriented Architecture

The codebase is organized into independent services located in the `client_server/services` directory. Each service follows a strict architectural pattern:

- **Interfaces:** The interface for a service is defined in the `__init__.py` file within its respective folder (e.g., `ILLM`). This promotes dependency inversion.
- **Implementations:** Specific implementations of an interface (e.g., `OpenAILLMBackend`) are contained in separate files within the service's folder.
- **Factories:** Each service includes a `utils.py` file containing a builder or factory function. This allows for the dynamic creation of service implementations based on configuration or context.

### Concurrency Model

The server uses a combination of `ProcessPoolExecutor` and `ThreadPoolExecutor` to balance CPU-bound and I/O-bound tasks. This hybrid approach optimizes performance by delegating tasks to the appropriate execution model, ensuring the server remains responsive.

## Project Structure

The project is organized into the following key directories:

- `client_server/api/`: Contains handlers for both REST routes (`routes`) and WebSocket events (`events`).
- `client_server/cmd/`: Entry points for the application, such as scripts to run the server and build the application.
- `client_server/core/`: Core, side-effect-free utilities used throughout the codebase, such as logging and global state management.
- `client_server/services/`: Independent business logic modules, each with its own interface and implementation(s).
- `client_server/swagger/`: A niche utility for Swagger/OpenAPI-related tasks.
- `client_server/utils/`: Common utilities shared across the application, including:
  - `actions/`: Handlers for tool calls during chat events.
  - `chunkers/`: Code-specific logic for parsing and chunking different types of source code.
  - `models/`: Data models, such as the `KnowledgeBase` model for data transmission.

## Tooling and Build Process

- **Dependency Management:** `Poetry` is used for managing project dependencies.
- **Development Workflow:** A `Makefile` provides common commands for tasks like running tests, linting, and building the project.
- **Distribution:** `PyInstaller` is used to package the application into a single executable for distribution.
- **Code Signing:** Code signing is integrated into the build script to ensure the integrity of the distributed application.
