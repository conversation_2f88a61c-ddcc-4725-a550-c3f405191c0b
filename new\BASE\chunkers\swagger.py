import os
import time
import json
from uuid import uuid4
import httpx
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Callable, Coroutine
import constants

from . import create_chunk, create_chunk_metadata


def get_endpoint_summary_simple(endpoint_dict: dict) -> str:
    """Simple endpoint summary generation function."""
    start_time = time.time()
    endpoint_content = json.dumps(endpoint_dict)
    print(f"Generating summary for endpoint: {endpoint_content[:100]}...")
    print(f"Endpoint full length: {len(endpoint_content)} characters")

    content = (
        "Your task is to generate a summary and possible use for the provided endpoint.\nBe descriptive, explain thoroughly, mention all aspects.\n\nSTRICT INSTRUCTION: THe SUMMARY SHOULD NOT BE MORE THAN 5 LINES LONG."
        + endpoint_content
        + "\n\n\nSTRICT INSTRUCTION:\nWrap the summary in <summary> and </summary> tags. Nothing related to the endpoint shall be outside the tags."
    )

    print(f"Generated content length: {len(content)} characters")

    retries = 10

    while retries > 0:
        # Make API call and return data.
        try:
            base_url = constants.general
            print(f"Making API call to {base_url}/swagger/summary")
            api_start_time = time.time()

            with httpx.Client(verify=constants.SSL_CONTEXT) as client:
                response = client.post(
                    f"{base_url}/swagger/summary",
                    json={"content": content},
                    timeout=30,  # Add timeout for better error handling
                )

                api_time = time.time() - api_start_time
                print(f"API call completed in {api_time:.2f} seconds")

                if response.status_code != 200:
                    print(f"API call failed with status {response.status_code}: {response.text[:500]}...")
                    raise ValueError(f"API call failed with status {response.status_code}")

                response_length = len(response.text)
                print(f"API call successful, summary generated (length: {response_length} chars)")
                execution_time = time.time() - start_time
                print(f"get_summary execution time: {execution_time:.2f}s")
                return response.text.strip('"')
        except Exception as e:
            print(f"Error generating endpoint summary: {e}")

        time.sleep(1)
        retries -= 1
    return ""


async def process_swagger_chunks(
    endpoints: list[dict],
    base_path: str,
    progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
) -> list[dict]:

    """Process Swagger endpoints and return chunks as simple dictionaries."""
    print("Processing Swagger type knowledge base")
    endpoint_count = len(endpoints)
    print(f"Processing {endpoint_count} endpoints")

    # Log endpoint statistics
    methods = {}
    for endpoint in endpoints:
        method = endpoint.get("method", "unknown")
        methods[method] = methods.get(method, 0) + 1
    print(f"Endpoint methods distribution: {methods}")

    MAX_WORKERS = max(1, 10)
    print(f"Will use {MAX_WORKERS} threads for endpoint processing")

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        print(f"Created thread pool with {MAX_WORKERS} workers")

        endpoint_processing_start = time.time()
        futures = [
            executor.submit(get_endpoint_summary_simple, endpoint=endpoint)
            for endpoint in endpoints
        ]
        print(f"Submitted {len(futures)} endpoint summary tasks")

        endpoint_summaries = map(lambda x: x.result(), as_completed(futures))
        processed_endpoints = 0

        chunks = []
        for i, (endpoint, summary) in enumerate(zip(endpoints, endpoint_summaries)):
            print(f"Processing endpoint {i+1}/{endpoint_count}: {endpoint.get('path', 'unknown')}")

            endpoints_dir = os.path.join(base_path, "swagger_endpoints", str(uuid4()))
            os.makedirs(endpoints_dir, exist_ok=True)

            endpoint_file = os.path.join(endpoints_dir, f"{str(uuid4())}.json")
            endpoint_data = {
                "path": endpoint.get("path", ""),
                "summary": summary,
                "endpoint": endpoint,
            }

            with open(endpoint_file, "w+") as f:
                f.write(json.dumps(endpoint_data, indent=2))

            try:
                file_size = os.path.getsize(endpoint_file)
                print(f"Saved endpoint data to: {endpoint_file} (size: {file_size} bytes)")
            except OSError:
                print(f"Saved endpoint data to: {endpoint_file}")

            metadata = create_chunk_metadata(
                chunk_id=str(uuid4()),
                file_path=endpoint_file,
                name=endpoint.get("path", ""),
                content=summary,
                additional_metadata=endpoint
            )
            chunk = create_chunk(metadata=metadata, embeddings=[])
            chunks.append(chunk)

            processed_endpoints += 1
            progress_pct = (processed_endpoints / endpoint_count) * 100
            print(f"Progress: {progress_pct:.2f}%")
            if progress_callback:
                await progress_callback(progress_pct)

        endpoint_processing_time = time.time() - endpoint_processing_start
        print(f"Endpoint processing completed in {endpoint_processing_time:.2f} seconds (total chunks: {len(chunks)})")

        return chunks
