"""
Tool calling module for LLM integration.

This module contains all tool calling functionality including:
- Tool call handler for processing LLM tool calls
- Tool schemas and definitions
- Integration with existing action handlers
"""

# Import all components to maintain backward compatibility
from .handler import ToolCallHandler
from .schemas import get_tool_schemas, get_all_tool_schemas

__all__ = [
    "ToolCallHandler",
    "get_tool_schemas",
    "get_all_tool_schemas",
]
