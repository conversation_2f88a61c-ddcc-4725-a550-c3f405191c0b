# Application Constants

## Server and Model Configuration

- `PORT: int`: The port on which the client server listens.
- `PRELOAD_EMBEDDING_MODEL: bool`: A feature flag to determine whether the embedding model should be preloaded on startup.
- `PRELOAD_CHAT_MODEL: bool`: A feature flag to determine whether the chat model should be preloaded on startup.
- `PRELOAD_INLINE_SUGGESTION_MODEL`: A feature flag to determine whether the inline suggestion model should be preloaded on startup.

## SSL Configuration

- `SSL_CERT_FILE: str`: The path to the SSL certificate file, using `certifi.where()` to locate the appropriate certificate bundle.
- `SSL_CONTEXT: ssl.SSLContext`: An `SSLContext` object created with the specified certificate file, used for establishing secure connections.

## Socket.IO Configuration

These settings are tuned for resilience, especially to handle system sleep/wake cycles without losing connection.

- `SOCKETIO_PING_INTERVAL: int`: The interval (in seconds) at which ping packets are sent to the client to keep the connection alive.
- `SOCKETIO_PING_TIMEOUT: int`: The time (in seconds) the server will wait for a ping response before considering the connection timed out.
- `SOCKETIO_MAX_HTTP_BUFFER_SIZE: int`: The maximum size (in bytes) for Socket.IO messages, set to 100 MB.
- `SOCKETIO_SESSION_CLEANUP_INTERVAL: int`: The interval (in seconds) at which the server runs a cleanup process for old Socket.IO sessions.
- `SOCKETIO_SESSION_TIMEOUT: int`: The time (in seconds) after which an inactive session is considered old and will be cleaned up.
