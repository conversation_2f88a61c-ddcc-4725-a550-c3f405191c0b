# Action Utilities (`utils.py`)

This module provides common utilities that are shared across all the different action handlers in the `actions` sub-package.

## Functions

### `create_action_messages(tool_id: str, action_name: str, action_arguments: dict[str, str], search_results: list[dict[str, Any]])`

This function creates the standard message structure that is sent back to the language model after an action has been executed. It ensures that the model receives the information in a consistent format.

- **Parameters:**
  - `tool_id` (str): The unique ID of the tool call that triggered the action.
  - `action_name` (str): The name of the action (e.g., "context_search").
  - `action_arguments` (dict): The arguments that were passed to the action.
  - `search_results` (list): The list of result dictionaries from the search.
- **Returns:**
  - `list[dict]`: A list containing two dictionaries:
    1.  An `assistant` role message containing the action details.
    2.  An `action` role message containing the concatenated text content of the search results, linked by the `action_id`.

## Classes

### `SearchReferences`

This class is used to accumulate references to all the information sources that are retrieved during the various search actions within a single request. This allows the application to keep track of what context was used.

#### `__init__(self, request_id: str)`

Initializes the object with a unique request ID.

#### `add_search_result(self, path: str, type: str, name: str, content: str)`

Adds a new search result to the internal list.

- **Parameters:**
  - `path` (str): The path to the resource (e.g., file path or URL).
  - `type` (str): The type of resource (e.g., "file", "web").
  - `name` (str): The display name of the resource (e.g., filename).
  - `content` (str): The actual content of the search result.

#### `get_search_result(self)`

- **Returns:**
  - `dict`: A deep copy of the collected search results.
