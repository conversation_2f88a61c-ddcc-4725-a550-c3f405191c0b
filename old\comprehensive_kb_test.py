#!/usr/bin/env python3
"""
Knowledge Base Listing Test Script

This script focuses on listing and displaying knowledge bases with comprehensive
diagnostic capabilities including sync status analysis and path uniqueness validation.
"""

import requests
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict
import sys

# Configuration
SESSION_ID = "0968c5ef-37ff-429c-b975-dbe4b75f9f76"
BASE_URL = "http://localhost:45213"
API_ENDPOINT = f"{BASE_URL}/list_kbs"


def format_timestamp(timestamp: int) -> str:
    """Convert timestamp to readable format in Indian Standard Time (IST)."""
    if not timestamp or timestamp <= 0:
        return "Never"
    
    try:
        # Handle both seconds and milliseconds timestamps
        if timestamp > 1e12:  # Milliseconds
            timestamp = timestamp / 1000
            
        # Create UTC datetime
        from datetime import timezone, timedelta
        utc_dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        
        # Convert to IST (UTC+5:30)
        ist_offset = timedelta(hours=5, minutes=30)
        ist_tz = timezone(ist_offset, name="IST")
        ist_dt = utc_dt.astimezone(ist_tz)
        
        # Format with IST indicator
        return ist_dt.strftime("%Y-%m-%d %H:%M:%S IST")
    except (ValueError, OSError):
        return f"Invalid timestamp: {timestamp}"


def get_sync_status_icon(sync_status: str) -> str:
    """Get appropriate icon for sync status."""
    icons = {
        'upload_needed': '⬆️',
        'sync_available': '🔄', 
        'synced': '✅',
        'unknown': '❓'
    }
    return icons.get(sync_status, '❓')


def get_source_icon(source: str) -> str:
    """Get appropriate icon for KB source."""
    icons = {
        'LOCAL': '🏠',
        'REMOTE': '☁️'
    }
    return icons.get(source.upper(), '📁')


def determine_sync_status(kb: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine sync status based on simplified cloud sync workflow.
    This replicates the backend logic for demonstration.
    """
    cloud_id = kb.get('cloud_id')
    source = kb.get('source', '').upper()
    
    sync_info = {
        'sync_status': 'unknown',
        'cloud_sync_available': False,
        'can_upload': False,
        'can_sync': False,
        'status_reason': ''
    }
    
    if source == 'REMOTE':
        sync_info.update({
            'sync_status': 'synced',
            'cloud_sync_available': True,
            'can_upload': False,
            'can_sync': False,
            'status_reason': 'Cloud knowledge base - always synced'
        })
    elif source == 'LOCAL':
        if not cloud_id:
            sync_info.update({
                'sync_status': 'upload_needed',
                'cloud_sync_available': True,
                'can_upload': True,
                'can_sync': False,
                'status_reason': 'Local KB not yet uploaded to cloud'
            })
        else:
            sync_info.update({
                'sync_status': 'sync_available',
                'cloud_sync_available': True,
                'can_upload': False,
                'can_sync': True,
                'status_reason': 'KB can be synced with cloud'
            })
    
    return sync_info


def print_kb_details(kb: Dict[str, Any], index: int, section_type: str) -> None:
    """Print detailed information about a knowledge base."""
    # Determine sync status
    sync_info = determine_sync_status(kb)
    sync_icon = get_sync_status_icon(sync_info['sync_status'])
    source_icon = get_source_icon(kb.get('source', ''))
    
    print(f"  [{index}] {kb.get('name', 'Unknown Name')} {sync_icon}")
    print(f"      📋 Basic Info:")
    print(f"         ID: {kb.get('id', 'N/A')}")
    print(f"         Source: {source_icon} {kb.get('source', 'Unknown')} ({section_type})")
    print(f"         Scope: {kb.get('scope', 'Unknown')}")
    print(f"         Type: {kb.get('type', 'Unknown')}")
    print(f"         Status: {kb.get('status', 'Unknown')}")
    
    print(f"      🔄 Cloud Sync Status:")
    print(f"         Sync Status: {sync_info['sync_status']} {sync_icon}")
    print(f"         Can Upload: {'✅' if sync_info['can_upload'] else '❌'}")
    print(f"         Can Sync: {'✅' if sync_info['can_sync'] else '❌'}")
    print(f"         Cloud Sync Available: {'✅' if sync_info['cloud_sync_available'] else '❌'}")
    print(f"         Reason: {sync_info['status_reason']}")
    
    print(f"      ☁️  Cloud Information:")
    cloud_id = kb.get('cloud_id')
    print(f"         Cloud ID: {cloud_id if cloud_id else 'None'}")
    
    print(f"      📁 Path & Indexing:")
    print(f"         Auto-Indexed: {'✅' if kb.get('isAutoIndexed') else '❌'}")
    
    # Path information
    metadata = kb.get('metadata', {})
    if isinstance(metadata, dict) and 'path' in metadata:
        print(f"         Path: {metadata['path']}")
    else:
        print(f"         Path: Not available")
    
    # File timestamps information
    if isinstance(metadata, dict) and 'file_timestamps' in metadata and isinstance(metadata['file_timestamps'], dict):
        file_timestamps = metadata['file_timestamps']
        total_files = len(file_timestamps)
        print(f"      📂 File Timestamps:")
        print(f"         Total Files Tracked: {total_files}")
        
        if total_files > 0:
            print(f"         Recent Files:")
            # Display up to 3 files with timestamps
            for i, (file_path, timestamp) in enumerate(list(file_timestamps.items())[:3]):
                formatted_time = format_timestamp(timestamp)
                print(f"            {file_path}: {formatted_time}")
            
            # Indicate if there are more files
            if total_files > 3:
                print(f"            ... and {total_files - 3} more files")
    
    print(f"      ⏰ Sync Configuration:")
    sync_config = kb.get('syncConfig', {})
    if isinstance(sync_config, dict):
        sync_enabled = sync_config.get('enabled', False)
        last_synced = sync_config.get('lastSynced', 0)
        print(f"         Sync Enabled: {'✅' if sync_enabled else '❌'}")
        print(f"         Last Synced: {format_timestamp(last_synced)}")
    else:
        print(f"         Sync Config: {sync_config}")
    
    if kb.get('description'):
        print(f"      📝 Description: {kb.get('description')}")
    
    print()  # Empty line for readability


def categorize_knowledge_bases(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Categorize knowledge bases by source."""
    local_kbs = []
    cloud_kbs = []
    
    for kb in knowledge_bases:
        source = kb.get('source', '').upper()
        if source == 'REMOTE':
            cloud_kbs.append(kb)
        else:
            local_kbs.append(kb)
    
    return {'local': local_kbs, 'cloud': cloud_kbs}


def analyze_sync_statuses(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, int]:
    """Analyze sync status distribution."""
    status_counts = {
        'upload_needed': 0,
        'sync_available': 0,
        'synced': 0,
        'unknown': 0
    }
    
    for kb in knowledge_bases:
        sync_info = determine_sync_status(kb)
        status = sync_info['sync_status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    return status_counts


def analyze_path_uniqueness(knowledge_bases: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze path-based uniqueness constraints."""
    paths_seen = {}
    auto_indexed_count = 0
    manually_uploaded_count = 0
    path_conflicts = []
    
    for kb in knowledge_bases:
        # Count indexing types
        if kb.get('isAutoIndexed'):
            auto_indexed_count += 1
        else:
            manually_uploaded_count += 1
        
        # Check path uniqueness
        metadata = kb.get('metadata', {})
        if isinstance(metadata, dict) and 'path' in metadata:
            path = metadata['path']
            if path in paths_seen:
                path_conflicts.append({
                    'path': path,
                    'kbs': [paths_seen[path], kb.get('name', 'Unknown')]
                })
            else:
                paths_seen[path] = kb.get('name', 'Unknown')
    
    return {
        'total_paths': len(paths_seen),
        'auto_indexed_count': auto_indexed_count,
        'manually_uploaded_count': manually_uploaded_count,
        'path_conflicts': path_conflicts
    }


def print_workflow_demonstration(knowledge_bases: List[Dict[str, Any]]) -> None:
    """Demonstrate the simplified cloud sync workflow."""
    print("=" * 80)
    print("🔄 SIMPLIFIED CLOUD SYNC WORKFLOW DEMONSTRATION")
    print("=" * 80)

    print("Workflow Logic:")
    print("1. Local KB without cloud_id → ⬆️  Upload Needed")
    print("2. Local KB with cloud_id → 🔄 Sync Available")
    print("3. Remote KB → ✅ Synced (always)")
    print()

    print("Current Knowledge Bases in Workflow:")
    for kb in knowledge_bases:
        sync_info = determine_sync_status(kb)
        icon = get_sync_status_icon(sync_info['sync_status'])
        source_icon = get_source_icon(kb.get('source', ''))

        print(f"  {icon} {kb.get('name', 'Unknown')}")
        print(f"     Source: {source_icon} {kb.get('source', 'Unknown')}")
        print(f"     Cloud ID: {'✅ Present' if kb.get('cloud_id') else '❌ None'}")
        print(f"     Status: {sync_info['sync_status']}")
        print(f"     Next Action: {get_next_action_suggestion(sync_info)}")
        print()

    print("=" * 80)


def get_next_action_suggestion(sync_info: Dict[str, Any]) -> str:
    """Get suggested next action based on sync status."""
    if sync_info['can_upload']:
        return "Upload to cloud to enable syncing"
    elif sync_info['can_sync']:
        return "Sync local changes with cloud"
    else:
        return "No action needed - already synced"


def print_summary_analysis(knowledge_bases: List[Dict[str, Any]]) -> None:
    """Print comprehensive summary analysis."""
    if not knowledge_bases:
        print("📊 No knowledge bases found for analysis")
        return

    categorized = categorize_knowledge_bases(knowledge_bases)
    sync_analysis = analyze_sync_statuses(knowledge_bases)
    path_analysis = analyze_path_uniqueness(knowledge_bases)

    print("=" * 80)
    print("📊 COMPREHENSIVE ANALYSIS SUMMARY")
    print("=" * 80)

    # Basic counts
    print(f"📈 Knowledge Base Counts:")
    print(f"   Total KBs: {len(knowledge_bases)}")
    print(f"   🏠 Local KBs: {len(categorized['local'])}")
    print(f"   ☁️  Cloud KBs: {len(categorized['cloud'])}")
    print()

    # Sync status distribution
    print(f"🔄 Sync Status Distribution:")
    print(f"   ⬆️  Upload Needed: {sync_analysis['upload_needed']}")
    print(f"   🔄 Sync Available: {sync_analysis['sync_available']}")
    print(f"   ✅ Synced: {sync_analysis['synced']}")
    print(f"   ❓ Unknown: {sync_analysis['unknown']}")
    print()

    # Path-based uniqueness analysis
    print(f"📁 Path-Based Uniqueness Analysis:")
    print(f"   Unique Paths: {path_analysis['total_paths']}")
    print(f"   Auto-Indexed KBs: {path_analysis['auto_indexed_count']}")
    print(f"   Manually Uploaded KBs: {path_analysis['manually_uploaded_count']}")

    if path_analysis['path_conflicts']:
        print(f"   ⚠️  Path Conflicts Found: {len(path_analysis['path_conflicts'])}")
        for conflict in path_analysis['path_conflicts']:
            print(f"      Path: {conflict['path']}")
            print(f"      KBs: {', '.join(conflict['kbs'])}")
    else:
        print(f"   ✅ No Path Conflicts: Path uniqueness constraint satisfied")
    print()

    # Cloud sync capabilities
    cloud_sync_capable = sum(1 for kb in knowledge_bases
                           if determine_sync_status(kb)['cloud_sync_available'])
    can_upload = sum(1 for kb in knowledge_bases
                    if determine_sync_status(kb)['can_upload'])
    can_sync = sum(1 for kb in knowledge_bases
                  if determine_sync_status(kb)['can_sync'])

    print(f"☁️  Cloud Sync Capabilities:")
    print(f"   Cloud Sync Available: {cloud_sync_capable}")
    print(f"   Can Upload to Cloud: {can_upload}")
    print(f"   Can Sync with Cloud: {can_sync}")

    print("=" * 80)




















def print_path_uniqueness_validation() -> None:
    """Print path-based uniqueness constraint explanation."""
    print("=" * 80)
    print("📁 PATH-BASED UNIQUENESS CONSTRAINT")
    print("=" * 80)

    print("Core Principle:")
    print("• One knowledge base per unique path")
    print("• Either auto-indexed OR manually uploaded, not both")
    print()

    print("Enforcement Rules:")
    print("✅ Auto-indexed KB exists + user uploads same path → Upgrade (reuse ID)")
    print("❌ Uploaded KB exists + user uploads same path → Reject with error")
    print("❌ Uploaded KB exists + auto-indexing same path → Prevent auto-indexing")
    print("✅ Auto-indexed KB exists + auto-indexing same path → Reuse existing")
    print()

    print("Benefits:")
    print("• Eliminates confusion about which KB represents a folder")
    print("• Prevents duplicate processing of same content")
    print("• Clear upgrade path from auto-indexed to uploaded")
    print("• Simplified sync status determination")

    print("=" * 80)


def main():
    """Main function to execute the comprehensive KB test."""
    print("=" * 80)
    print("🧪 COMPREHENSIVE KNOWLEDGE BASE ANALYSIS")
    print("=" * 80)
    print(f"Session ID: {SESSION_ID[:8]}...{SESSION_ID[-8:]}")
    print(f"API Endpoint: {API_ENDPOINT}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Check for detailed analysis flag
    enable_detailed_analysis = "--detailed" in sys.argv or "-d" in sys.argv
    if enable_detailed_analysis:
        print("🔬 Detailed chunking analysis enabled (use --detailed or -d flag)")
    else:
        print("ℹ️  Basic analysis mode (add --detailed or -d for comprehensive chunking analysis)")

    print("=" * 80)
    print()
    
    try:
        # Make API request
        headers = {"x-session": SESSION_ID}
        params = {"include_cloud": True}
        
        print("🔄 Making API request...")
        response = requests.get(API_ENDPOINT, headers=headers, params=params, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ API Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return 1
        
        knowledge_bases = response.json()
        print(f"✅ Successfully retrieved {len(knowledge_bases)} knowledge bases")
        print()
        
        if not knowledge_bases:
            print("📭 No knowledge bases found")
            return 0
        
        # Categorize and display
        categorized = categorize_knowledge_bases(knowledge_bases)
        local_kbs = categorized['local']
        cloud_kbs = categorized['cloud']
        
        # Display local knowledge bases
        if local_kbs:
            print("🏠 LOCAL KNOWLEDGE BASES")
            print("-" * 50)
            for i, kb in enumerate(local_kbs, 1):
                print_kb_details(kb, i, "Local Storage")
        else:
            print("🏠 LOCAL KNOWLEDGE BASES: None found")
            print()
        
        # Display cloud knowledge bases
        if cloud_kbs:
            print("☁️  CLOUD KNOWLEDGE BASES")
            print("-" * 50)
            for i, kb in enumerate(cloud_kbs, 1):
                print_kb_details(kb, i, "Cloud API")
        else:
            print("☁️  CLOUD KNOWLEDGE BASES: None found")
            print()
        
        # Print comprehensive analysis
        print_summary_analysis(knowledge_bases)

        # Note: Detailed chunking analysis has been moved to chunking_analysis_test.py
        if enable_detailed_analysis:
            print("\n💡 For detailed chunking analysis, use the standalone chunking analysis tool:")
            print("   python chunking_analysis_test.py --chunk-size 1000 --overlap 100 --validate")
        else:
            print("\n💡 TIP: For comprehensive chunking analysis, use the standalone tool:")
            print("   python chunking_analysis_test.py --help")

        # Demonstrate workflow
        print_workflow_demonstration(knowledge_bases)

        # Explain path uniqueness constraint
        print_path_uniqueness_validation()

        return 0
        
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST FAILED: {e}")
        return 1
    except json.JSONDecodeError as e:
        print(f"❌ JSON DECODE ERROR: {e}")
        return 1
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
