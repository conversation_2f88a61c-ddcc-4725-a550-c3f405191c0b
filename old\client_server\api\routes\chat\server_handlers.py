"""
Server-based chat processing handlers.

This module contains the chat processing logic for NORMAL and PRO modes
that communicate with external servers.
"""

import asyncio
import json
import time
import traceback
from typing import Any, AsyncGenerator
from uuid import uuid4

import httpx

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import (
    log_memory_usage,
    log_system_info,
)
from client_server.core.state import G_CANCELLED_REQUEST_IDS, G_BASE_URL
from client_server.core.constants import SSL_CERT_FILE
from client_server.utils.router.litellm_router import get_litellm_session_router

from .models import ChatStreamRequest
from .streaming import HTTPStreamEventEmitter
from client_server.utils.prompts.chat_prompts import ChatPrompts
from .utils.context_processors import FileContextProcessor, SwaggerContextProcessor
from .message_processor import DualMessageManager, FrontendEventHandler

# Import tool call handler from the tool_call module
from client_server.utils.tool_call import Tool<PERSON>all<PERSON><PERSON><PERSON>

# Import thinking classification utility
# from client_server.utils.services.thinking_classifier import get_thinking_tag
from client_server.utils.services.llm_model import get_llm_model_name


async def send_chat_history_to_cloud(conversation_messages: list[dict], session_id: str, is_error: bool = False) -> None:
    """
    Send conversation messages to the cloud /chat/history endpoint.

    Args:
        conversation_messages: List of conversation messages from the current chat session
        session_id: Session identifier for the x-session header
    """
    try:
        if not session_id:
            LOGGER.warning("No session ID provided, skipping chat history sync to cloud")
            return

        if not conversation_messages:
            LOGGER.warning("No conversation messages to send to cloud")
            return

        # Get cloud URL from configuration
        base_url = G_BASE_URL.get().cloud
        endpoint_url = f"{base_url}/chat/history"

        LOGGER.info(f"Sending chat history to cloud: {endpoint_url}")
        LOGGER.info(f"Conversation messages send to cloud count: {len(conversation_messages)} with messsages {json.dumps(conversation_messages, indent=2)}")

        # Prepare the payload with conversation messages
        payload = {
            "messages": conversation_messages,
            "is_error": is_error
        }

        # Prepare headers with session identifier
        headers = {
            "x-session": session_id,
            "Content-Type": "application/json"
        }




        # Make the POST request to cloud
        # async with httpx.AsyncClient(
        #     verify=SSL_CERT_FILE,
        #     timeout=30.0  # 30 second timeout
        # ) as client:
        #     response = await client.post(
        #         endpoint_url,
        #         json=payload,
        #         headers=headers
        #     )

        #     if response.status_code == 200:
        #         LOGGER.info("Successfully sent chat history to cloud")
        #     else:
        #         LOGGER.warning(f"Failed to send chat history to cloud. Status: {response.status_code}, Response: {response.text}")

    except Exception as e:
        LOGGER.error(f"Error sending chat history to cloud: {e}")
        LOGGER.error(f"Chat history sync error traceback: {traceback.format_exc()}")
        # Don't raise the exception to avoid disrupting the main chat flow



async def process_chat_request_from_server_http(
    event_emitter: HTTPStreamEventEmitter,
    payload: ChatStreamRequest,
) -> AsyncGenerator[str, None]:
    """
    HTTP streaming version of process_chat_request_from_server.
    Yields Server-Sent Events instead of using WebSocket emissions.
    """
    overall_start_time = time.time()
    log_memory_usage("http_chat_request_start")

    LOGGER.info(
        f"Processing HTTP chat request from server - "
        f"Request ID: {payload.request_id}, Mode: {payload.mode}"
    )
    LOGGER.info(
        f"Message count: {len(payload.messages)}, "
        f"Web search enabled: {payload.web_search}, "
        f"Conversation ID: {payload.conversation_id}"
    )

    # Log system information
    log_system_info()

    # This maintains backward compatibility while enabling dual message system
    context_processing_start = time.time()

    # Handle special context types that need immediate processing (like swagger search)
    for message in payload.messages:
        message_contexts = message.get("context", [])

        for context in message_contexts:
            context_type = context.get("type") if isinstance(context, dict) else getattr(context, "type", None)
            context_name = context.get("name") if isinstance(context, dict) else getattr(context, "name", "")

            # Handle file contexts that need content loading
            if context_type == "file":
                FileContextProcessor.process_file_context(context, context_name)

            # Handle swagger contexts that need search processing
            elif context_type == "swagger":
                try:
                    # Emit searching event before swagger search
                    yield event_emitter.format_as_sse({
                        "event": "chat_response_searching",
                        "data": {"request_id": payload.request_id}
                    })

                    # Process swagger context and get search references
                    search_references = await SwaggerContextProcessor.process_swagger_context(
                        context, message, event_emitter, payload.request_id
                    )

                    # Emit search references if available
                    if search_references:
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": search_references.get_search_result()
                        })

                except Exception as e:
                    LOGGER.error(f"Error processing swagger context: {e}")
                    LOGGER.error(f"Swagger context error traceback: {traceback.format_exc()}")

    context_processing_time = time.time() - context_processing_start
    LOGGER.info(f"Pre-processing completed in {context_processing_time:.2f}s")
    log_memory_usage("after_context_preprocessing")

    try:
        assistant_response = ""   

        # Initialize local LiteLLM tool-calling system
        LOGGER.info("Starting local LiteLLM chat stream with tool calling")
        request_start = time.time()

        # Get LiteLLM router
        router = get_litellm_session_router(payload.session__)
        if not router:
            raise ValueError("Failed to create LiteLLM router")

        # Extract context types from payload for conditional tool selection
        context_types = ToolCallHandler.extract_context_types(payload)
        LOGGER.info(f"Detected context types: {context_types}")

        # Initialize tool call handler with context information
        tool_handler = ToolCallHandler(
            event_emitter,
            payload.session__,
            payload.request_id,
            web_search_enabled=payload.web_search,
            context_types=context_types
        )

        # Extract the latest user message for thinking classification
        latest_user_message = ""
        for message in reversed(payload.messages):
            message_role = message.get("role")
            if message_role == "user":
                # Handle simple text content (Type 1)
                if isinstance(message.get("content"), str):
                    latest_user_message = message.get("content", "")
                    break
                # Handle array content with text (Type 2)
                elif isinstance(message.get("content"), list):
                    latest_user_message = next((
                        item.get("text", "") 
                        for item in message.get("content", [])
                        if item.get("type") == "text"
                    ), "")
                    break

        # Classify the thinking mode required for the user's message
        thinking_tag = "<slow_thinking>"  # Default fallback
        # if latest_user_message.strip():
        #     try:
        #         thinking_tag = get_thinking_tag(latest_user_message)
        #         LOGGER.info(f"Classified user message thinking mode: {thinking_tag}")
        #     except Exception as e:
        #         LOGGER.warning(f"Failed to classify thinking mode, using default: {e}")
        #         thinking_tag = "<slow_thinking>"

        if payload.image:
            thinking_tag = "<image_chat>"   
        model_name = get_llm_model_name(thinking_tag)

        LOGGER.info(f"Using model: {model_name}")

        # Initialize dual message system
        message_manager = DualMessageManager(payload.messages)
        frontend_handler = FrontendEventHandler(event_emitter, payload.request_id)

        # Get LLM sequence messages with context processing
        llm_sequence_messages = await message_manager.initialize_llm_sequence(event_emitter, payload.request_id)

        # Apply thinking mode system prompt
        conversation_messages = ChatPrompts.get_initial_messages(thinking_tag, llm_sequence_messages)

        # Iterative conversation loop with tool calling
        max_iterations = 10
        iteration = 0
        chat_id = str(uuid4())

        LOGGER.info(f"Starting iterative conversation with chat ID: {chat_id}")

        while iteration < max_iterations:
            iteration += 1
            LOGGER.info(f"Conversation iteration {iteration}/{max_iterations}")

            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, breaking iteration loop")
                break

            # Make LLM completion request with tool calling
            try:

                LOGGER.info(f"Conversation messages: {json.dumps(conversation_messages, indent=2)}")

                LOGGER.info(f"Router modle list {router.model_names}")

                llm_response = router.completion(
                    model=model_name,  # Use default model, can be made configurable
                    messages=conversation_messages,
                    tools=tool_handler.get_tool_schemas(),
                    tool_choice="auto",
                    stream=True,
                    temperature=0.5
                )


                # Process streaming response
                final_response = ""
                current_content = ""
                tool_calls = {}
                chunk_count = 0

                for chunk in llm_response:
                    if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                        LOGGER.info(f"Request {payload.request_id} cancelled, breaking chunk loop")
                        break

                    chunk_count += 1

                    # Skip chunks without choices or with empty choices array
                    if not hasattr(chunk, 'choices') or not chunk.choices or len(chunk.choices) == 0:
                        LOGGER.debug(f"Skipping chunk {chunk_count} - no choices available")
                        continue

                    # Handle tool calls
                    if hasattr(chunk.choices[0].delta, 'tool_calls') and chunk.choices[0].delta.tool_calls:
                        for tool_call in chunk.choices[0].delta.tool_calls:
                            tool_call_index = tool_call.index

                            if tool_call_index not in tool_calls:
                                tool_calls[tool_call_index] = {"name": "", "args": "", "id": ""}

                            if tool_call.id:
                                tool_calls[tool_call_index]["id"] = tool_call.id

                            if tool_call.function and tool_call.function.name:
                                tool_calls[tool_call_index]["name"] += tool_call.function.name

                            if tool_call.function and tool_call.function.arguments:
                                tool_calls[tool_call_index]["args"] += tool_call.function.arguments

                    # Handle content streaming
                    elif hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                        final_response += chunk.choices[0].delta.content
                        content = chunk.choices[0].delta.content
                        current_content += content

                        # Stream content to client
                        response_data = {
                            "type": "content",
                            "request_id": payload.request_id,
                            "conversation_id": chat_id,
                            "data": {
                                "type": "content",
                                "memory_id": chat_id,
                                "chunk_index": chunk_count,
                                "content": content,
                            },
                        }

                        await event_emitter.emit("chat_response", data=response_data)
                        yield event_emitter.format_as_sse({
                            "event": "chat_response",
                            "data": response_data
                        })

                # If there's content and no tool calls, add final assistant response and we're done
                if current_content.strip() and not tool_calls:
                    message_manager.add_final_assistant_response(current_content)
                    assistant_response += current_content
                    LOGGER.info(f"Conversation completed after {iteration} iterations")
                    break

                # Process tool calls if any
                if tool_calls:
                    LOGGER.info(f"Processing {len(tool_calls)} tool calls")
                    LOGGER.info(f"Tool calls: {json.dumps(tool_calls, indent=2)}")

                    # Prepare tool calls for dual message system
                    formatted_tool_calls = []
                    tool_results = []
                    requires_followup = False

                    for tool_call_index, func_data in tool_calls.items():
                        if func_data["name"] and func_data["args"]:
                            try:
                                # Format tool call
                                tool_call = {
                                    "id": func_data["id"] or f"call_{tool_call_index}",
                                    "type": "function",
                                    "function": {
                                        "name": func_data["name"],
                                        "arguments": func_data["args"]
                                    }
                                }
                                formatted_tool_calls.append(tool_call)

                                # Emit tool call request event (new granular event)
                                # if hasattr(event_emitter, 'emit_tool_call_request'):
                                #     try:
                                #         parsed_args = json.loads(func_data["args"])
                                #     except json.JSONDecodeError:
                                #         parsed_args = {"raw_args": func_data["args"]}

                                #     sse_event = await event_emitter.emit_tool_call_request(
                                #         tool_call["id"], func_data["name"], parsed_args
                                #     )
                                #     yield sse_event

                                # Execute the tool call
                                tool_call_data = {
                                    "id": tool_call["id"],
                                    "function": {
                                        "name": func_data["name"],
                                        "arguments": func_data["args"]
                                    }
                                }

                                # Store events before tool execution to yield new ones
                                events_before = len(event_emitter.events)

                                tool_result, needs_followup = await tool_handler.execute_tool_call(tool_call_data)

                                # Yield any new events that were emitted during tool execution
                                events_after = len(event_emitter.events)
                                if events_after > events_before:
                                    for event in event_emitter.events[events_before:]:
                                        yield event_emitter.format_as_sse(event)

                                # Add tool result
                                tool_results.append({
                                    "role": "tool",
                                    "tool_call_id": tool_result["tool_call_id"],
                                    "content": tool_result["content"]
                                })

                                if needs_followup:
                                    requires_followup = True

                            except json.JSONDecodeError as e:
                                LOGGER.error(f"JSON decode error for tool call {tool_call_index}: {e}")

                                # Emit tool call error event for JSON decode error
                                # if hasattr(event_emitter, 'emit_tool_call_error'):
                                #     tool_id = func_data["id"] or f"call_{tool_call_index}"
                                #     sse_event = await event_emitter.emit_tool_call_error(
                                #         tool_id, func_data["name"], f"Invalid arguments: {e}"
                                #     )
                                #     yield sse_event

                                tool_results.append({
                                    "role": "tool",
                                    "tool_call_id": func_data["id"] or f"call_{tool_call_index}",
                                    "content": json.dumps({
                                        "error": f"Invalid arguments: {e}",
                                        "status": "error"
                                    })
                                })
                                requires_followup = True
                            except Exception as e:
                                LOGGER.error(f"Error executing tool call {tool_call_index}: {e}")

                                # Emit tool call error event for general execution error
                                # if hasattr(event_emitter, 'emit_tool_call_error'):
                                #     tool_id = func_data["id"] or f"call_{tool_call_index}"
                                #     sse_event = await event_emitter.emit_tool_call_error(
                                #         tool_id, func_data["name"], str(e)
                                #     )
                                #     yield sse_event

                                tool_results.append({
                                    "role": "tool",
                                    "tool_call_id": func_data["id"] or f"call_{tool_call_index}",
                                    "content": json.dumps({
                                        "error": str(e),
                                        "status": "error"
                                    })
                                })
                                requires_followup = True

                    # Use dual message system to handle tool calls
                    assistant_message = message_manager.add_assistant_message_with_tool_calls(
                        current_content, formatted_tool_calls
                    )
                    message_manager.add_tool_call_results(tool_results)

                    # Update conversation messages for LLM
                    conversation_messages.append(assistant_message)
                    conversation_messages.extend(tool_results)

                    # Emit search references if available
                    if tool_handler.search_references and tool_handler.search_references.get_search_result().get("results"):
                        await event_emitter.emit(
                            "chat_response_references",
                            data=tool_handler.search_references.get_search_result()
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": tool_handler.search_references.get_search_result()
                        })
                        await asyncio.sleep(0.001)

                    # If no functions need follow-up, make one final LLM call to get the response
                    if not requires_followup:
                        LOGGER.info("No functions require follow-up, making final LLM call for response")
                        # Continue to next iteration to get final assistant response
                        # The LLM will process the tool results and provide a final response
                else:
                    # No tool calls, add final assistant response
                    if current_content.strip():
                        message_manager.add_final_assistant_response(current_content)
                        assistant_response += current_content
                    LOGGER.info("No tool calls, conversation complete")
                    break

            except Exception as e:
                LOGGER.error(f"Error in LLM completion iteration {iteration}: {e}")
                LOGGER.error(f"LLM error traceback: {traceback.format_exc()}")
                # Continue to next iteration or break if critical error
                break

        if iteration >= max_iterations:
            LOGGER.warning(f"Maximum conversation iterations ({max_iterations}) reached")

        request_time = time.time() - request_start
        LOGGER.info(f"Local LiteLLM chat stream completed in {request_time:.2f}s")

        # Generate follow-ups using local LLM if requested
        if payload.provide_followups and assistant_response.strip():
            LOGGER.info("Starting local follow-up generation")
            last_user_message_content = ""
            for message in reversed(payload.messages):
                message_role = message.get("role") if isinstance(message, dict) else getattr(message, "role", "")
                if message_role == "user":
                    last_user_message_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
                    break

            LOGGER.debug(
                f"Last user message for follow-ups: {str(last_user_message_content)[:100]}..."
            )

            try:
                followup_start = time.time()

                # Use local LLM for follow-up generation
                conversation_context = [
                    {"role": "user", "content": last_user_message_content},
                    {"role": "assistant", "content": assistant_response}
                ]
                followup_messages = ChatPrompts.get_followup_messages(conversation_context)
                LOGGER.info(f"Follow-up messages: {json.dumps(followup_messages, indent=2)}")
                model_name = get_llm_model_name("followup")
                followup_response = router.completion(
                    model=model_name,
                    messages=followup_messages,
                    temperature=0.7,
                    max_tokens=200
                )

                # LOGGER.info(f"Follow-up response: {json.dumps(followup_response, indent=2)}")

                followup_time = time.time() - followup_start

                # Parse follow-up response
                followup_content = followup_response.choices[0].message.content
                try:
                    follow_ups = json.loads(followup_content)
                    if not isinstance(follow_ups, list):
                        follow_ups = []
                except json.JSONDecodeError:
                    LOGGER.warning("Failed to parse follow-up JSON, using fallback")
                    follow_ups = []

                LOGGER.info(
                    f"Generated {len(follow_ups)} follow-up suggestions in {followup_time:.2f}s"
                )

                await event_emitter.emit(
                    "chat_response_follow_ups",
                    data={
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    }
                )
                yield event_emitter.format_as_sse({
                    "event": "chat_response_follow_ups",
                    "data": {
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    }
                })
                LOGGER.debug("Follow-ups emitted to client")

            except Exception as e:
                LOGGER.error(f"Error during follow-ups generation: {e}")
                LOGGER.error(f"Follow-up error traceback: {traceback.format_exc()}")

        

        await asyncio.sleep(0.5)

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        overall_time = time.time() - overall_start_time
        log_memory_usage("http_chat_request_complete")

        LOGGER.info(
            f"HTTP chat request completed successfully - Total time: {overall_time:.2f}s, "
            f"Response length: {len(assistant_response)} chars"
        )

        LOGGER.info(f"Final response: {assistant_response}")

        # Send chat history to cloud after successful completion using payload format
        try:
            cloud_messages = message_manager.get_messages_for_cloud_sync()
            await send_chat_history_to_cloud(cloud_messages, payload.session__, is_error=False)
        except Exception as e:
            LOGGER.error(f"Failed to send chat history to cloud: {e}")
            # Continue with normal flow even if cloud sync fails

    except Exception as e:
        overall_time = time.time() - overall_start_time
        LOGGER.error(f"Error during HTTP chat request after {overall_time:.2f}s: {e}")
        LOGGER.error(f"HTTP chat request error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)
        await event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        })

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        log_memory_usage("http_chat_request_error")
        try:
            # Send error state to cloud with available messages
            if 'message_manager' in locals():
                cloud_messages = message_manager.get_messages_for_cloud_sync()
                await send_chat_history_to_cloud(cloud_messages, payload.session__, is_error=True)
        except Exception as e:
            LOGGER.error(f"Failed to send chat history to cloud: {e}")

        raise
