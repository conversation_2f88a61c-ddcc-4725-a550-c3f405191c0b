.PHONY: start dev install setup clean all build

start:
ifeq ($(OS),Windows_NT)
	@cmd /c cls 2>nul || echo.
else
	@clear
endif
	@echo "Starting CodeMate dual-port server..."
	@echo "HTTP REST API: http://127.0.0.1:45213"
	@echo "WebSocket: ws://127.0.0.1:45214"
	@echo ""
	@python -m poetry run server

dev: start

install:
	@poetry install

setup:
	@pip install poetry
	@poetry config virtualenvs.in-project true
	@make install

clean: clean-build clean-venv clean-cache

clean-cache:
	@rm -rf .cache

clean-venv:
	-@if [ ! -z "$$VIRTUAL_ENV" ]; then \
		source deactivate 2>/dev/null || true; \
	fi
	@rm -rf .venv

all: setup start

ifeq ($(OS),Windows_NT)
    CLEAN := del /F /Q
    RMDIR := rmdir /S /Q
    NULL := NUL
else
    CLEAN := rm -f
    RMDIR := rm -rf
    NULL := /dev/null
endif



build:
ifeq ($(OS),Windows_NT)
	@cmd /c cls 2>nul || echo.
else
	@clear
endif
	@poetry run build
