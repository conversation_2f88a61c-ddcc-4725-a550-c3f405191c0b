# Inline Suggestion Event (`inline_suggestion.py`)

The `inline_suggestion.py` module provides real-time code completion suggestions as the user types.

## Overview

When a user is editing a file, the client sends the code context around the cursor to the server via the `inline_suggestion` event. The server then uses a language model to predict the most likely completion and sends it back to the client, which displays it as a grayed-out suggestion.

## Data Models

- `InlineSuggestionRequest`: This model captures the full context needed to generate a suggestion.
  - `requestID: str`: A unique ID for the suggestion request.
  - `language: str`: The programming language of the file.
  - `prefix: list[str]`: The lines of code before the cursor.
  - `current: str`: The content of the line the cursor is on.
  - `suffix: list[str]`: The lines of code after the cursor.
  - `filepath: str`: The path of the file being edited.
  - `position: dict`: The line and character position of the cursor.

## Event Handler

- `handle_inline_suggestion_event(sio, sid, data)`:
  - The main entry point for suggestion requests.
  - It validates the incoming `InlineSuggestionRequest` payload.
  - It calls the `get_inline_suggestion` function to generate the suggestion.
  - It emits an `inline_suggestion:response` event containing the suggestion and a status (`success` or `error`).

## Suggestion Generation

The system can generate suggestions using two different backends, although it's currently configured to use the cloud service.

- `get_inline_suggestion_from_cloud(request, model)`:

  - This function sends the `prefix`, `suffix`, and other context to a dedicated cloud endpoint (`/generate`).
  - The cloud service is optimized for code completion and returns the suggested code.
  - This is the default method.

- `get_inline_suggestion_from_local(request, model)`:
  - This function uses a local language model for suggestions.
  - It formats the code by placing an `[INFILL]` marker at the cursor's position.
  - It uses a specific system prompt (`INLINE_SYSTEM_PROMPT`) to instruct the local model to fill in the missing code.
  - This method is suitable for offline use or when `ECO` mode is preferred.

## Event Flow

This diagram shows the flow for getting an inline code suggestion.

<details>
<summary>View Inline Suggestion Flow</summary>

```mermaid
sequenceDiagram
    participant E as User's Editor
    participant S as Our Server
    participant AI as Cloud AI

    Note over E: As the user types...
    E->>S: Editor sends the surrounding code for context
    S->>S: Process the code context
    S->>AI: Ask Cloud AI for a code completion
    AI-->>S: Cloud AI provides a suggestion
    S-->>E: Show the suggestion to the user
```

</details>
