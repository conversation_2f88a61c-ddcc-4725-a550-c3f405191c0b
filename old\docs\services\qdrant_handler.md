# Qdrant Handler (qdrant_handler.py)

The `qdrant_handler.py` service provides a simple, centralized access point to the Qdrant vector database client. Qdrant is used throughout the application as the primary store for vector embeddings, enabling powerful semantic search capabilities for features like knowledge bases.

This service is a single file, following the project's design principle for utilities that have one clear, concrete implementation.

## Design

The design is straightforward, focusing on providing a global, singleton-like instance of the `QdrantClient`.

- **Client Initialization**: It initializes the `QdrantClient` in local mode, pointing it to a directory on the file system. The path is managed by `PathSelector.get_qdrant_db_path()`, ensuring that the vector database is stored in a consistent, user-specific location.
- **Singleton Access**: It uses a global variable (`_qc`) and a getter function (`get_db_client`) to ensure that the application uses a single, shared instance of the client. This is efficient and prevents issues related to multiple clients accessing the same database files.

## Core Components

### `get_db_client() -> QdrantClient`

This is the only function in the service.

- When called for the first time, it checks if the global `_qc` client instance is `None`.
- If it is, it creates a new `QdrantClient`, configuring it to use the local path from `PathSelector`.
- It then returns the client instance.
- Subsequent calls will find that `_qc` is already initialized and will simply return the existing instance.

## Workflow

Any part of the application that needs to interact with the Qdrant vector database (e.g., to add or search for embeddings) will do so by calling this function.

**Example Usage:**

```python
from client_server.services.qdrant_handler import get_db_client

# Get the shared Qdrant client
qdrant = get_db_client()

# Now use the client to perform operations
qdrant.recreate_collection(
    collection_name="my_test_collection",
    vectors_config=...
)

qdrant.upsert(...)

results = qdrant.search(...)
```
