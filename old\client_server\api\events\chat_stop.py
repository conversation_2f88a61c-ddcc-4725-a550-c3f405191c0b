import traceback
from typing import Any

import socketio
from pydantic import BaseModel

from client_server.core.logger import LOGGER
from client_server.core.state import G_CANCELLED_REQUEST_IDS


class ChatStopPayload(BaseModel):
    request_id: str


async def handle_chat_stop_event(
    sio: socketio.AsyncServer, sid: str, data: dict[str, Any]
):
    try:
        # Validate payload
        payload = ChatStopPayload.model_validate(data)
        LOGGER.info(f"Chat stop event received for request {payload.request_id}")

        G_CANCELLED_REQUEST_IDS.set(payload.request_id)

    except Exception as e:
        LOGGER.error(f"Error in chat stop event handler: {e}")
        LOGGER.error(f"Chat stop handler error traceback: {traceback.format_exc()}")
        raise
