import os
import time
import json
from uuid import uuid4
import httpx
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Callable, Coroutine

from client_server.core.constants import SSL_CONTEXT
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_execution_time, log_file_stats
from client_server.core.state import G_BASE_URL
from client_server.utils.path_selector import PathSelector
from . import IChunker
from overrides import override

from client_server.utils.models.knowledgebase import (
    QdrantKnowledgebaseChunkMetadata,
    QdrantKnowledgeBaseChunk,
    QdrantSwaggerEndpoint,
    QdrantSwaggerMetadata,
)


class SwaggerChunker(IChunker):
    def __init__(self, metadata: QdrantSwaggerMetadata):
        self.metadata = metadata

    @override
    async def process(
        self,
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        chunks = await self._make_chunks(progress_callback)
        return chunks

    def _get_summary(self, endpoint: QdrantSwaggerEndpoint):
        start_time = time.time()
        endpoint_content = endpoint.model_dump_json()
        LOGGER.debug(f"Generating summary for endpoint: {endpoint_content[:100]}...")
        LOGGER.debug(f"Endpoint full length: {len(endpoint_content)} characters")

        content = (
            "Your task is to generate a summary and possible use for the provided endpoint.\nBe descriptive, explain thoroughly, mention all aspects.\n\nSTRICT INSTRUCTION: THe SUMMARY SHOULD NOT BE MORE THAN 5 LINES LONG."
            + endpoint_content
            + "\n\n\nSTRICT INSTRUCTION:\nWrap the summary in <summary> and </summary> tags. Nothing related to the endpoint shall be outside the tags."
        )

        LOGGER.debug(f"Generated content length: {len(content)} characters")

        retries = 10

        while retries > 0:
            # Make API call and return data.
            try:
                base_url = G_BASE_URL.get().general
                LOGGER.debug(f"Making API call to {base_url}/swagger/summary")
                api_start_time = time.time()

                with httpx.Client(verify=SSL_CONTEXT) as client:
                    response = client.post(
                        f"{base_url}/swagger/summary",
                        json={"content": content},
                        timeout=30,  # Add timeout for better error handling
                    )

                    api_time = time.time() - api_start_time
                    LOGGER.debug(f"API call completed in {api_time:.2f} seconds")

                    if response.status_code != 200:
                        LOGGER.error(
                            f"API call failed with status {response.status_code}: {response.text[:500]}..."
                        )
                        raise ValueError(
                            f"API call failed with status {response.status_code}"
                        )

                    response_length = len(response.text)
                    LOGGER.debug(
                        f"API call successful, summary generated (length: {response_length} chars)"
                    )
                    log_execution_time("get_summary", start_time)
                    return response.text.strip('"')
            except Exception as e:
                LOGGER.error(f"Error generating endpoint summary: {e}")

            time.sleep(1)
            retries -= 1
        return ""

    async def _make_chunks(
        self,
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        LOGGER.info("Processing Swagger type knowledge base")
        endpoint_count = len(self.metadata.endpoints)
        LOGGER.info(f"Processing {endpoint_count} endpoints")

        # Log endpoint statistics
        methods = {}
        for endpoint in self.metadata.endpoints:
            method = getattr(endpoint, "method", "unknown")
            methods[method] = methods.get(method, 0) + 1
        LOGGER.info(f"Endpoint methods distribution: {methods}")

        MAX_WORKERS = max(1, 10)
        LOGGER.debug(f"Will use {MAX_WORKERS} threads for endpoint processing")

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            LOGGER.debug(f"Created thread pool with {MAX_WORKERS} workers")

            endpoint_processing_start = time.time()
            futures = [
                executor.submit(self._get_summary, endpoint=endpoint)
                for endpoint in self.metadata.endpoints
            ]
            LOGGER.debug(f"Submitted {len(futures)} endpoint summary tasks")

            endpoint_summaries = map(lambda x: x.result(), as_completed(futures))
            processed_endpoints = 0

            chunks = []
            for i, (endpoint, summary) in enumerate(
                zip(self.metadata.endpoints, endpoint_summaries)
            ):
                LOGGER.debug(
                    f"Processing endpoint {i+1}/{endpoint_count}: {endpoint.path}"
                )

                base_path = PathSelector.get_qdrant_db_path()
                endpoints_dir = base_path / "swagger_endpoints" / str(uuid4())
                os.makedirs(endpoints_dir, exist_ok=True)

                endpoint_file = f"{endpoints_dir}/{str(uuid4())}.json"
                endpoint_data = {
                    "path": endpoint.path,
                    "summary": summary,
                    "endpoint": endpoint.model_dump(),
                }

                with open(endpoint_file, "w+") as f:
                    f.write(json.dumps(endpoint_data, indent=2))

                file_size = log_file_stats(endpoint_file)
                LOGGER.debug(
                    f"Saved endpoint data to: {endpoint_file} (size: {file_size} bytes)"
                )

                chunks.append(
                    QdrantKnowledgeBaseChunk(
                        embeddings=[],
                        metadata=QdrantKnowledgebaseChunkMetadata(
                            id=str(uuid4()),
                            file=endpoint_file,
                            name=endpoint.path,
                            content=summary,
                            additional_metadata=endpoint.model_dump(),
                        ),
                    )
                )

                processed_endpoints += 1
                progress_pct = (processed_endpoints / endpoint_count) * 100
                LOGGER.info(f"Progress: {progress_pct:.2f}%")
                if progress_callback:
                    await progress_callback(progress_pct)

            endpoint_processing_time = time.time() - endpoint_processing_start
            LOGGER.info(
                f"Endpoint processing completed in {endpoint_processing_time:.2f} seconds (total chunks: {len(chunks)})"
            )

            return chunks
