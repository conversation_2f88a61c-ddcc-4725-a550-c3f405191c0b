# Path Selector (`path_selector.py`)

The `path_selector.py` module is responsible for managing file system paths used by the application. It provides a centralized and persistent way to define the base directory for storing application data, caches, logs, and the Qdrant database.

The configuration is stored in a `settings.json` file located in the user's home directory.

## Classes

### `HomePathFinder`

A utility class designed to reliably determine the user's home directory across various operating systems (Windows, macOS, Linux). It tries several methods in succession to ensure a valid path is found.

#### `get_home_path() -> Path`

- **Returns:**
  - `pathlib.Path`: The path to the user's home directory.

### `PathSelector`

A static class that acts as the main interface for path management. It reads from and writes to the `settings.json` file to manage the application's base path.

#### `get_user_home_path() -> Path`

- **Returns:**
  - `pathlib.Path`: The user's home directory path, obtained via `HomePathFinder`.

#### `get_settings_file() -> Path`

- **Returns:**
  - `pathlib.Path`: The full path to the `settings.json` file.

#### `set_base_path(base_path: Path)`

Sets the application's base path and saves it to `settings.json`.

- **Parameters:**
  - `base_path` (pathlib.Path): The path to be set as the base directory.

#### `get_base_path() -> Path`

Retrieves the base path from `settings.json`. If it's not set, it defaults to a `.codemate` directory within the user's home folder.

- **Returns:**
  - `pathlib.Path`: The application's base path.

#### `get_cache_path(make_if_not_exists: bool = True)`

- **Returns:**
  - `pathlib.Path`: The path to the cache directory (`<base_path>/.cache`).

#### `get_logs_path(make_if_not_exists: bool = True)`

- **Returns:**
  - `pathlib.Path`: The path to the logs directory (`<base_path>/.logs`).

#### `get_qdrant_db_path(make_if_not_exists: bool = True)`

- **Returns:**
  - `pathlib.Path`: The path to the Qdrant database storage directory (`<base_path>/.neocortex`).
