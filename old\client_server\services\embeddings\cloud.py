import asyncio
from overrides import override
from typing import Coroutine
import httpx

from client_server.core.constants import SSL_CONTEXT
from client_server.core.logger import LOGGER
from client_server.core.state import G_BASE_URL
from . import IEmbeddingBackend


class CloudEmbeddingsBackend(IEmbeddingBackend):
    @override
    async def generate(self, content: str) -> Coroutine[list[float], None, None]:
        return (await self.generate_batch([content]))[0]

    @override
    async def generate_batch(
        self, content: list[str]
    ) -> Coroutine[list[list[float]], None, None]:
        base_url = G_BASE_URL.get().embeddings

        error = None
        retry_count = 10
        while retry_count > 0:
            try:
                async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
                    response = await client.post(
                        f"{base_url}/generate",
                        json={"texts": content},
                        timeout=30000,
                    )
                response.raise_for_status()
                body = response.json()
                embeddings = [embedding["embedding"] for embedding in body]
                return embeddings
            except Exception as e:
                retry_count -= 1
                await asyncio.sleep(1)
                error = e
        raise error
