"""
Timestamp Migration Utility

This module provides utilities for migrating existing knowledge bases
to support the new file-level timestamp tracking system.

This is primarily for backward compatibility when upgrading from
versions that didn't have timestamp tracking.
"""

from pathlib import Path
from client_server.core.logger import LOGGER
from client_server.utils.models.knowledgebase import QdrantKnowledgeBase
from client_server.utils.path_selector import PathSelector


def run_timestamp_migration() -> bool:
    """
    Run the timestamp migration for existing knowledge bases.
    This should be called during server startup to ensure all KBs
    have the necessary timestamp tracking fields.
    
    Returns:
        True if migration completed successfully, False otherwise
    """
    try:
        LOGGER.info("🔄 Starting timestamp tracking migration for existing knowledge bases")
        
        # Run the migration
        migrated_count = QdrantKnowledgeBase.migrate_existing_kbs_for_timestamps()
        
        if migrated_count > 0:
            LOGGER.info(f"✅ Timestamp migration completed successfully: {migrated_count} knowledge bases updated")
        else:
            LOGGER.info("✅ Timestamp migration completed: no knowledge bases needed migration")
            
        return True
        
    except Exception as e:
        LOGGER.error(f"❌ Timestamp migration failed: {e}")
        return False


def check_migration_needed() -> bool:
    """
    Check if timestamp migration is needed by looking for codebase KBs
    without file_timestamps field OR with empty file_timestamps.

    Returns:
        True if migration is needed, False otherwise
    """
    try:
        from client_server.services.db_handler import Database

        kbdb = Database()["knowledge_bases"]

        # Look for codebase KBs that need migration:
        # 1. KBs without file_timestamps field at all
        # 2. KBs with empty file_timestamps but have files that should have timestamps

        # Check for KBs without file_timestamps field
        kb_without_field = kbdb.find_one({
            "type": "codebase",
            "metadata.file_timestamps": {"$exists": False}
        })

        if kb_without_field:
            return True

        # Check for KBs with empty file_timestamps but have files
        for kb_record in kbdb.find({"type": "codebase"}):
            metadata = kb_record.get("metadata", {})
            if not metadata:
                continue

            file_timestamps = metadata.get("file_timestamps", {})
            files_list = metadata.get("files", [])

            # If KB has files but no timestamps, it needs migration
            if len(files_list) > 0 and len(file_timestamps) == 0:
                LOGGER.debug(f"Found KB '{kb_record.get('name', 'unknown')}' with {len(files_list)} files but no timestamps")
                return True

        return False

    except Exception as e:
        LOGGER.error(f"Error checking migration status: {e}")
        return True  # Assume migration is needed if we can't check


def get_migration_status() -> dict:
    """
    Get detailed status information about the timestamp migration.
    
    Returns:
        Dictionary with migration status information
    """
    try:
        from client_server.services.db_handler import Database
        
        kbdb = Database()["knowledge_bases"]
        
        # Count total codebase KBs
        total_codebase_kbs = kbdb.count_documents({"type": "codebase"})
        
        # Count codebase KBs with timestamps
        kbs_with_timestamps = kbdb.count_documents({
            "type": "codebase",
            "metadata.file_timestamps": {"$exists": True}
        })
        
        # Count codebase KBs without timestamps
        kbs_without_timestamps = kbdb.count_documents({
            "type": "codebase",
            "metadata.file_timestamps": {"$exists": False}
        })
        
        return {
            "total_codebase_kbs": total_codebase_kbs,
            "kbs_with_timestamps": kbs_with_timestamps,
            "kbs_without_timestamps": kbs_without_timestamps,
            "migration_needed": kbs_without_timestamps > 0,
            "migration_complete": kbs_without_timestamps == 0
        }
        
    except Exception as e:
        LOGGER.error(f"Error getting migration status: {e}")
        return {
            "error": str(e),
            "migration_needed": True,
            "migration_complete": False
        }


def _get_migration_marker_file() -> Path:
    """
    Get the path to the timestamp migration marker file.

    Returns:
        Path to the migration marker file in .codemate folder
    """
    codemate_folder = PathSelector.get_base_path()
    return codemate_folder / "timestamp_migration_complete.marker"


def _is_migration_already_completed() -> bool:
    """
    Check if timestamp migration has already been completed by looking for marker file.

    Returns:
        True if migration marker file exists, False otherwise
    """
    marker_file = _get_migration_marker_file()
    return marker_file.exists()


def _create_migration_marker() -> bool:
    """
    Create a marker file to indicate that timestamp migration has been completed.

    Returns:
        True if marker file was created successfully, False otherwise
    """
    try:
        marker_file = _get_migration_marker_file()

        # Ensure the .codemate folder exists
        marker_file.parent.mkdir(parents=True, exist_ok=True)

        # Create the marker file with timestamp information
        with open(marker_file, 'w', encoding='utf-8') as f:
            import time
            f.write(f"Timestamp migration completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("This file indicates that the timestamp tracking migration has been completed.\n")
            f.write("Do not delete this file unless you want to re-run the migration.\n")

        LOGGER.info(f"✅ Created migration marker file: {marker_file}")
        return True

    except Exception as e:
        LOGGER.error(f"❌ Failed to create migration marker file: {e}")
        return False


def perform_conditional_timestamp_migration() -> bool:
    """
    Perform timestamp migration conditionally during server startup.
    Only runs migration if it hasn't been completed before and legacy KBs exist.

    This function follows the same pattern as startup_config.py:
    - Checks for a marker file to see if migration was already completed
    - Only runs migration when legacy knowledge bases are detected
    - Creates a marker file after successful migration to prevent re-running

    Returns:
        True if migration was performed, False if skipped or failed
    """
    try:
        LOGGER.info("🔄 Starting conditional timestamp migration check...")

        # Check if migration has already been completed
        if _is_migration_already_completed():
            LOGGER.info("⏭️  Timestamp migration already completed, skipping migration")
            marker_file = _get_migration_marker_file()
            LOGGER.info(f"📄 Migration marker file location: {marker_file}")
            return False

        # Check if migration is actually needed
        if not check_migration_needed():
            LOGGER.info("⏭️  No legacy knowledge bases found, creating migration marker")
            # Create marker file even if no migration was needed to prevent future checks
            _create_migration_marker()
            return False

        LOGGER.info("🔍 Legacy knowledge bases detected, starting migration...")

        # Run the actual migration
        migration_success = run_timestamp_migration()
        if not migration_success:
            LOGGER.error("❌ Timestamp migration failed")
            return False

        # Create marker file to indicate migration completion
        marker_created = _create_migration_marker()
        if not marker_created:
            LOGGER.warning("⚠️  Migration completed but failed to create marker file")
            # Don't return False here since migration itself succeeded

        LOGGER.info("✅ Conditional timestamp migration completed successfully")
        return True

    except Exception as e:
        LOGGER.error(f"❌ Error during conditional timestamp migration: {e}")
        LOGGER.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
        return False
