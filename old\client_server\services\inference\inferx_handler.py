import random
import socket
import subprocess
import time
from pathlib import Path
from typing import Optional, <PERSON><PERSON>

import litellm
from overrides import override

from client_server.core.logger import LOGGER
from client_server.utils.path_selector import PathSelector

from . import IInferenceBackend

MODELS_SOURCES = {
    # Chat Models
    "openai/chat": "chat",
    "openai/autocomplete": "autocomplete",
}


class InferXInferenceBackend(IInferenceBackend):
    """
    A singleton interface for InferX interactions.
    Manages InferX server processes and handles inference requests.
    """

    _instance: Optional["InferXInferenceBackend"] = None
    _model_processes: dict[str, Tuple[subprocess.Popen, int]] = {}

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, base_path: Path = PathSelector.get_cache_path() / "inferx"):
        if not hasattr(self, "_initialized"):
            self._base_path = base_path
            self._inferx_path = self._base_path / "inferx.exe"
            self._models_path = self._base_path / "models"
            self._initialized = True

    def get_available_port(self, start: int = 11435, end: int = 11800) -> int:
        """Generate a random available port number within the given range."""
        while True:
            port = random.randint(start, end)
            try:
                # Test if port is in use
                test_socket = subprocess.run(
                    ["netstat", "-an"],
                    capture_output=True,
                    text=True,
                    shell=False,
                )
                if str(port) not in test_socket.stdout:
                    return port
            except Exception:
                continue

    def _create_inference(
        self, model: str
    ) -> Tuple[Optional[subprocess.Popen], Optional[int]]:
        """
        Create a new inference deployment for the specified model.

        Args:
            model: Name of the model to deploy

        Returns:
            Tuple containing the subprocess handle and port number, or (None, None) on failure
        """

        if model not in MODELS_SOURCES:
            raise ValueError(f"Model {model} not supported")
        model_source = MODELS_SOURCES[model]

        try:
            # Validate inferx executable path
            if not self._inferx_path.exists():
                raise FileNotFoundError(
                    f"InferX executable not found: {self._inferx_path}"
                )

            # Validate model paths
            model_params_path = self._models_path / model_source / "model_params"
            model_lib_path = self._models_path / model_source / "snapdragon-x.dll"
            for p in [model_params_path, model_lib_path]:
                if not p.exists():
                    raise FileNotFoundError(
                        f"Required model file or inferx executable not found: {p}"
                    )

            # Get available port
            port = self.get_available_port()

            # Launch inference process
            process = subprocess.Popen(
                [
                    self._inferx_path.resolve(),
                    model_params_path.resolve(),
                    "--model-lib",
                    model_lib_path.resolve(),
                    "--port",
                    str(port),
                    "--device",
                    "opencl",
                ],
                stderr=subprocess.STDOUT,
                text=True,
                shell=False,
            )
            LOGGER.info(f"InferX server starting for model {model} on port {port}...")

            # Check if the server started successfully
            start_time = time.time()
            timeout = 60 * 2  # 2 minutes
            connected = False
            while time.time() - start_time < timeout:
                if process.poll() is not None:  # Process terminated prematurely
                    LOGGER.error(
                        f"InferX process for model {model} exited unexpectedly with code {process.returncode}."
                    )
                    # Optionally read stderr/stdout from process if needed for debugging
                    return None, None

                try:
                    with socket.create_connection(("localhost", port), timeout=1):
                        connected = True
                        LOGGER.info(
                            f"Successfully connected to InferX server for model {model} on port {port}"
                        )
                        break
                except (socket.timeout, ConnectionRefusedError):
                    time.sleep(0.5)  # Wait before retrying
                except Exception as sock_err:
                    LOGGER.warning(f"Socket check error for model {model}: {sock_err}")
                    time.sleep(0.5)

            if not connected:
                LOGGER.error(
                    f"InferX server for model {model} failed to start listening on port {port} within {timeout} seconds."
                )
                process.terminate()
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    process.kill()
                return None, None

            LOGGER.info(f"InferX server started for model {model} on port {port}")
            return process, port
        except FileNotFoundError as fnf_error:
            LOGGER.error(f"File not found error during inference creation: {fnf_error}")
            return None, None
        except Exception as e:
            LOGGER.error(f"Error creating inference: {e}")
            return None, None

    def _destroy_inference(self, process: subprocess.Popen) -> bool:
        """
        Safely terminate an inference process.

        Args:
            process: Subprocess handle to terminate

        Returns:
            bool indicating success/failure
        """
        try:
            if (
                process and process.poll() is None
            ):  # Check if process exists and is running
                process.terminate()  # Try graceful termination first
                try:
                    process.wait(timeout=5)  # Wait for graceful shutdown
                except subprocess.TimeoutExpired:
                    process.kill()  # Force kill if graceful shutdown fails
                    process.wait()
            return True
        except Exception as e:
            LOGGER.error(f"Error destroying inference process: {e}")
            return False

    @override(check_signature=False)
    def ensure_model(self, model: str) -> int:
        """
        Ensure model server is running and return its port.
        Starts the server if not already running.

        Args:
            model: Name of the model to ensure

        Returns:
            int: Port number where the model server is running
        """
        if model not in self._model_processes:
            process, port = self._create_inference(model)
            if process is None or port is None:
                raise RuntimeError(
                    f"Failed to start inference server for model {model}"
                )
            self._model_processes[model] = (process, port)
            LOGGER.info(f"Started inference server for model {model} on port {port}")

        return self._model_processes[model][1]

    @override(check_signature=False)
    def generate(self, model: str, messages: list[dict[str, str]], **model_params):
        """
        Generate completion using specified model and chat messages.

        Args:
            model: The name of the model to use
            messages: List of message dictionaries with 'role' and 'content' keys
            **model_params: Additional parameters to pass to the model
        """
        port = self.ensure_model(model)

        response = litellm.completion(
            model=model,
            messages=messages,
            base_url=f"http://localhost:{port}/v1/",
            api_key="klasdhfghsjfgl",
            max_tokens=1000,
            **model_params,
        )
        return response

    @override(check_signature=False)
    def stream(self, model: str, messages: list[dict[str, str]], **model_params):
        """
        Stream completion using specified model and chat messages.

        Args:
            model: The name of the model to use
            messages: List of message dictionaries with 'role' and 'content' keys
            **model_params: Additional parameters to pass to the model
        """
        port = self.ensure_model(model)

        response = litellm.completion(
            model=model,
            messages=messages,
            base_url=f"http://localhost:{port}/v1/",
            api_key="klasdhfghsjfgl",
            stream=True,
            **model_params,
        )
        return response

    @override(check_signature=False)
    def dispose(self) -> None:
        """Dispose of all running InferX processes"""
        for model, (process, _) in self._model_processes.items():
            if self._destroy_inference(process):
                LOGGER.info(f"Stopped inference server for model {model}")
        self._model_processes.clear()

    @override(check_signature=False)
    def __enter__(self):
        return self

    @override(check_signature=False)
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.dispose()

    def __del__(self):
        """Destructor to ensure cleanup"""
        self.dispose()
