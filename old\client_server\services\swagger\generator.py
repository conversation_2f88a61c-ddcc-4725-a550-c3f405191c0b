#!/usr/bin/env python3
import json
import os
import yaml
import requests
from pathlib import Path
from urllib.parse import urlparse

from .resolver import SwaggerSpecRefsResolver


class SwaggerSpecGenerator:
    """
    Class responsible for loading OpenAPI specifications from different sources
    and generating individual endpoint specifications.
    """

    def __init__(self):
        """
        Initialize the SwaggerSpecGenerator.

        Args:
            target_dir (str, optional): The target directory for storing generated files
        """
        self.resolver = SwaggerSpecRefsResolver()

    def generate_from_content(self, swagger_dict, base_uri=None):
        """
        Load an OpenAPI specification from a dictionary and resolve references.

        Args:
            swagger_dict (dict): The OpenAPI specification as a dictionary
            base_uri (str, optional): The base URI for resolving references

        Returns:
            list: A list of dictionaries containing endpoint specifications

        Raises:
            Exception: If there's an error processing the content
        """
        try:
            # Resolve references
            resolved_spec = self.resolver.resolve_references(swagger_dict, base_uri)

            # Generate endpoint specifications
            return self.resolver.generate_endpoint_specs(resolved_spec)
        except Exception as e:
            raise Exception(f"Error processing content: {str(e)}")

    def generate_from_file(self, file_path, base_uri=None):
        """
        Load an OpenAPI specification from a file and resolve references.

        Args:
            file_path (str): Path to the specification file

        Returns:
            list: A list of dictionaries containing endpoint specifications

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file format is invalid
            Exception: If there's an error processing the file
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Specification file not found: {file_path}")

        file_extension = Path(file_path).suffix.lower()

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                content = file.read()

            # Parse content based on file extension
            if file_extension in [".yaml", ".yml"]:
                try:
                    swagger_dict = yaml.safe_load(content)
                except yaml.YAMLError:
                    raise ValueError(f"Invalid YAML format in file: {file_path}")
            else:
                try:
                    swagger_dict = json.loads(content)
                except json.JSONDecodeError:
                    raise ValueError(f"Invalid JSON format in file: {file_path}")

            # Get the absolute directory path for resolving references
            base_uri = base_uri or f"file://{os.path.abspath(os.path.dirname(file_path))}/"

            # Process the content
            return self.generate_from_content(swagger_dict, base_uri)
        except Exception as e:
            raise Exception(f"Error loading specification file: {str(e)}")

    def generate_from_url(self, url):
        """
        Load an OpenAPI specification from a URL and resolve references.

        Args:
            url (str): URL to the specification file

        Returns:
            list: A list of dictionaries containing endpoint specifications

        Raises:
            ValueError: If the URL is invalid or the content format is invalid
            Exception: If there's an error fetching or processing the content
        """
        try:
            # Validate URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError(f"Invalid URL: {url}")

            # Fetch content from URL
            response = requests.get(url, timeout=30)
            response.raise_for_status()  # Raise exception for HTTP errors
            content = response.text

            # Parse content based on URL extension or Content-Type header
            is_yaml = False

            # Check URL extension
            if url.lower().endswith((".yaml", ".yml")):
                is_yaml = True
            # Check Content-Type header
            elif (
                "application/yaml" in response.headers.get("Content-Type", "").lower()
                or "text/yaml" in response.headers.get("Content-Type", "").lower()
            ):
                is_yaml = True

            # Parse content into a dictionary
            if is_yaml:
                try:
                    swagger_dict = yaml.safe_load(content)
                except yaml.YAMLError:
                    raise ValueError(f"Invalid YAML format from URL: {url}")
            else:
                try:
                    swagger_dict = json.loads(content)
                except json.JSONDecodeError:
                    raise ValueError(f"Invalid JSON format from URL: {url}")

            # Use the URL as the base URI for resolving references
            base_uri = f"{parsed_url.scheme}://{parsed_url.netloc}{os.path.dirname(parsed_url.path)}/"

            # Process the content
            return self.generate_from_content(swagger_dict, base_uri)
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error fetching specification from URL: {str(e)}")
        except Exception as e:
            raise Exception(f"Error processing specification from URL: {str(e)}")
