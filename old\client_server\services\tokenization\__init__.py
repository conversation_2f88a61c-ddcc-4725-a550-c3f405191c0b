from abc import ABC, abstractmethod
from typing import List


class ITokenizationBackend(ABC):
    @abstractmethod
    def count_tokens(self, text: str, model: str = None) -> int:
        """Count the number of tokens in a text string."""
        pass

    @abstractmethod
    def tokenize(self, text: str, model: str = None) -> List[int]:
        """Convert a string to tokens."""
        pass

    @abstractmethod
    def detokenize(self, tokens: List[int], model: str = None) -> str:
        """Convert tokens back to a string."""
        pass

    @abstractmethod
    def truncate_to_token_limit(
        self, text: str, max_tokens: int, model: str = None
    ) -> str:
        """Truncate text to stay within a maximum token limit."""
        pass
