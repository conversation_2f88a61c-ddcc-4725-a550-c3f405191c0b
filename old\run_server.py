"""
Dual Server Runner
==================

This script coordinates and runs both the HTTP server (for REST API and chat) 
and the WebSocket server (for real-time features) simultaneously.

Architecture:
- HTTP Server (port 45213): Handles REST API endpoints and chat functionality
- WebSocket Server (port 45214): Handles real-time features (notifications, file progress, monitoring, data streaming)

Usage:
    python run_server.py

The script will start both servers concurrently and handle graceful shutdown.
"""

import asyncio
import logging
import signal
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Optional

import uvicorn

# Import our servers
from server import app as http_app  # HTTP server with chat functionality
from websocket_server import WebSocketServer


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DualServerManager:
    """
    Manages both HTTP and WebSocket servers, ensuring they start and stop together.
    """
    
    def __init__(self, http_port: int = 45213, websocket_port: int = 45214):
        self.http_port = http_port
        self.websocket_port = websocket_port
        self.http_server: Optional[uvicorn.Server] = None
        self.websocket_server: Optional[WebSocketServer] = None
        self.shutdown_event = asyncio.Event()
        
    async def start_http_server(self):
        """Start the HTTP server (FastAPI with chat functionality)."""
        try:
            logger.info(f"🚀 Starting HTTP server on port {self.http_port}")
            
            config = uvicorn.Config(
                http_app,
                host="127.0.0.1",
                port=self.http_port,
                log_level="info",
                access_log=False,  # Reduce noise
                proxy_headers=False
            )
            
            self.http_server = uvicorn.Server(config)
            await self.http_server.serve()
            
        except Exception as e:
            logger.error(f"❌ Error starting HTTP server: {e}")
            self.shutdown_event.set()
            raise
    
    async def start_websocket_server(self):
        """Start the WebSocket server (for real-time features)."""
        try:
            logger.info(f"🔌 Starting WebSocket server on port {self.websocket_port}")
            
            self.websocket_server = WebSocketServer(
                host="127.0.0.1", 
                port=self.websocket_port
            )
            
            # Add some example custom handlers
            await self._setup_custom_websocket_handlers()
            
            await self.websocket_server.start_server()
            
        except Exception as e:
            logger.error(f"❌ Error starting WebSocket server: {e}")
            self.shutdown_event.set()
            raise
    
    async def _setup_custom_websocket_handlers(self):
        """Setup additional custom WebSocket handlers for demonstration."""
        
        async def handle_server_info(websocket, data, client_id):
            """Provide information about the dual-server setup."""
            info = {
                "type": "server_info_response",
                "data": {
                    "architecture": "dual-server",
                    "http_server": {
                        "port": self.http_port,
                        "purpose": "REST API and chat functionality",
                        "endpoints": ["/chat/stream"]
                    },
                    "websocket_server": {
                        "port": self.websocket_port,
                        "purpose": "Real-time features (notifications, file progress, monitoring)",
                        "available_events": list(self.websocket_server.router.handlers.keys())
                    },
                    "note": "Chat functionality is handled by HTTP server, not WebSocket server"
                },
                "timestamp": time.time()
            }
            await websocket.send(json.dumps(info))
        
        async def handle_ping(websocket, data, client_id):
            """Simple ping-pong handler for connection testing."""
            pong_response = {
                "type": "pong",
                "data": {
                    "message": "pong",
                    "client_id": client_id,
                    "server_time": time.time()
                },
                "timestamp": time.time()
            }
            await websocket.send(json.dumps(pong_response))
        
        # Register the custom handlers
        self.websocket_server.register_handler("server_info", handle_server_info)
        self.websocket_server.register_handler("ping", handle_ping)
    
    async def run_servers(self):
        """Run both servers concurrently."""
        logger.info("🚀 Starting dual-server architecture...")
        logger.info(f"📡 HTTP REST API & Chat: http://127.0.0.1:{self.http_port}")
        logger.info(f"🔌 WebSocket Real-time Features: ws://127.0.0.1:{self.websocket_port}")
        logger.info("=" * 60)
        
        try:
            # Start both servers concurrently
            await asyncio.gather(
                self.start_http_server(),
                self.start_websocket_server(),
                return_exceptions=True
            )
        except KeyboardInterrupt:
            logger.info("🛑 Received shutdown signal")
        except Exception as e:
            logger.error(f"❌ Error in server execution: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown both servers."""
        logger.info("🛑 Shutting down servers...")
        
        # Shutdown HTTP server
        if self.http_server:
            logger.info("Stopping HTTP server...")
            self.http_server.should_exit = True
        
        # WebSocket server will be stopped when the event loop ends
        logger.info("✅ Servers stopped")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}")
            # Create new event loop for shutdown if needed
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.shutdown())
                else:
                    asyncio.run(self.shutdown())
            except RuntimeError:
                # If no event loop is running, just exit
                sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


def print_startup_info():
    """Print startup information and usage examples."""
    print("\n" + "=" * 60)
    print("🚀 CodeMate Dual-Server Architecture")
    print("=" * 60)
    print(f"📡 HTTP Server (REST API & Chat): http://127.0.0.1:45213")
    print(f"🔌 WebSocket Server (Real-time): ws://127.0.0.1:45214")
    print("\n📋 Available Services:")
    print("   • HTTP: Chat streaming, REST API endpoints")
    print("   • WebSocket: Notifications, file progress, system monitoring")
    print("\n🔧 Example Usage:")
    print("   • Chat: POST http://127.0.0.1:45213/chat/stream")
    print("   • WebSocket: Connect to ws://127.0.0.1:45214")
    print("\n💡 WebSocket Events:")
    print("   • system_status - Get server status")
    print("   • file_progress_request - Monitor file operations")
    print("   • notification_subscribe - Subscribe to notifications")
    print("   • ping - Test connection")
    print("   • server_info - Get architecture information")
    print("\n🛑 Press Ctrl+C to stop both servers")
    print("=" * 60 + "\n")


async def main():
    """Main entry point for the dual-server setup."""
    print_startup_info()
    
    # Create and configure the server manager
    server_manager = DualServerManager()
    server_manager.setup_signal_handlers()
    
    try:
        # Run both servers
        await server_manager.run_servers()
    except KeyboardInterrupt:
        logger.info("🛑 Shutdown requested by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        # Import json here since it's used in the custom handlers
        import json
        
        # Run the dual server setup
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Shutdown complete")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Failed to start servers: {e}")
        sys.exit(1)
