# Cloud Synchronization Feature

## Overview

The cloud synchronization feature enables incremental synchronization of knowledge bases to the cloud, sending only modified chunks based on file timestamps. This optimizes bandwidth usage and reduces sync time by avoiding unnecessary data transfer.

## Key Features

### 1. Incremental Synchronization
- **File Timestamp Tracking**: Uses `metadata.file_timestamps` to track when files were last modified
- **Smart Filtering**: Only syncs chunks from files modified since `syncConfig.lastSynced`
- **Buffer Time**: Includes a 5-second buffer to avoid edge cases with timestamp precision
- **Efficient Updates**: Sends only modified chunks instead of entire knowledge base

### 2. Socket Event Handling
- **Event Name**: `sync_to_cloud`
- **Progress Updates**: Real-time progress reporting via `sync_to_cloud:progress`
- **Success Response**: Detailed sync results via `sync_to_cloud:success`
- **Error Handling**: Comprehensive error reporting via `sync_to_cloud:error`

### 3. Cloud API Integration
- **Endpoint**: `POST /knowledge/update`
- **Authentication**: Uses session-based authentication via `x-session` header
- **Payload Schema**: Follows exact cloud API requirements
- **Timestamp Updates**: Updates `syncConfig.lastSynced` after successful sync

## Implementation Details

### Socket Event Handler: `handle_sync_to_cloud_event`

**Location**: `client_server/api/events/upload.py`

**Input Schema**:
```python
class CloudSyncData(BaseModel):
    kb_id: str              # Knowledge base ID to sync
    request_id: str         # Request tracking ID
    session: Optional[str]  # Authentication session
```

**Process Flow**:
1. **Validation**: Validates KB exists, is local, and has cloud_id
2. **Chunk Retrieval**: Gets all chunks from the knowledge base
3. **Incremental Filtering**: Filters chunks based on file timestamps
4. **Payload Preparation**: Prepares update payload with exact cloud schema
5. **Cloud Request**: Sends POST request to `/knowledge/update`
6. **Timestamp Update**: Updates `lastSynced` after successful sync

### Helper Functions

#### `_validate_kb_for_cloud_sync(kb_id: str)`
Validates that a knowledge base can be synced:
- Checks KB exists
- Ensures KB is local (not remote)
- Verifies KB has cloud_id (required for sync)

#### `_get_modified_chunks_for_sync(kb, all_chunks, buffer_time_ms=5000)`
Filters chunks to only include those from modified files:
- Compares file timestamps against `syncConfig.lastSynced`
- Uses 5-second buffer to avoid edge cases
- Returns only chunks from files modified since last sync

### Cloud API Payload Schema

```json
{
  "collection_id": "CLOUD_COLLECTION_ID",
  "update_queries": {
    "vectors": [[...], ...],     // vectors for modified chunks only
    "metadata": [...],           // metadata for modified chunks only  
    "ids": [...]                // IDs of chunks to add/update
  },
  "timestamp": 1718145330        // current timestamp for lastSynced update
}
```

## Usage Examples

### Socket.IO Client Usage

```javascript
// Connect to WebSocket server
const socket = io('http://localhost:45214');

// Set up event handlers
socket.on('sync_to_cloud:progress', (data) => {
    console.log(`Progress: ${data.progress}% - ${data.message}`);
});

socket.on('sync_to_cloud:success', (data) => {
    console.log('Sync successful!', data);
});

socket.on('sync_to_cloud:error', (data) => {
    console.error('Sync failed:', data.message);
});

// Trigger sync
socket.emit('sync_to_cloud', {
    kb_id: 'your-kb-id',
    request_id: 'unique-request-id',
    session: 'your-session-token'
});
```

### Python Test Script

Use the provided `test_sync_to_cloud.py` script:

```bash
python test_sync_to_cloud.py
```

This script will:
1. List available knowledge bases
2. Find syncable KBs (local with cloud_id)
3. Test sync functionality with the first available KB
4. Show real-time progress and results

## Server Registration

The sync event handler is registered in `client_server/cmd/server.py`:

```python
from client_server.api.events.upload import handle_sync_to_cloud_event

# Register socket event
sio.on("sync_to_cloud", handler=make_handler(handle_sync_to_cloud_event))
```

## Error Handling

### Validation Errors
- **KB Not Found**: Returns error if knowledge base doesn't exist
- **Remote KB**: Cannot sync remote KBs (already in cloud)
- **No Cloud ID**: KB must be uploaded to cloud first before syncing
- **Missing Session**: Authentication session is required

### Sync Errors
- **No Modified Files**: Returns success with `sync_skipped: true`
- **Chunk Retrieval Failed**: Error retrieving chunks from local storage
- **Cloud API Error**: HTTP errors from cloud endpoint
- **Network Issues**: Connection timeouts and network failures

### Progress Events

1. **10%**: "Retrieving knowledge base chunks"
2. **30%**: "Analyzing modified files for incremental sync"
3. **50%**: "Preparing incremental update payload"
4. **70%**: "Syncing modified chunks to cloud"
5. **90%**: "Finalizing sync"
6. **100%**: "Sync complete"

## Benefits

### Performance Optimization
- **Reduced Bandwidth**: Only modified chunks are transmitted
- **Faster Sync**: Smaller payloads mean quicker sync times
- **Efficient Storage**: Cloud storage only updated with changes

### User Experience
- **Real-time Progress**: Users see detailed progress updates
- **Smart Detection**: Automatically detects when sync is unnecessary
- **Error Recovery**: Comprehensive error handling and reporting

### System Reliability
- **Timestamp Tracking**: Robust file modification detection
- **Buffer Time**: Prevents edge cases with timestamp precision
- **Validation**: Multiple validation layers prevent invalid operations

## Testing

### Prerequisites
1. Local knowledge base with cloud_id (uploaded to cloud)
2. Sync enabled in knowledge base configuration
3. Valid authentication session
4. Running WebSocket server on port 45214

### Test Scenarios
1. **Normal Sync**: Sync KB with modified files
2. **No Changes**: Sync KB with no modifications (should skip)
3. **Large KB**: Test with knowledge base containing many chunks
4. **Network Issues**: Test error handling with network problems
5. **Invalid KB**: Test validation with non-existent or invalid KBs

### Monitoring
- Check server logs for detailed sync information
- Monitor network traffic to verify only modified chunks are sent
- Verify `lastSynced` timestamp is updated after successful sync
- Confirm cloud storage reflects the incremental updates

## Future Enhancements

### Potential Improvements
1. **Batch Processing**: Process large KBs in smaller batches
2. **Compression**: Compress payload for better network efficiency
3. **Retry Logic**: Automatic retry with exponential backoff
4. **Conflict Resolution**: Handle concurrent modifications
5. **Delta Sync**: Even more granular change detection

### Integration Opportunities
1. **File Watchers**: Automatic sync when files change
2. **Scheduled Sync**: Periodic background synchronization
3. **Multi-KB Sync**: Sync multiple knowledge bases simultaneously
4. **Sync Status UI**: Visual sync status in user interface
