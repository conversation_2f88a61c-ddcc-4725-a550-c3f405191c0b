import time
import psutil
from fastapi import HTTPException

from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_system_info
from . import route


@route("GET", "/hardware_stats")
async def hardware_stats():
    """
    Get hardware statistics including memory usage
    """
    start_time = time.time()
    LOGGER.info("Processing hardware stats request")

    try:
        # Log system information first
        system_info = log_system_info()

        # Get memory statistics
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        disk = psutil.disk_usage("/")

        # Calculate values and check if there's enough memory
        total_memory = memory.total
        free_memory = memory.available
        memory_usage_percent = memory.percent
        available_memory = memory.available

        # Log detailed system metrics
        LOGGER.debug(
            f"System metrics - CPU: {cpu_percent}%, "
            f"Memory: {memory_usage_percent}%, "
            f"Disk: {disk.percent}% used"
        )

        # Minimum 4GB available memory check
        required_memory = 4 * 1024 * 1024 * 1024  # 4GB in bytes
        allowed = available_memory >= required_memory

        if not allowed:
            LOGGER.warning(
                f"Insufficient memory available: {available_memory/1024/1024/1024:.2f}GB "
                f"(required: {required_memory/1024/1024/1024:.2f}GB)"
            )

        response = {
            "total_memory": total_memory,
            "free_memory": free_memory,
            "memory_usage_percent": memory_usage_percent,
            "available_memory": available_memory,
            "allowed": allowed,
            "cpu_percent": cpu_percent,
            "disk_usage_percent": disk.percent,
        }

        total_time = time.time() - start_time
        LOGGER.info(f"Hardware stats request completed in {total_time:.3f}s")

        return response

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error fetching hardware stats after {total_time:.3f}s: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching hardware stats: {str(e)}"
        )
