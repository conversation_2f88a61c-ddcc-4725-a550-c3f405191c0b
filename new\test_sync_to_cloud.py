#!/usr/bin/env python3
"""
Test script for the sync_to_cloud functionality.

This script demonstrates how to use the new sync_to_cloud socket event
to perform incremental synchronization of knowledge bases to the cloud.
"""

import asyncio
import json
import time

import socketio

# Configuration
SESSION_ID = "d4d38050-b68f-4ebd-83d5-09580b08afc9"
WEBSOCKET_URL = "http://localhost:45214"  # WebSocket port


class CloudSyncTester:
    def __init__(self):
        self.sio = socketio.AsyncClient()
        self.setup_event_handlers()
        
    def setup_event_handlers(self):
        """Set up event handlers for sync responses."""
        
        @self.sio.event
        async def connect():
            print("✅ Connected to server")
            
        @self.sio.event
        async def disconnect():
            print("❌ Disconnected from server")
            
        @self.sio.on("sync_to_cloud:progress")
        async def on_sync_progress(data):
            print(f"🔄 Sync Progress: {data.get('progress', 0)}% - {data.get('message', '')}")
            
        @self.sio.on("sync_to_cloud:success")
        async def on_sync_success(data):
            print("✅ Sync Success!")
            print(f"   Request ID: {data.get('request_id')}")
            print(f"   Message: {data.get('message')}")
            
            sync_data = data.get('data', {})
            if sync_data:
                print(f"   Total Chunks: {sync_data.get('total_chunks', 'N/A')}")
                print(f"   Modified Chunks: {sync_data.get('modified_chunks', 'N/A')}")
                print(f"   Sync Time: {sync_data.get('sync_time_seconds', 'N/A')}s")
                print(f"   Last Synced: {sync_data.get('last_synced', 'N/A')}")
                
                if sync_data.get('sync_skipped'):
                    print("   ℹ️  Sync was skipped - no changes detected")
            
        @self.sio.on("sync_to_cloud:error")
        async def on_sync_error(data):
            print("❌ Sync Error!")
            print(f"   Request ID: {data.get('request_id')}")
            print(f"   Message: {data.get('message')}")
            
    async def connect(self):
        """Connect to the WebSocket server."""
        try:
            await self.sio.connect(WEBSOCKET_URL)
            return True
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            return False
            
    async def disconnect(self):
        """Disconnect from the WebSocket server."""
        await self.sio.disconnect()
        
    async def sync_kb_to_cloud(self, kb_id: str) -> bool:
        """
        Sync a knowledge base to cloud using incremental sync.
        
        Args:
            kb_id: The knowledge base ID to sync
            
        Returns:
            True if sync was successful, False otherwise
        """
        request_id = f"sync_test_{int(time.time())}"
        
        sync_data = {
            "kb_id": kb_id,
            "request_id": request_id,
            "session": SESSION_ID
        }
        
        print(f"🚀 Starting sync for KB: {kb_id}")
        print(f"   Request ID: {request_id}")
        print(f"   Session: {SESSION_ID[:8]}...{SESSION_ID[-8:]}")
        
        try:
            # Send sync request
            await self.sio.emit("sync_to_cloud", sync_data)
            
            # Wait for completion (with timeout)
            await asyncio.sleep(30)  # Wait up to 30 seconds
            return True
            
        except Exception as e:
            print(f"❌ Error during sync: {e}")
            return False


def get_kb_id_from_user() -> str:
    """Get knowledge base ID from user input."""
    print("Enter the Knowledge Base ID to sync:")
    kb_id = input("> ").strip()

    if not kb_id:
        print("❌ KB ID is required!")
        return ""

    return kb_id


async def main():
    """Main test function."""
    print("=" * 80)
    print("🧪 CLOUD SYNC FUNCTIONALITY TEST")
    print("=" * 80)
    print(f"Session ID: {SESSION_ID[:8]}...{SESSION_ID[-8:]}")
    print(f"WebSocket URL: {WEBSOCKET_URL}")
    print("=" * 80)
    print()

    # Get KB ID from user
    kb_id = get_kb_id_from_user()

    if not kb_id:
        return 1

    # Create tester and connect
    tester = CloudSyncTester()

    if not await tester.connect():
        return 1

    try:
        print(f"\n🧪 Testing sync for KB ID: {kb_id}")

        success = await tester.sync_kb_to_cloud(kb_id)

        if success:
            print("\n✅ Sync test completed successfully!")
        else:
            print("\n❌ Sync test failed!")

    finally:
        await tester.disconnect()

    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        exit(1)
