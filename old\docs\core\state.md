# State Management

The `core` module provides a thread-safe mechanism for managing global state, which prevents race conditions in a multi-threaded environment.

## `ThreadSafeState` Class

The centerpiece of state management is the `ThreadSafeState` class, a generic class that encapsulates a value and protects access to it using a `threading.Lock`.

### Features

- **Type-Safe**: It is a generic class, so it can hold any type of data, and type hints can be used for static analysis.
- **Thread-Safe**: All `get` and `set` operations are protected by a lock, preventing concurrent access issues.
- **Named States**: Each instance is given a name for easier debugging and logging.

### Usage

```python
# Create a thread-safe state variable
my_state = ThreadSafeState[int]("MY_STATE", initial_value=0)

# Set the value
my_state.set(10)

# Get the value
value = my_state.get()
```

## Global State Variables

The application defines several global state variables in `client_server/core/state.py`:

- `G_SESSION_ID: ThreadSafeState[str]`: Tracks the current session ID provided by the VSCode extension.
- `G_EXTENSION_VERSION: ThreadSafeState[str]`: Stores the version of the VSCode extension.
- `G_CLIENT_SERVER_VERSION: ThreadSafeState[str]`: Stores the version of the Client Server itself.
- `G_BASE_URL: ThreadSafeState[BaseURL]`: Holds the base URLs for various backend services. The `BaseURL` is a Pydantic model defined in `client_server/core/state_types.py` that structures the different API endpoints.
- `G_CANCELLED_REQUEST_IDS: ThreadSafeState[set[str]]`: Maintains a set of request IDs that have been cancelled by the user, allowing long-running tasks to be gracefully terminated.
