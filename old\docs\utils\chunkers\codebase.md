# Codebase Chunker (`codebase.py`)

The `codebase.py` module contains the `CodebaseChunker`, which is an implementation of the `IChunker` interface designed to process a local codebase. It takes a path to a directory, analyzes the files within it, and splits them into chunks.

## Classes

### `CodebaseChunker(IChunker)`

This class handles the entire process of chunking a local codebase.

#### `__init__(self, metadata: QdrantCodebaseMetadata)`

- **Parameters:**
  - `metadata` (`QdrantCodebaseMetadata`): An object that contains information about the codebase, including the list of files to be processed (`metadata.files`).

#### `process(self, progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

This method orchestrates the chunking process. It calls the internal `_make_chunks` method to perform the work.

#### `_make_chunks(self, progress_callback: ... ) -> list[QdrantKnowledgeBaseChunk]`

This is the core method where the chunking happens.

- **Logging:** It logs detailed statistics about the codebase, including the number of files, the distribution of file types by extension, and the total size.
- **Parallel Processing:** It uses a `ThreadPoolExecutor` to process multiple files in parallel, significantly speeding up the chunking of large codebases. It dispatches a `make_qdrant_knowledgebase_chunks_from_file` task (from `chunkers.utils`) for each file.
- **Progress Reporting:** As files are completed, it calculates the overall progress and calls the `progress_callback` function to provide real-time feedback.
- **Error Handling:** It includes `try...except` blocks to catch and log any errors that occur during the file processing.
- **Returns:**
  - A flattened list of `QdrantKnowledgeBaseChunk` objects from all the processed files.
